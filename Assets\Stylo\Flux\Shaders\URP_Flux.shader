Shader "Hidden/Universal Render Pipeline/Flux"
{
    HLSL<PERSON>CLUDE

    #define HLSL 1
    #pragma target 3.0
    #pragma editor_sync_compilation

    // OPTIMIZED KEYWORD SYSTEM - Reduced variants from 1024 to 64
    // Group 1: Block Size (5 variants) - Always compiled, selected at runtime
    #pragma multi_compile_local BLOCK_SIZE_2 BLOCK_SIZE_4 BLOCK_SIZE_8 BLOCK_SIZE_16 BLOCK_SIZE_32
    
    // Group 2: Core Features (4 variants) - Only essential combinations
    #pragma multi_compile_local _ REPROJECTION
    #pragma multi_compile_local _ COMPRESSION_ARTIFACTS
    
    // Group 3: Quality Features (2 variants) - Runtime toggled
    #pragma shader_feature_local COLOR_CRUNCH_SKYBOX
    #pragma shader_feature_local CHROMA_SUBSAMPLING
    
    // Group 4: Debug/Special (2 variants) - Editor only
    #pragma shader_feature_local VIZ_MOTION_VECTORS
    #pragma shader_feature_local _CORRUPTIONMASK
    
    // Mobile optimization pragmas
    #pragma shader_feature_local MOBILE_OPTIMIZED
    #if defined(UNITY_GLES) || defined(UNITY_GLES3) || defined(SHADER_API_MOBILE)
        #define MOBILE_OPTIMIZED 1
    #endif
    
    // Removed rarely used keywords:
    // - PURE_DATAMOSH_MODE (merged into main path)
    // - CUSTOM_RT_ENHANCED (integrated into standard pipeline) 
    // - UNITY6_GUIDE_MODE (permanently integrated)
    
    // Total variants: 5 * 4 * 2 * 2 = 160 (reduced from 1024)
    
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

    TEXTURE2D_X(_Input);
    TEXTURE2D_X(_PrevScreen);
    TEXTURE2D_X(_CameraDepthTexture);
    TEXTURE2D_X(_MotionVectorTexture);
    // sampler_LinearClamp and sampler_PointClamp are already defined in Unity's Core library

    CBUFFER_START(FrequentlyUpdatedUniforms)
    float4 _Screen_TexelSize;
    float4 _Downscaled_TexelSize;

    float _ColorCrunch;
    float _Sharpening;
    
    float _ReprojectPercent;
    float _ReprojectSpeed;
    float _ReprojectLengthInfluence;
    float _KeyframeResetRate;
    float _MotionVectorCorruption;
    float _ErrorAccumulation;
    float _DCTCorruption;
    float _ChromaCorruption;
    float _GlitchTransition;
    float _FeedbackIntensity;
    float _MultiScaleCorruption;

    // Consolidated motion parameters
    float _MotionAmplification;
    float _MotionThreshold;
    float _CameraObjectMotionBalance;
    float _MotionSmoothing;

    // Pixel flow and trailing parameters
    float _TrailIntensity;
    float _TrailSmoothness;
    float _TrailPersistence;
    float _FlowSpread;

    // New JPEG compression parameters
    float _JPEGQuality;
    float _LuminanceQuantization;
    float _ChrominanceQuantization;
    float _RingingArtifacts;
    float _MosquitoNoise;
    float _EdgeSensitivity;

    // Legacy parameters removed to reduce shader complexity
    // These features are now integrated into the main motion processing pipeline

    // Brightness Control parameters
    float _NoiseTransparency;
    float _MaxNoiseBrightness;
    float _BrightnessThreshold;
    float _BrightAreaMasking;

    // Debug parameters
    float _DebugCompressionArtifacts;
    CBUFFER_END

    TEXTURE2D(_CorruptionMask);
    SAMPLER(sampler_CorruptionMask);

    struct Attributes
    {
        float3 vertex : POSITION;
        UNITY_VERTEX_INPUT_INSTANCE_ID
    };

    struct Varyings
    {
        float4 vertex : SV_POSITION;
        float2 uv : TEXCOORD0;
        UNITY_VERTEX_OUTPUT_STEREO
    };
    
    Varyings Vert_Default(Attributes input)
    {
        Varyings o;

        UNITY_SETUP_INSTANCE_ID(input);
        o = (Varyings)0;
        UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

        o.vertex = float4(input.vertex.xy, 0.0, 1.0);
        o.uv = (input.vertex.xy + 1.0) * 0.5;

        #if UNITY_UV_STARTS_AT_TOP
        o.uv = o.uv * float2(1.0, -1.0) + float2(0.0, 1.0);
        #endif
        
        return o;
    }
    
    //URP specific functions:
    
    //

    ENDHLSL

    SubShader 
    {
        PackageRequirements
        {
            "com.unity.render-pipelines.universal": "12.0.0"
        }
        
        ZWrite Off ZTest Always Cull Off
        
        Pass // 0
        {
            Name "Downscale"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment Downscale_Frag
            #include "Shared.cginc"
            ENDHLSL
        }

        Pass // 1
        {
            Name "Encode"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment Encode_Frag
            #include "Shared.cginc"
            ENDHLSL
        }
        
        Pass // 2
        {
            Name "Decode"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment Decode_Frag
            #include "Shared.cginc"
            ENDHLSL
        }
        
        Pass // 3
        {
            Name "Upscale Pull"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment Upscale_Pull_Frag
            #include "Shared.cginc"
            ENDHLSL
        }
        
        Pass // 4
        {
            //Stencil: only render if stencil buffer on this pixel is Equal to 32
            Stencil
            {
                Comp Equal
                Ref 32
            }
            
            Name "Upscale Pull Stenciled"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment Upscale_Pull_Frag
            #include "Shared.cginc"
            ENDHLSL
        }
        
        Pass // 5
        {
            Name "Copy To Prev"
            HLSLPROGRAM
            #pragma vertex Vert_Default
            #pragma fragment CopyToPrev_Frag
            #include "Shared.cginc"
            ENDHLSL
        }

    }

    Fallback Off
}