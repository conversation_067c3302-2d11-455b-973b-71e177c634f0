using System;
using System.Collections.Generic;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// Internal class for managing event callbacks and triggering.
    /// Compatible with Koreographer's EventObj.
    /// </summary>
    internal class EventObj
    {
        private string eventID;
        private List<Cadance.CadanceEventCallback> callbacks = new List<Cadance.CadanceEventCallback>();
        private List<Cadance.CadanceEventCallbackWithTime> callbacksWithTime = new List<Cadance.CadanceEventCallbackWithTime>();

        public EventObj(string eventID)
        {
            this.eventID = eventID;
        }

        public void RegisterCallback(Cadance.CadanceEventCallback callback)
        {
            if (callback != null && !callbacks.Contains(callback))
            {
                callbacks.Add(callback);
            }
        }

        public void RegisterCallbackWithTime(Cadance.CadanceEventCallbackWithTime callback)
        {
            if (callback != null && !callbacksWithTime.Contains(callback))
            {
                callbacksWithTime.Add(callback);
            }
        }

        public void UnregisterCallback(Cadance.CadanceEventCallback callback)
        {
            callbacks.Remove(callback);
        }

        public void UnregisterCallbackWithTime(Cadance.CadanceEventCallbackWithTime callback)
        {
            callbacksWithTime.Remove(callback);
        }

        public void UnregisterAllCallbacksForObject(object obj)
        {
            // Remove callbacks that belong to the specified object
            for (int i = callbacks.Count - 1; i >= 0; i--)
            {
                if (callbacks[i].Target == obj)
                {
                    callbacks.RemoveAt(i);
                }
            }

            for (int i = callbacksWithTime.Count - 1; i >= 0; i--)
            {
                if (callbacksWithTime[i].Target == obj)
                {
                    callbacksWithTime.RemoveAt(i);
                }
            }
        }

        public void TriggerEvent(CadanceEvent evt, int sampleTime, int sampleTimeDelta, DeltaSlice deltaSlice)
        {
            Debug.Log($"[EventObj] *** TRIGGERING EVENT *** '{eventID}' with {callbacks.Count} standard callbacks and {callbacksWithTime.Count} timed callbacks");

            // Trigger standard callbacks
            foreach (var callback in callbacks)
            {
                try
                {
                    Debug.Log($"[EventObj] *** CALLING CALLBACK *** Invoking standard callback for event '{eventID}'");
                    callback(evt);
                    Debug.Log($"[EventObj] *** CALLBACK COMPLETED *** Standard callback for event '{eventID}' completed successfully");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Cadance] Error in event callback for '{eventID}': {ex.Message}");
                }
            }

            // Trigger callbacks with timing information
            foreach (var callback in callbacksWithTime)
            {
                try
                {
                    Debug.Log($"[EventObj] *** CALLING TIMED CALLBACK *** Invoking timed callback for event '{eventID}'");
                    callback(evt, sampleTime, sampleTimeDelta, deltaSlice);
                    Debug.Log($"[EventObj] *** TIMED CALLBACK COMPLETED *** Timed callback for event '{eventID}' completed successfully");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Cadance] Error in timed event callback for '{eventID}': {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cleans up callbacks that reference destroyed Unity objects.
        /// This prevents memory leaks and null reference exceptions.
        /// </summary>
        public void CleanupDestroyedObjects()
        {
            // Clean up standard callbacks
            for (int i = callbacks.Count - 1; i >= 0; i--)
            {
                var callback = callbacks[i];
                if (callback.Target is UnityEngine.Object unityObj && unityObj == null)
                {
                    callbacks.RemoveAt(i);
                    Debug.Log($"[Cadance] Removed destroyed object callback from event '{eventID}'");
                }
            }

            // Clean up timed callbacks
            for (int i = callbacksWithTime.Count - 1; i >= 0; i--)
            {
                var callback = callbacksWithTime[i];
                if (callback.Target is UnityEngine.Object unityObj && unityObj == null)
                {
                    callbacksWithTime.RemoveAt(i);
                    Debug.Log($"[Cadance] Removed destroyed object timed callback from event '{eventID}'");
                }
            }
        }

        /// <summary>
        /// Gets the total number of registered callbacks.
        /// </summary>
        public int GetCallbackCount()
        {
            return callbacks.Count + callbacksWithTime.Count;
        }

        /// <summary>
        /// Checks if this EventObj has any active callbacks.
        /// </summary>
        public bool HasActiveCallbacks()
        {
            return callbacks.Count > 0 || callbacksWithTime.Count > 0;
        }
    }
}
