#ifdef SHADER_STAGE_COMPUTE
    // For compute shaders, use direct texture sampling
    #define SAMPLE(texture, sampler, uv) texture.Sample(sampler, uv)
#elif HLSL
    #define SAMPLE(texture, sampler, uv) SAMPLE_TEXTURE2D_X(texture, sampler, uv)
#else
    #define SAMP<PERSON>(texture, sampler, uv) UNITY_SAMPLE_SCREENSPACE_TEXTURE(texture, uv)
#endif

// Variable handling - different for compute vs regular shaders
#ifdef SHADER_STAGE_COMPUTE
    // For compute shaders, use cbuffer fields (no underscores)
    #define FLUX_TIME Time
    #define FLUX_JPEG_QUALITY JPEGQuality
    #define FLUX_BLOCK_SIZE BlockSize
    #define FLUX_EFFECT_INTENSITY EffectIntensity
    #define FLUX_COLOR_CRUNCH ColorCrunch
    #define FLUX_DOWNSCALING Downscaling
    #define FLUX_REPROJECT_BASE_NOISE ReprojectBaseNoise
    #define FLUX_REPROJECT_LENGTH_INFLUENCE ReprojectLengthInfluence
    #define FLUX_KEYFRAME_RESET_RATE KeyframeResetRate
    #define FLUX_MOTION_VECTOR_CORRUPTION MotionVectorCorruption
    #define FLUX_ERROR_ACCUMULATION ErrorAccumulation
    #define FLUX_DCT_CORRUPTION DCTCorruption
    #define FLUX_CHROMA_CORRUPTION ChromaCorruption
    #define FLUX_MULTI_SCALE_CORRUPTION MultiScaleCorruption
    #define FLUX_CORRUPTION_INTENSITY CorruptionIntensity
    #define FLUX_MOTION_AMPLIFICATION MotionAmplification
    #define FLUX_MOTION_THRESHOLD MotionThreshold
    #define FLUX_CAMERA_OBJECT_MOTION_BALANCE CameraObjectMotionBalance
    #define FLUX_MOTION_SMOOTHING MotionSmoothing
    #define FLUX_TRAIL_INTENSITY TrailIntensity
    #define FLUX_TRAIL_SMOOTHNESS TrailSmoothness
    #define FLUX_TRAIL_PERSISTENCE TrailPersistence
    #define FLUX_FLOW_SPREAD FlowSpread
    #define FLUX_LUMINANCE_QUANTIZATION LuminanceQuantization
    #define FLUX_CHROMINANCE_QUANTIZATION ChrominanceQuantization
    #define FLUX_CHROMA_SUBSAMPLING ChromaSubsampling
    #define FLUX_RINGING_ARTIFACTS RingingArtifacts
    #define FLUX_MOSQUITO_NOISE MosquitoNoise
    #define FLUX_EDGE_SENSITIVITY EdgeSensitivity
    #define FLUX_TEMPORAL_BLEND TemporalBlend
    #define FLUX_BRIGHTNESS_MASK BrightnessMask
    #define FLUX_GAMMA Gamma
    #define FLUX_BRIGHTNESS Brightness
    #define FLUX_CONTRAST Contrast
    #define FLUX_NOISE_TRANSPARENCY NoiseTransparency
    #define FLUX_MAX_NOISE_BRIGHTNESS MaxNoiseBrightness
    #define FLUX_BRIGHTNESS_THRESHOLD BrightnessThreshold
    #define FLUX_BRIGHT_AREA_MASKING BrightAreaMasking
    #define FLUX_QUANTIZATION_LEVELS QuantizationLevels
    #define FLUX_COMPRESSION_INTENSITY CompressionIntensity
    #define FLUX_DOWNSCALED_TEXEL_SIZE DownscaledTexelSize
    #define FLUX_SCREEN_SIZE ScreenSize
    #define FLUX_SCREEN_TEXEL_SIZE ScreenTexelSize
    #define FLUX_FRAME_COUNT FrameCount
    // Texture references for compute shaders (use actual texture names)
    #define FLUX_INPUT _Input
    #define FLUX_PREV_SCREEN _PrevScreen
    #define FLUX_MOTION_VECTOR_TEXTURE _MotionVectorTexture
    #define FLUX_CAMERA_DEPTH_TEXTURE _CameraDepthTexture
    // Additional effect parameters for compute shaders
    #define FLUX_COLOR_CRUNCH ColorCrunch
    #define FLUX_SHARPENING Sharpening
    #define FLUX_REPROJECT_PERCENT ReprojectPercent
    #define FLUX_REPROJECT_SPEED ReprojectSpeed
    #define FLUX_GLITCH_TRANSITION GlitchTransition
    #define FLUX_FEEDBACK_INTENSITY FeedbackIntensity
    #define FLUX_DEBUG_COMPRESSION_ARTIFACTS DebugCompressionArtifacts
#else
    // For regular shaders, use underscore-prefixed variables
    #define FLUX_TIME _Time
    #define FLUX_JPEG_QUALITY _JPEGQuality
    #define FLUX_BLOCK_SIZE _BlockSize
    #define FLUX_EFFECT_INTENSITY _EffectIntensity
    #define FLUX_COLOR_CRUNCH _ColorCrunch
    #define FLUX_DOWNSCALING _Downscaling
    #define FLUX_REPROJECT_BASE_NOISE _ReprojectBaseNoise
    #define FLUX_REPROJECT_LENGTH_INFLUENCE _ReprojectLengthInfluence
    #define FLUX_KEYFRAME_RESET_RATE _KeyframeResetRate
    #define FLUX_MOTION_VECTOR_CORRUPTION _MotionVectorCorruption
    #define FLUX_ERROR_ACCUMULATION _ErrorAccumulation
    #define FLUX_DCT_CORRUPTION _DCTCorruption
    #define FLUX_CHROMA_CORRUPTION _ChromaCorruption
    #define FLUX_MULTI_SCALE_CORRUPTION _MultiScaleCorruption
    #define FLUX_CORRUPTION_INTENSITY _CorruptionIntensity
    #define FLUX_MOTION_AMPLIFICATION _MotionAmplification
    #define FLUX_MOTION_THRESHOLD _MotionThreshold
    #define FLUX_CAMERA_OBJECT_MOTION_BALANCE _CameraObjectMotionBalance
    #define FLUX_MOTION_SMOOTHING _MotionSmoothing
    #define FLUX_TRAIL_INTENSITY _TrailIntensity
    #define FLUX_TRAIL_SMOOTHNESS _TrailSmoothness
    #define FLUX_TRAIL_PERSISTENCE _TrailPersistence
    #define FLUX_FLOW_SPREAD _FlowSpread
    #define FLUX_LUMINANCE_QUANTIZATION _LuminanceQuantization
    #define FLUX_CHROMINANCE_QUANTIZATION _ChrominanceQuantization
    #define FLUX_CHROMA_SUBSAMPLING _ChromaSubsampling
    #define FLUX_RINGING_ARTIFACTS _RingingArtifacts
    #define FLUX_MOSQUITO_NOISE _MosquitoNoise
    #define FLUX_EDGE_SENSITIVITY _EdgeSensitivity
    #define FLUX_TEMPORAL_BLEND _TemporalBlend
    #define FLUX_BRIGHTNESS_MASK _BrightnessMask
    #define FLUX_GAMMA _Gamma
    #define FLUX_BRIGHTNESS _Brightness
    #define FLUX_CONTRAST _Contrast
    #define FLUX_NOISE_TRANSPARENCY _NoiseTransparency
    #define FLUX_MAX_NOISE_BRIGHTNESS _MaxNoiseBrightness
    #define FLUX_BRIGHTNESS_THRESHOLD _BrightnessThreshold
    #define FLUX_BRIGHT_AREA_MASKING _BrightAreaMasking
    #define FLUX_QUANTIZATION_LEVELS _QuantizationLevels
    #define FLUX_COMPRESSION_INTENSITY _CompressionIntensity
    #define FLUX_DOWNSCALED_TEXEL_SIZE _Downscaled_TexelSize
    #define FLUX_SCREEN_SIZE _ScreenSize
    #define FLUX_SCREEN_TEXEL_SIZE _Screen_TexelSize
    #define FLUX_FRAME_COUNT _FrameCount
    // Texture references for regular shaders
    #define FLUX_INPUT _Input
    #define FLUX_PREV_SCREEN _PrevScreen
    #define FLUX_MOTION_VECTOR_TEXTURE _MotionVectorTexture
    #define FLUX_CAMERA_DEPTH_TEXTURE _CameraDepthTexture
    // Additional effect parameters for regular shaders
    #define FLUX_COLOR_CRUNCH _ColorCrunch
    #define FLUX_SHARPENING _Sharpening
    #define FLUX_REPROJECT_PERCENT _ReprojectPercent
    #define FLUX_REPROJECT_SPEED _ReprojectSpeed
    #define FLUX_GLITCH_TRANSITION _GlitchTransition
    #define FLUX_FEEDBACK_INTENSITY _FeedbackIntensity
    #define FLUX_DEBUG_COMPRESSION_ARTIFACTS _DebugCompressionArtifacts
#endif

#ifdef BLOCK_SIZE_2
    #define BLOCK_SIZE 2
#endif
#ifdef BLOCK_SIZE_4
    #define BLOCK_SIZE 4
#endif
#ifdef BLOCK_SIZE_8
    #define BLOCK_SIZE 8
#endif
#ifdef BLOCK_SIZE_16
    #define BLOCK_SIZE 16
#endif
#ifdef BLOCK_SIZE_32
    #define BLOCK_SIZE 32
#endif
#ifndef BLOCK_SIZE
    #define BLOCK_SIZE 4
#endif

// Hash function used throughout the shader
float hash1(uint n)
{
    n++;
    n = (n << 13U) ^ n;
    n = n * (n * n * 15731U + 789221U) + 1376312589U;
    return float(n & uint(0x7fffffffU))/float(0x7fffffff);
}

// Luminance calculation for brightness-based masking
float CalculateLuminance(float3 color)
{
    return dot(color, float3(0.2126, 0.7152, 0.0722));
}

// Brightness-based noise masking function
float CalculateBrightnessMask(float3 color, float brightnessThreshold, float maskingStrength)
{
    float luminance = CalculateLuminance(color);
    float brightnessFactor = saturate((luminance - brightnessThreshold) / (1.0 - brightnessThreshold));
    return lerp(1.0, 1.0 - maskingStrength, brightnessFactor);
}

// Enhanced Motion Vector Processing
// Implements coordinate transformation and pixelated noise generation
float2 ProcessMotionVectorCoordinates(float2 baseUV, float2 motionVector, float intensity)
{
    // Guide's coordinate transformation approach
    // Add motion vector offset to base UV coordinates
    float2 processedMotion = motionVector * intensity;
    return baseUV + processedMotion;
}

float4 GeneratePixelatedNoise(float2 uv, float pixelationScale, float noiseScale)
{
    // Guide's pixelated noise system implementation
    // Create stepped/pixelated coordinates
    float2 pixelatedUV = floor(uv * pixelationScale) / pixelationScale;

    // Generate noise using pixelated coordinates
    float noise = hash1(uint(pixelatedUV.x * 1000.0 + pixelatedUV.y * 2000.0 + FLUX_TIME.y * noiseScale));
    return float4(noise, noise, noise, 1.0);
}

float2 EnhancedMotionVectorSampling(float2 uv, float2 motionVector, float blendFactor)
{
    // Guide's motion vector blending approach
    // Blend between base UV and motion-offset UV
    float2 motionOffsetUV = ProcessMotionVectorCoordinates(uv, motionVector, 1.0);
    return lerp(uv, motionOffsetUV, blendFactor);
}

//
float basis1D(float k, float i)
{
    float4 _G = float4(2, 1, 2, 2);
    float _Contrast = 0.0;
    return k == 0 ? sqrt(1. / float(BLOCK_SIZE)) : sqrt((_G.w + _Contrast) / float(BLOCK_SIZE)) * cos(float((_G.x * float(i) + _G.y) * k) * 3.14159265358 / (_G.z * float(BLOCK_SIZE)));
}
float basis2D(float2 jk, float2 xy)
{
    return basis1D(jk.x, xy.x) * basis1D(jk.y, xy.y);
}
float4 jpg(float2 uv, int m)
{
    // JPEG quality calculation - use user-controlled quality with JPG Bitcrunch fallback
    float _Quality = (FLUX_JPEG_QUALITY > 0.001) ? (FLUX_JPEG_QUALITY / 25.0) : 4.0; // Default to JPG Bitcrunch value if not set
    float quality = length(float2(_Quality, _Quality)); // Exact same calculation as JPG Bitcrunch
    float4 outColor = float4(0, 0, 0, 1);

    float2 textureSize = FLUX_DOWNSCALED_TEXEL_SIZE.zw;
    textureSize = floor(textureSize / 2.0) * 2.0;

    float2 coords = int2(textureSize * uv);
    float2 inBlock = coords % BLOCK_SIZE - m * 0.5;
    float2 block = coords - inBlock;

    // EXACT DCT implementation like JPG Bitcrunch - no complex features
    [loop]
    for (int2 xy = 0; xy.x < BLOCK_SIZE; xy.x++)
    {
        [loop]
        for (xy.y = 0; xy.y < BLOCK_SIZE; xy.y++)
        {
            outColor += SAMPLE(FLUX_INPUT, sampler_LinearClamp, float2(block + xy) / textureSize)
                            * basis2D(lerp(inBlock, xy, m), lerp(inBlock, xy, 1.0 - m));
        }
    }

    // Much more aggressive frequency filtering like JPG Bitcrunch
    outColor *= lerp(step(length(float2(inBlock)), quality), 1.0, m);
    return outColor;
}
//


#ifndef SHADER_STAGE_COMPUTE
// Fragment shader functions - not available in compute shaders

float4 Downscale_Frag(Varyings input) : SV_Target
{
    return SAMPLE(FLUX_INPUT, sampler_LinearClamp, input.uv);
}

float4 Encode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 0);

    // 5. Multi-Scale Corruption - Different corruption at multiple scales
    if (FLUX_MULTI_SCALE_CORRUPTION > 0.001)
    {
        #ifdef MOBILE_OPTIMIZED
            // Mobile optimization: Only use 2 scales instead of 3
            float2 mediumBlockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + FLUX_TIME.y * 4.0));

            float2 smallBlockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + FLUX_TIME.y * 5.0));

            if (mediumCorruption < FLUX_MULTI_SCALE_CORRUPTION * 0.6)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * FLUX_MULTI_SCALE_CORRUPTION * 0.2;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < FLUX_MULTI_SCALE_CORRUPTION * 0.8)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * FLUX_MULTI_SCALE_CORRUPTION * 0.05;
            }
        #else
            // Desktop: Full 3-scale corruption
            float2 largeBlockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / 32.0);
            float largeCorruption = hash1(uint(largeBlockPos.x * 111 + largeBlockPos.y * 222 + FLUX_TIME.y * 3.0));

            float2 mediumBlockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + FLUX_TIME.y * 4.0));

            float2 smallBlockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + FLUX_TIME.y * 5.0));

            if (largeCorruption < FLUX_MULTI_SCALE_CORRUPTION * 0.3)
            {
                col.rgb += (hash1(uint(largeBlockPos.x * 777 + largeBlockPos.y * 888)) - 0.5) * FLUX_MULTI_SCALE_CORRUPTION * 0.2;
            }

            if (mediumCorruption < FLUX_MULTI_SCALE_CORRUPTION * 0.5)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * FLUX_MULTI_SCALE_CORRUPTION * 0.3;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < FLUX_MULTI_SCALE_CORRUPTION * 0.7)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * FLUX_MULTI_SCALE_CORRUPTION * 0.1;
            }
        #endif
    }

    // DCT Corruption - only when enabled (> 0) with brightness control
    if (FLUX_DCT_CORRUPTION > 0.001)
    {
        float2 blockPos = floor(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw / BLOCK_SIZE);
        float corruption = hash1(uint(blockPos.x * 123 + blockPos.y * 456 + FLUX_TIME.y * 5.0));

        if (corruption < FLUX_DCT_CORRUPTION)
        {
            // Corrupt specific frequency components
            float2 freqPos = fmod(input.uv * FLUX_DOWNSCALED_TEXEL_SIZE.zw, BLOCK_SIZE);
            float freqWeight = length(freqPos - BLOCK_SIZE * 0.5) / (BLOCK_SIZE * 0.5);

            // Apply brightness-based masking to DCT corruption
            float brightnessMask = CalculateBrightnessMask(col.rgb, FLUX_BRIGHTNESS_THRESHOLD, FLUX_BRIGHT_AREA_MASKING);

            // Higher frequencies get more corruption (typical of compression artifacts)
            float corruptionAmount = freqWeight * FLUX_DCT_CORRUPTION * brightnessMask;
            float3 corruptionNoise = (hash1(uint(blockPos.x * 789 + blockPos.y * 123 + FLUX_TIME.y * 7.0)) - 0.5) * corruptionAmount * 0.5;

            // Clamp corruption to prevent peak brightness
            corruptionNoise = min(corruptionNoise, FLUX_MAX_NOISE_BRIGHTNESS - CalculateLuminance(col.rgb));
            col.rgb += corruptionNoise;
        }
    }

    // EXACT JPG Bitcrunch color crunching - this is the key visual effect!
    if(FLUX_COLOR_CRUNCH == 0.0) return col;
    #ifndef COLOR_CRUNCH_SKYBOX
        float depth = SAMPLE(FLUX_CAMERA_DEPTH_TEXTURE, sampler_LinearClamp, input.uv).x;
        if (depth == 0.0 || depth == 1.0) return col;
    #endif

    // EXACT same truncation calculation as JPG Bitcrunch
    float truncation = log10(lerp(1.0, 0.0001, FLUX_COLOR_CRUNCH));
    col.rgb = round(col.rgb / truncation) * truncation;

    // Enhanced Feature: Additional JPEG quantization features
    if (FLUX_LUMINANCE_QUANTIZATION > 0.0 || FLUX_CHROMINANCE_QUANTIZATION > 0.0)
    {
        // Convert to YUV for separate quantization
        float3 yuv;
        yuv.x = dot(col.rgb, float3(0.299, 0.587, 0.114)); // Luminance
        yuv.y = dot(col.rgb, float3(-0.14713, -0.28886, 0.436)); // U (Cb)
        yuv.z = dot(col.rgb, float3(0.615, -0.51499, -0.10001)); // V (Cr)

        // Apply additional quantization
        if (FLUX_LUMINANCE_QUANTIZATION > 0.0)
        {
            float lumaQuantization = log10(lerp(1.0, 0.001, FLUX_LUMINANCE_QUANTIZATION));
            yuv.x = round(yuv.x / lumaQuantization) * lumaQuantization;
        }

        if (FLUX_CHROMINANCE_QUANTIZATION > 0.0)
        {
            float chromaQuantization = log10(lerp(1.0, 0.001, FLUX_CHROMINANCE_QUANTIZATION));
            yuv.yz = round(yuv.yz / chromaQuantization) * chromaQuantization;
        }

        // Convert back to RGB
        col.r = yuv.x + 1.13983 * yuv.z;
        col.g = yuv.x - 0.39465 * yuv.y - 0.58060 * yuv.z;
        col.b = yuv.x + 2.03211 * yuv.y;
        col.rgb = saturate(col.rgb);
    }

    return col;
}

float4 Decode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 1);
    col.a = 1.0;
    return col;
}


float4 Upscale_Pull_Frag(Varyings input) : SV_Target
{
    float2 uv = input.uv;
    
    float3 center = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(0, 0)).rgb;
    float3 col;
    if (FLUX_SHARPENING > 0.0)
    {
        float3 up = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(0, 1)).rgb;
        float3 left = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(-1, 0)).rgb;
        float3 right = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(1, 0)).rgb;
        float3 down = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(0, -1)).rgb;
        FLUX_SHARPENING *= 2.0;
        col = (1.0 + 4.0 * FLUX_SHARPENING) * center - FLUX_SHARPENING * (up + left + right + down);
    }
    else
    {
        col = center;
    }

    // MOTION VECTOR VISUALIZATION - Enable this to see motion vectors
    // #define VIZ_MOTION_VECTORS
    #ifdef VIZ_MOTION_VECTORS
        float2 mv = SAMPLE(FLUX_MOTION_VECTOR_TEXTURE, sampler_LinearClamp, uv).xy;
        // Scale motion vectors for visibility and show as color
        return 0.5 + float4(mv * 50.0, 0.0, 1.0);
    #endif

    #ifdef REPROJECTION
        // Motion Sampling: Different behavior for Pure Datamosh vs Enhanced Mode
        float2 snappedUV = (floor(uv / (FLUX_DOWNSCALED_TEXEL_SIZE.xy * BLOCK_SIZE)) + 0.5) * (FLUX_DOWNSCALED_TEXEL_SIZE.xy * BLOCK_SIZE);

        // Core Datamosh: Always use block-based sampling as base
        float2 motionSampleUV = snappedUV;
        int2 blockID = floor(uv / (FLUX_DOWNSCALED_TEXEL_SIZE.xy * BLOCK_SIZE));

        // Enhanced Mode: Optionally blend with per-pixel sampling for smooth trails
        if (FLUX_TRAIL_SMOOTHNESS > 0.001)
        {
            float2 pixelUV = uv; // Per-pixel sampling for smooth trails
            float smoothnessFactor = FLUX_TRAIL_SMOOTHNESS;
            motionSampleUV = lerp(snappedUV, pixelUV, smoothnessFactor);
            // Also apply smoothness to block ID calculation for smoother transitions
            blockID = floor(lerp(snappedUV, pixelUV, smoothnessFactor * 0.5) / (FLUX_DOWNSCALED_TEXEL_SIZE.xy * BLOCK_SIZE));
        }

        // Core Datamosh: Basic corruption control (always active)
        float glitchTransition = 1.0;

        // Enhanced Feature: Glitch Transition - Animated corruption spread
        if (FLUX_GLITCH_TRANSITION > 0.001)
        {
            float2 transitionCenter = float2(0.5, 0.5); // Could be animated
            float distanceFromCenter = length(uv - transitionCenter);
            float transitionRadius = FLUX_GLITCH_TRANSITION * 2.0;
            glitchTransition = smoothstep(transitionRadius - 0.2, transitionRadius + 0.2, distanceFromCenter);
            if (glitchTransition < 0.1) return float4(col, 1.0); // Clean areas during transition
        }

        // Enhanced Feature: Keyframe Reset (I-Frame simulation) - only when enabled
        if (FLUX_KEYFRAME_RESET_RATE > 0.001)
        {
            float keyframeReset = hash1(uint(blockID.x * 789 + blockID.y * 456 + floor(FLUX_TIME.y * 60.0)));
            if (keyframeReset < FLUX_KEYFRAME_RESET_RATE)
                return float4(col, 1.0); // Reset to clean frame
        }

        // ENHANCED Motion Vector Sampling
        // Declare motion vector variables at higher scope to avoid undeclared identifier errors
        float2 motionVector = float2(0, 0);
        float2 enhancedMotionVector = float2(0, 0);

        // Core Datamosh: Always sample motion vectors (JPG Bitcrunch base behavior)
        float2 baseMotionVector = SAMPLE(FLUX_MOTION_VECTOR_TEXTURE, sampler_LinearClamp, motionSampleUV).xy;
        motionVector = baseMotionVector;
        enhancedMotionVector = baseMotionVector;

        // Enhanced Feature: Coordinate transformation for enhanced motion processing
        if (FLUX_MOTION_AMPLIFICATION > 0.001)
        {
            float transformIntensity = FLUX_MOTION_AMPLIFICATION * 0.1; // Scale down for coordinate transform
            #ifdef CUSTOM_RT_ENHANCED
                // Custom RT Enhanced Mode: Double the transformation intensity
                transformIntensity *= 2.0;
            #endif

            float2 transformedUV = ProcessMotionVectorCoordinates(motionSampleUV, baseMotionVector, transformIntensity);
            float2 enhancedSample = SAMPLE(FLUX_MOTION_VECTOR_TEXTURE, sampler_LinearClamp, transformedUV).xy;

            // Blend enhanced sample with base motion vector
            if (FLUX_TRAIL_SMOOTHNESS > 0.001)
            {
                motionVector = lerp(baseMotionVector, enhancedSample, FLUX_TRAIL_SMOOTHNESS);
                enhancedMotionVector = motionVector;
            }
        }


        // Calculate camera motion magnitude using actual screen resolution
        float2 screenResolution = FLUX_SCREEN_TEXEL_SIZE.zw;
        float motionMagnitude = length(motionVector * screenResolution);

        // Motion Processing System - Core + Enhanced Features
        float totalMotionAmplification = 1.0;

        // CONSOLIDATED MOTION PROCESSING SYSTEM
        // Single unified system that replaces all previous overlapping motion parameters
        if (motionMagnitude > FLUX_MOTION_THRESHOLD)
        {
            // Master motion amplification with much stronger base multiplier
            float masterAmplification = FLUX_MOTION_AMPLIFICATION * motionMagnitude * 15.0; // Increased from 10.0

            // Trail intensity directly affects motion amplification
            float trailAmplification = FLUX_TRAIL_INTENSITY * motionMagnitude * 8.0;

            // Camera vs Object motion balance - unified approach
            // This replaces separate camera motion parameters with a single balance control
            float cameraMotionFactor = FLUX_CAMERA_OBJECT_MOTION_BALANCE;
            float objectMotionFactor = 1.0 - FLUX_CAMERA_OBJECT_MOTION_BALANCE;

            // Apply balanced motion scaling with threshold
            float balancedAmplification = (cameraMotionFactor * 2.0 + objectMotionFactor) *
                                        smoothstep(FLUX_MOTION_THRESHOLD, FLUX_MOTION_THRESHOLD * 2.0, motionMagnitude) * 6.0;

            // Apply motion smoothing to prevent jittery effects
            float smoothingFactor = lerp(1.0, 0.3, FLUX_MOTION_SMOOTHING);

            // Combine all amplifications with smoothing
            totalMotionAmplification = 1.0 + (masterAmplification + trailAmplification + balancedAmplification) * smoothingFactor;
        }

        // Consolidated Motion Processing with Flow Spread
        if (FLUX_FLOW_SPREAD > 0.001 && motionMagnitude > 0.001)
        {
            // Calculate flow patterns that spread outward from motion centers
            float2 motionDirection = normalize(motionVector);
            float2 uvOffset = uv - 0.5;
            float motionAlignment = dot(normalize(uvOffset), motionDirection);

            // Enhanced flow spread calculation with stronger multipliers
            float spreadInfluence = FLUX_FLOW_SPREAD * motionMagnitude * (1.0 + motionAlignment) * 5.0; // Increased from 2.0
            totalMotionAmplification += spreadInfluence;
        }

        // Apply total amplification
        enhancedMotionVector *= totalMotionAmplification;

        // Enhanced Motion Vector Corruption with Pixelated Noise (works in all modes)
        if (FLUX_MOTION_VECTOR_CORRUPTION > 0.001)
        {
            // Original corruption method
            float2 corruption = float2(
                hash1(uint(blockID.x * 123 + blockID.y * 456 + FLUX_TIME.y * 10.0)) - 0.5,
                hash1(uint(blockID.x * 456 + blockID.y * 789 + FLUX_TIME.y * 10.0)) - 0.5
            ) * FLUX_MOTION_VECTOR_CORRUPTION * 0.1;

            // Pixelated noise enhancement with motion-based scale
            float pixelationScale = FLUX_MOTION_VECTOR_CORRUPTION * 100.0 + 10.0; // Motion-based scale with base value
            float4 pixelatedNoise = GeneratePixelatedNoise(uv, pixelationScale, 5.0);

            // Combine traditional corruption with pixelated noise using brightness controls
            float baseNoiseIntensity = FLUX_NOISE_TRANSPARENCY; // Use user-controlled transparency
            #ifdef PURE_DATAMOSH_MODE
                baseNoiseIntensity *= 0.5; // Reduced intensity in Pure mode
            #endif

            // Apply brightness-based masking to noise intensity
            float3 currentColor = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv).rgb;
            float brightnessMask = CalculateBrightnessMask(currentColor, FLUX_BRIGHTNESS_THRESHOLD, FLUX_BRIGHT_AREA_MASKING);
            float effectiveNoiseIntensity = baseNoiseIntensity * brightnessMask;

            // Clamp pixelated noise to prevent peak brightness
            float4 clampedPixelatedNoise = float4(min(pixelatedNoise.xyz, FLUX_MAX_NOISE_BRIGHTNESS), pixelatedNoise.w);

            float2 enhancedCorruption = corruption + (clampedPixelatedNoise.xy - 0.5) * FLUX_MOTION_VECTOR_CORRUPTION * effectiveNoiseIntensity;
            enhancedMotionVector += enhancedCorruption;
        }

        // Use appropriate motion vector based on mode
        #ifndef PURE_DATAMOSH_MODE
            // Enhanced Mode: Use enhanced motion vector for additional features
            motionVector = enhancedMotionVector;
        #endif
        // Pure Datamosh Mode: Keep original motion vector unchanged

        #ifdef COMPRESSION_ARTIFACTS
            // Enhanced Compression Artifacts: Ringing and Mosquito Noise
            if (FLUX_RINGING_ARTIFACTS > 0.001 || FLUX_MOSQUITO_NOISE > 0.001)
            {
                // Debug mode: Show compression artifacts in bright colors
                if (FLUX_DEBUG_COMPRESSION_ARTIFACTS > 0.5)
                {
                    // Bright red overlay to show where compression artifacts are active
                    col.rgb = lerp(col.rgb, float3(1.0, 0.0, 0.0), 0.3);
                }
                // Improved edge detection for artifact placement
                float3 up = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(0, 1)).rgb;
                float3 left = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(-1, 0)).rgb;
                float3 right = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(1, 0)).rgb;
                float3 down = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv + FLUX_DOWNSCALED_TEXEL_SIZE.xy * float2(0, -1)).rgb;

                // Enhanced edge detection with better sensitivity
                float edgeDetection = length(col - up) + length(col - left) + length(col - right) + length(col - down);
                edgeDetection *= FLUX_EDGE_SENSITIVITY;

                // Lower threshold for more visible artifacts and add base artifact level
                float artifactThreshold = 0.05; // Reduced from 0.1 for more sensitivity
                float baseArtifactLevel = 0.1; // Always apply some artifacts even without strong edges

                if (edgeDetection > artifactThreshold || baseArtifactLevel > 0.0)
                {
                    float effectiveEdgeStrength = max(edgeDetection, baseArtifactLevel);

                    // Enhanced Ringing artifacts: Overshoot around edges with brightness control
                    if (FLUX_RINGING_ARTIFACTS > 0.001)
                    {
                        float ringingFrequency = 12.0; // Increased frequency for more visible ringing
                        float2 ringingPattern = sin(uv * ringingFrequency * 3.14159);

                        // Apply brightness-based masking to ringing artifacts
                        float brightnessMask = CalculateBrightnessMask(col.rgb, FLUX_BRIGHTNESS_THRESHOLD, FLUX_BRIGHT_AREA_MASKING);

                        // Multi-directional ringing for more authentic compression artifacts
                        float ringingIntensity = FLUX_RINGING_ARTIFACTS * effectiveEdgeStrength * brightnessMask;
                        float3 ringingEffect = float3(
                            ringingPattern.x * ringingIntensity * 0.3,
                            ringingPattern.y * ringingIntensity * 0.2,
                            (ringingPattern.x + ringingPattern.y) * ringingIntensity * 0.1
                        );

                        // Clamp ringing effect to prevent peak brightness
                        ringingEffect = min(ringingEffect, FLUX_MAX_NOISE_BRIGHTNESS - CalculateLuminance(col.rgb));
                        col.rgb += ringingEffect;
                    }

                    // Enhanced Mosquito noise: High-frequency artifacts with brightness control
                    if (FLUX_MOSQUITO_NOISE > 0.001)
                    {
                        float mosquitoFrequency = 20.0; // Higher frequency for more visible mosquito noise

                        // Multi-layer mosquito noise for more authentic effect
                        float mosquitoNoise1 = sin(uv.x * mosquitoFrequency + FLUX_TIME.y * 15.0) *
                                             sin(uv.y * mosquitoFrequency + FLUX_TIME.y * 18.0);
                        float mosquitoNoise2 = sin(uv.x * mosquitoFrequency * 1.3 + FLUX_TIME.y * 12.0) *
                                             sin(uv.y * mosquitoFrequency * 0.7 + FLUX_TIME.y * 22.0);

                        float combinedMosquitoNoise = (mosquitoNoise1 + mosquitoNoise2 * 0.5) / 1.5;

                        // Apply brightness-based masking to mosquito noise
                        float brightnessMask = CalculateBrightnessMask(col.rgb, FLUX_BRIGHTNESS_THRESHOLD, FLUX_BRIGHT_AREA_MASKING);

                        // Enhanced intensity calculation - now includes motion bonus but doesn't require it
                        float mosquitoIntensity = FLUX_MOSQUITO_NOISE * effectiveEdgeStrength * brightnessMask;
                        if (motionMagnitude > 0.001)
                        {
                            mosquitoIntensity *= (1.0 + motionMagnitude * 2.0); // Motion bonus
                        }

                        // Apply mosquito noise with brightness clamping
                        float3 mosquitoEffect = combinedMosquitoNoise * mosquitoIntensity * 0.15;
                        mosquitoEffect = min(mosquitoEffect, FLUX_MAX_NOISE_BRIGHTNESS - CalculateLuminance(col.rgb));
                        col.rgb += mosquitoEffect;
                    }
                }
            }
        #endif

        // Enhanced Previous Frame Sampling (works in all modes)
        float2 baseOffsetUV = uv - enhancedMotionVector;



        // Declare pull variable at proper scope
        float3 pull;

        // Apply enhanced motion vector sampling
        if (FLUX_TRAIL_SMOOTHNESS > 0.001 && FLUX_MOTION_AMPLIFICATION > 0.001)
        {
            float2 enhancedOffsetUV = EnhancedMotionVectorSampling(uv, enhancedMotionVector, FLUX_TRAIL_SMOOTHNESS);

            // Blend between base and enhanced sampling using coordinate transform intensity
            float blendStrength = FLUX_MOTION_AMPLIFICATION * 0.02; // Scale down for blending
            #ifdef PURE_DATAMOSH_MODE
                blendStrength *= 0.5; // Reduced strength in Pure mode
            #endif

            float2 finalSampleUV = lerp(baseOffsetUV, enhancedOffsetUV, blendStrength);
            pull = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, finalSampleUV).rgb;
        }
        else
        {
            // Standard sampling when enhanced features are disabled
            pull = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, baseOffsetUV).rgb;
        }

        // Safety check for uninitialized previous frame data (like JPG Bitcrunch)
        if (all(pull == 0.0)) return float4(col, 1.0);

        // Debug: Uncomment to visualize when previous frame data is available
        // if (any(pull > 0.0)) return float4(1, 0, 1, 1); // Magenta = previous frame data available

        // Debug: Motion Vector Visualization
        // return 0.5 + float4(motionVector * 50.0, 0.0, 1.0); // Visualize motion vectors

        // Debug: Reprojection Threshold Visualization
        // float debugThreshold = FLUX_REPROJECT_PERCENT + min(length(motionVector * float2(1920, 1080)) * FLUX_REPROJECT_LENGTH_INFLUENCE, 0.7);
        // if (debugThreshold > 0.1) return float4(0, 1, 0, 1); // Green = high reprojection chance

        #ifndef PURE_DATAMOSH_MODE
            // Trail Persistence: Enhanced temporal trail effects with stronger multipliers
            if (FLUX_TRAIL_PERSISTENCE > 0.001 && motionMagnitude > 0.001)
            {
                // Sample multiple points along the motion vector for persistence with increased intensity
                float3 persistentSample1 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 0.3).rgb;
                float3 persistentSample2 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 0.7).rgb;
                float3 persistentSample3 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 1.2).rgb;
                float3 persistentSample4 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 1.8).rgb;
                float3 persistentSample5 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 2.5).rgb;
                float3 persistentSample6 = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - enhancedMotionVector * 3.2).rgb;

                // Enhanced persistence strength with much stronger motion response
                float persistenceStrength = FLUX_TRAIL_PERSISTENCE * (2.0 + motionMagnitude * 200.0); // Doubled base + stronger motion scaling
                float3 persistentAverage = (persistentSample1 + persistentSample2 + persistentSample3 + persistentSample4 + persistentSample5 + persistentSample6) / 6.0;

                // Enhanced blending with stronger motion-based influence
                pull = lerp(pull, persistentAverage, saturate(persistenceStrength));
            }

            // Note: Temporal accumulation now integrated into Trail Persistence above for consolidated control
        #endif

        #ifndef PURE_DATAMOSH_MODE
            // Enhanced corruption features only in non-pure mode

            // 3. Chroma Corruption - Separate RGB channel corruption with brightness control
            if (FLUX_CHROMA_CORRUPTION > 0.001)
            {
                // Apply brightness-based masking to chroma corruption
                float3 currentColor = SAMPLE(FLUX_INPUT, sampler_LinearClamp, uv).rgb;
                float brightnessMask = CalculateBrightnessMask(currentColor, FLUX_BRIGHTNESS_THRESHOLD, FLUX_BRIGHT_AREA_MASKING);

                float effectiveChromaCorruption = FLUX_CHROMA_CORRUPTION * brightnessMask;

                float2 chromaOffset1 = float2(
                    hash1(uint(blockID.x * 234 + blockID.y * 567 + FLUX_TIME.y * 8.0)) - 0.5,
                    hash1(uint(blockID.x * 567 + blockID.y * 890 + FLUX_TIME.y * 8.0)) - 0.5
                ) * effectiveChromaCorruption * 0.02;

                float2 chromaOffset2 = float2(
                    hash1(uint(blockID.x * 345 + blockID.y * 678 + FLUX_TIME.y * 9.0)) - 0.5,
                    hash1(uint(blockID.x * 678 + blockID.y * 901 + FLUX_TIME.y * 9.0)) - 0.5
                ) * effectiveChromaCorruption * 0.02;

                // Sample RGB channels with different offsets
                float3 chromaCorruptedSample = float3(
                    SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - motionVector + chromaOffset1).r,
                    SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - motionVector).g,
                    SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv - motionVector + chromaOffset2).b
                );

                // Clamp chroma corrupted result to prevent peak brightness
                chromaCorruptedSample = min(chromaCorruptedSample, FLUX_MAX_NOISE_BRIGHTNESS);
                pull = chromaCorruptedSample;
            }

            // 4. Feedback Loops - Route output back as input
            if (FLUX_FEEDBACK_INTENSITY > 0.001)
            {
                float feedbackNoise = hash1(uint(blockID.x * 456 + blockID.y * 789 + FLUX_TIME.y * 12.0));
                if (feedbackNoise < FLUX_FEEDBACK_INTENSITY)
                {
                    // Create feedback by sampling from a slightly offset position
                    float2 feedbackOffset = float2(
                        hash1(uint(blockID.x * 111 + blockID.y * 222 + FLUX_TIME.y * 6.0)) - 0.5,
                        hash1(uint(blockID.x * 222 + blockID.y * 333 + FLUX_TIME.y * 6.0)) - 0.5
                    ) * 0.05;

                    float3 feedbackSample = SAMPLE(FLUX_PREV_SCREEN, sampler_LinearClamp, uv + feedbackOffset).rgb;
                    pull = lerp(pull, feedbackSample, FLUX_FEEDBACK_INTENSITY * 0.5);
                }
            }
        #endif

        #ifdef PURE_DATAMOSH_MODE
            // Pure Datamosh Mode: Simple reprojection calculation like JPG Bitcrunch
            // No corruption mask or transition effects - just pure datamoshing
            float reprojectChance = FLUX_REPROJECT_PERCENT + min(length(motionVector * float2(1920, 1080)) * FLUX_REPROJECT_LENGTH_INFLUENCE, 0.7);
        #else
            // Enhanced Mode: Complex reprojection with all features

            // Apply corruption mask and transition effects
            float finalCorruptionStrength = corruptionMask * glitchTransition;

            // MASSIVE Motion-Driven Reprojection Logic
            // Use actual screen resolution with MASSIVE motion scaling
            float motionInfluence = length(enhancedMotionVector * screenResolution) * FLUX_REPROJECT_LENGTH_INFLUENCE * 10.0; // 10x stronger

            // Remove limits - let motion drive everything
            motionInfluence = min(motionInfluence, 50.0); // MASSIVE increase from 2.0

            // CONSOLIDATED reprojection chance using unified motion system
            float consolidatedMotionContribution = 0.0;
            if (motionMagnitude > FLUX_MOTION_THRESHOLD)
            {
                // Use the same unified motion processing for reprojection
                float motionAboveThreshold = motionMagnitude - FLUX_MOTION_THRESHOLD;
                
                // Apply motion amplification with trail intensity
                consolidatedMotionContribution = motionAboveThreshold * FLUX_MOTION_AMPLIFICATION * FLUX_TRAIL_INTENSITY * 25.0;
                
                // Apply camera/object balance to reprojection
                float balanceMultiplier = lerp(1.0, 2.5, FLUX_CAMERA_OBJECT_MOTION_BALANCE); // Camera motion gets stronger influence
                consolidatedMotionContribution *= balanceMultiplier;
                
                // Cap at reasonable maximum
                consolidatedMotionContribution = min(consolidatedMotionContribution, 40.0);
            }

            // Combine all influences with enhanced motion response
            float reprojectChance = (FLUX_REPROJECT_PERCENT + motionInfluence + consolidatedMotionContribution) * finalCorruptionStrength;
        #endif

        // FIXED: Trail Smoothness now has dramatic impact on blending behavior
        float blendFactor = reprojectChance;

        #ifdef PURE_DATAMOSH_MODE
            // Pure Datamosh Mode: EXACT JPG Bitcrunch behavior - simple, direct, aggressive
            // Use the exact same logic as JPG Bitcrunch for authentic datamoshing
            float reprojectThreshold = FLUX_REPROJECT_PERCENT + min(length(motionVector * float2(1920, 1080)) * FLUX_REPROJECT_LENGTH_INFLUENCE, 0.7);

            // CRITICAL FIX: Remove explicit uint() cast to match JPG Bitcrunch exactly
            if (hash1((123 + blockID.x) * (456 + blockID.y) + (FLUX_TIME.y * FLUX_REPROJECT_SPEED)) < reprojectThreshold)
            {
                // Direct reprojection like JPG Bitcrunch - no blending, no accumulation
                return float4(pull, 1.0);
            }
        #else
            // Enhanced Mode: Trail smoothness and enhanced blending
            // FIXED: Lower threshold for smooth mode and stronger blending
            if (FLUX_TRAIL_SMOOTHNESS > 0.1) // Much lower threshold
            {
                // FIXED: Smooth mode with much stronger blending
                float smoothBlend = saturate(blendFactor * (1.0 + FLUX_TRAIL_SMOOTHNESS * 3.0)); // Much stronger

                // FIXED: Trail Smoothness affects the blending strength directly
                float trailStrength = FLUX_TRAIL_SMOOTHNESS * (1.0 + motionMagnitude);

                // Enhanced blending with temporal accumulation
                if (FLUX_ERROR_ACCUMULATION > 0.001)
                {
                    float3 accumulated = lerp(col, pull, FLUX_ERROR_ACCUMULATION * smoothBlend * (1.0 + trailStrength));
                    return float4(accumulated, 1.0);
                }
                else
                {
                    // FIXED: Much stronger smooth reprojection blending
                    float3 smoothResult = lerp(col, pull, smoothBlend * (0.5 + trailStrength));
                    return float4(smoothResult, 1.0);
                }
            }
            else
            {
                // Block mode: Use original random threshold approach
                if (hash1(uint((123 + blockID.x) * (456 + blockID.y) + (FLUX_TIME.y * FLUX_REPROJECT_SPEED))) < reprojectChance)
                {
                    // Error Accumulation - only when enabled, otherwise use original behavior
                    if (FLUX_ERROR_ACCUMULATION > 0.001)
                    {
                        float3 accumulated = lerp(col, pull, FLUX_ERROR_ACCUMULATION);
                        return float4(accumulated, 1.0);
                    }
                    else
                    {
                        // Original behavior - direct reprojection
                        return float4(pull, 1.0);
                    }
                }
            }
        #endif
    #endif
    
    return float4(col, 1.0);
}

float4 CopyToPrev_Frag(Varyings input) : SV_Target
{
    return SAMPLE(_Input, sampler_LinearClamp, input.uv);
}

#endif // SHADER_STAGE_COMPUTE
