# Advanced Datamoshing Features for Flux Effect

## Overview

The Flux effect now includes comprehensive datamoshing capabilities that simulate authentic video compression artifacts and glitches. These features work seamlessly with Unity 6 Render Graph for URP and provide the missing elements for true datamoshing effects.

## New Parameters

### Advanced Datamoshing Section

#### **Keyframe Reset Rate** (0.0 - 1.0)

- **Purpose**: Simulates I-Frame behavior in video compression
- **Function**: Periodically resets blocks to clean frames, preventing infinite error accumulation
- **Values**:
  - `0.0`: Never reset (infinite accumulation)
  - `0.1`: Reset approximately every 10 frames (recommended)
  - `1.0`: Reset every frame (disables temporal effects)
- **Use Case**: Controls how "clean" vs "corrupted" the effect becomes over time

#### **Motion Vector Corruption** (0.0 - 2.0)

- **Purpose**: Adds random noise to motion vectors for classic datamosh smearing
- **Function**: Corrupts motion vector data to create wrong reprojection directions
- **Values**:
  - `0.0`: Clean motion vectors
  - `0.5`: Subtle corruption
  - `2.0`: Heavy corruption with dramatic smearing
- **Use Case**: Creates the characteristic "dragging" artifacts of corrupted video

#### **Error Accumulation** (0.0 - 1.0)

- **Purpose**: Allows temporal errors to build up over multiple frames
- **Function**: Blends current frame with previous frame data for trailing effects
- **Values**:
  - `0.0`: No accumulation (clean reprojection)
  - `0.5`: Moderate trailing
  - `1.0`: Heavy trailing artifacts
- **Use Case**: Creates persistent "ghost" trails and temporal smearing

#### **DCT Corruption** (0.0 - 1.0)

- **Purpose**: Simulates corrupted DCT coefficients in compression
- **Function**: Adds frequency-based artifacts during the encoding pass
- **Values**:
  - `0.0`: No DCT corruption
  - `0.3`: Subtle compression artifacts
  - `1.0`: Heavy compression corruption
- **Use Case**: Adds authentic JPEG/H.264-style compression glitches

## Technical Implementation

### Shader Enhancements

#### **Keyframe Reset System**

```hlsl
// I-Frame simulation with per-block random reset
float keyframeReset = hash1(blockID.x * 789 + blockID.y * 456 + floor(_Time.y * 60.0));
if (keyframeReset < _KeyframeResetRate)
    return float4(col, 1.0); // Reset to clean frame
```

#### **Motion Vector Corruption**

```hlsl
// Add random noise to motion vectors
float2 corruption = float2(
    hash1(blockID.x * 123 + blockID.y * 456 + _Time.y * 10.0) - 0.5,
    hash1(blockID.x * 456 + blockID.y * 789 + _Time.y * 10.0) - 0.5
) * _MotionVectorCorruption * 0.1;
motionVector += corruption;
```

#### **Error Accumulation**

```hlsl
// Blend current and previous frames for trailing effects
if (_ErrorAccumulation > 0.0)
{
    float3 accumulated = lerp(col, pull, _ErrorAccumulation);
    return float4(accumulated, 1.0);
}
```

#### **DCT Corruption**

```hlsl
// Corrupt frequency components during encoding
float freqWeight = length(freqPos - BLOCK_SIZE * 0.5) / (BLOCK_SIZE * 0.5);
float corruptionAmount = freqWeight * _DCTCorruption;
col.rgb += (hash1(...) - 0.5) * corruptionAmount * 0.5;
```

### Unity 6 Render Graph Compatibility

All new features are fully integrated with Unity 6's Render Graph system:

- **Automatic resource management** for temporal textures
- **Pass merging optimization** on mobile TBDR architectures
- **Memory-efficient** temporal frame storage
- **MSAA-compatible** depth and stencil handling

## Usage Recommendations

### **Classic Datamosh Look**

- Keyframe Reset Rate: `0.05` (rare resets)
- Motion Vector Corruption: `1.0` (heavy smearing)
- Error Accumulation: `0.7` (strong trails)
- DCT Corruption: `0.3` (subtle compression artifacts)

### **Subtle Glitch Effect**

- Keyframe Reset Rate: `0.2` (frequent resets)
- Motion Vector Corruption: `0.3` (light corruption)
- Error Accumulation: `0.2` (minimal trails)
- DCT Corruption: `0.1` (very subtle artifacts)

### **Extreme Corruption**

- Keyframe Reset Rate: `0.0` (no resets)
- Motion Vector Corruption: `2.0` (maximum corruption)
- Error Accumulation: `1.0` (full accumulation)
- DCT Corruption: `1.0` (heavy compression artifacts)

## Performance Considerations

- **DCT Corruption**: Minimal performance impact (computed during encoding)
- **Motion Vector Corruption**: Very low impact (simple noise addition)
- **Error Accumulation**: Low impact (single lerp operation)
- **Keyframe Reset**: Minimal impact (simple threshold check)

All features are designed to maintain the high performance of the original Flux effect while adding authentic datamoshing capabilities.

## Comparison with Traditional Datamoshing

### **What Flux Now Provides:**

✅ **I-Frame/P-Frame Simulation** (Keyframe Reset)
✅ **Motion Vector Corruption** (Classic smearing)
✅ **Temporal Error Accumulation** (Trailing artifacts)
✅ **DCT Coefficient Corruption** (Compression artifacts)
✅ **Block-based Processing** (Authentic chunky artifacts)
✅ **Real-time Performance** (Unity 6 Render Graph optimized)

### **Traditional Video Datamoshing:**

- Requires pre-rendered video files
- Limited to offline processing
- Difficult to control in real-time
- Platform-dependent codec behavior

**Flux provides superior control and real-time performance while maintaining authentic datamosh aesthetics.**

---

## 🌟 **Enhanced Corruption Features (NEW)**

### **1. Enhanced Corruption Control**

- **Purpose**: Advanced corruption algorithms for realistic datamoshing effects
- **Implementation**: Multi-scale corruption with brightness-based masking
- **Use Cases**:
  - Realistic JPEG-style compression artifacts
  - Temporal error accumulation
  - Motion-based corruption patterns
- **Performance**: Optimized for real-time use

### **2. Chroma Corruption**

- **Purpose**: Separate corruption of RGB channels (chroma subsampling simulation)
- **Implementation**: Independent motion vector offsets for R, G, B channels
- **Effect**: Color bleeding and separation typical of compressed video
- **Values**:
  - `0.0`: No chroma corruption
  - `0.5`: Moderate color bleeding
  - `1.0`: Heavy color separation
- **Authentic**: Simulates real video compression chroma subsampling artifacts

### **3. Glitch Transitions**

- **Purpose**: Smooth animated transitions between clean and corrupted states
- **Implementation**: Radial transition effect from center outward
- **Effect**: Corruption spreads/contracts in smooth waves
- **Values**:
  - `0.0`: No transition effect
  - `0.5`: Transition halfway across screen
  - `1.0`: Full screen transition
- **Use Cases**: Cinematic corruption reveals, impact effects, scene transitions

### **4. Feedback Loops**

- **Purpose**: Routes output back as input for recursive corruption
- **Implementation**: Controlled feedback with spatial offsets to prevent infinite buildup
- **Effect**: Psychedelic, recursive visual patterns
- **Values**:
  - `0.0`: No feedback
  - `0.4`: Moderate recursive effects
  - `0.8`: Maximum safe feedback (clamped to prevent infinite loops)
- **Warning**: Use carefully - high values can create intense visual effects

### **5. Multi-Scale Corruption**

- **Purpose**: Different corruption types at multiple scales simultaneously
- **Implementation**:
  - Large scale (32x32): Color shifts
  - Medium scale (8x8): Brightness corruption
  - Small scale (2x2): Fine noise
- **Effect**: More complex, layered corruption patterns
- **Values**:
  - `0.0`: No multi-scale effects
  - `0.5`: Moderate multi-scale corruption
  - `1.0`: Heavy multi-scale artifacts
- **Result**: More realistic compression-like artifacts with multiple frequency components

---

## 🎯 **Complete Feature Matrix**

| Feature                | Purpose               | Performance Impact | Authenticity | Creative Potential |
| ---------------------- | --------------------- | ------------------ | ------------ | ------------------ |
| **Basic Datamoshing**  | Core reprojection     | Low                | High         | Medium             |
| **Keyframe Reset**     | I-Frame simulation    | Minimal            | High         | Medium             |
| **Motion Corruption**  | Classic smearing      | Minimal            | High         | High               |
| **Error Accumulation** | Temporal trails       | Low                | High         | High               |
| **DCT Corruption**     | Compression artifacts | Low                | High         | Medium             |
| **Chroma Corruption**  | Color bleeding        | Low                | High         | High               |
| **Glitch Transitions** | Animated effects      | Low                | Medium       | High               |
| **Feedback Loops**     | Recursive patterns    | Low                | Low          | High               |
| **Multi-Scale**        | Complex artifacts     | Medium             | High         | High               |

---

## 🎨 **Creative Combinations**

### **Classic VHS Datamosh**

```
Keyframe Reset: 0.05
Motion Corruption: 1.2
Error Accumulation: 0.6
Chroma Corruption: 0.4
Multi-Scale: 0.3
```

### **Digital Glitch Art**

```
Feedback Loops: 0.6
Multi-Scale: 0.8
DCT Corruption: 0.5
Glitch Transition: 0.7 (animated)
```

### **Cinematic Impact Effect**

```
Glitch Transition: 0.0 → 1.0 (animated)
Motion Corruption: 2.0
Error Accumulation: 0.8
Keyframe Reset: 0.0 (during effect)
```

### **Subtle Compression Artifacts**

```
DCT Corruption: 0.2
Chroma Corruption: 0.1
Multi-Scale: 0.2
All others: 0.0
```
