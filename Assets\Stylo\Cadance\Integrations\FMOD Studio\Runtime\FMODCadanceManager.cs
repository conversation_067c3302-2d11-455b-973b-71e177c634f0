using UnityEngine;
using System;
using System.Collections.Generic;
using FMOD.Studio;
using FMOD;
using Stylo.Cadance;
using FMODUnity;
using FMODCore = global::FMOD;
using FMODStudio = global::FMOD.Studio;

namespace Stylo.Cadance.FMOD
{
    /// <summary>
    /// Manages the integration between FMOD Studio and Cadance.
    /// Acts as the central coordinator for all FMOD-related events.
    /// </summary>
    [DefaultExecutionOrder(-190)] // Execute after Cadance but before other game systems
    public class FMODCadanceManager : MonoBehaviour
    {
        #region Singleton Pattern

        private static FMODCadanceManager _instance;

        /// <summary>
        /// Gets the singleton instance of FMODCadanceManager.
        /// </summary>
        public static FMODCadanceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    // Find or create an instance at runtime
                    _instance = FindFirstObjectByType<FMODCadanceManager>();

                    if (_instance == null)
                    {
                        // Find Cadance first
                        var cadance = Cadance.Instance;

                        // Create the manager as a child of Cadance
                        GameObject go = new GameObject("FMODCadanceManager");
                        go.transform.SetParent(cadance.transform);
                        _instance = go.AddComponent<FMODCadanceManager>();
                    }
                }
                return _instance;
            }
        }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            _instance = null;
            UnityEngine.Debug.Log("[FMODCadanceManager] Static data reset for domain reload");
        }

        #endregion

        #region FMOD Event Tracking

        // Active event trackers
        private Dictionary<string, FMODEventInstanceTracker> _eventTrackers = new Dictionary<string, FMODEventInstanceTracker>();

        // Event description cache for efficient lookups
        private Dictionary<string, EventDescription> _eventDescriptionCache = new Dictionary<string, EventDescription>();

        // Registered event paths
        private HashSet<string> _eventPaths = new HashSet<string>();

        #endregion

        #region Public API

        /// <summary>
        /// Checks if an event with the specified Cadance ID is registered.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID to check</param>
        /// <returns>True if the event is registered</returns>
        public bool HasEventRegistered(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
                return false;

            return _eventTrackers.ContainsKey(cadanceID);
        }

        /// <summary>
        /// Checks if a specific FMOD event path has been registered.
        /// </summary>
        /// <param name="eventPath">The FMOD event path to check</param>
        /// <returns>True if the event path is registered</returns>
        public bool HasFMODEventPathRegistered(string eventPath)
        {
            return _eventPaths != null && _eventPaths.Contains(eventPath);
        }

        /// <summary>
        /// Gets the number of loaded FMOD banks.
        /// </summary>
        /// <returns>The count of loaded banks, or 0 if FMOD is not initialized</returns>
        public int GetBanksLoaded()
        {
            try
            {
                // Check if we have any registered events as a proxy for banks being loaded
                if (_eventPaths.Count > 0)
                {
                    return 1; // At least one bank is loaded if we have registered events
                }

                return 0;
            }
            catch (System.Exception)
            {
                return 0; // If any error occurs, assume no banks are loaded
            }
        }

        /// <summary>
        /// Gets a dictionary of all registered FMOD events by their Cadance ID.
        /// </summary>
        /// <returns>Dictionary mapping Cadance IDs to FMOD event paths</returns>
        public Dictionary<string, string> GetRegisteredEvents()
        {
            Dictionary<string, string> events = new Dictionary<string, string>();
            foreach (var kvp in _eventTrackers)
            {
                if (kvp.Value != null)
                {
                    events[kvp.Key] = kvp.Value.FMODEventPath;
                }
            }
            return events;
        }

        /// <summary>
        /// Plays an FMOD event registered with the specified Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID of the event to play</param>
        /// <returns>True if the event was played successfully</returns>
        public bool PlayEvent(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
            {
                UnityEngine.Debug.LogError($"[FMODCadanceManager] Cannot play event with empty ID");
                return false;
            }

            UnityEngine.Debug.Log($"[FMODCadanceManager] *** PLAY EVENT DEBUG *** Attempting to play event with Cadance ID: '{cadanceID}'");

            FMODEventInstanceTracker tracker = GetEventTracker(cadanceID);
            if (tracker == null)
            {
                UnityEngine.Debug.LogWarning($"[FMODCadanceManager] No event registered with ID '{cadanceID}'");
                return false;
            }

            UnityEngine.Debug.Log($"[FMODCadanceManager] *** PLAY EVENT DEBUG *** Found tracker for '{cadanceID}', calling tracker.PlayEvent()");
            bool result = tracker.PlayEvent();
            UnityEngine.Debug.Log($"[FMODCadanceManager] *** PLAY EVENT DEBUG *** tracker.PlayEvent() returned: {result}");
            
            return result;
        }

        /// <summary>
        /// Stops an FMOD event registered with the specified Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID of the event to stop</param>
        /// <returns>True if the event was stopped successfully</returns>
        public bool StopEvent(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
            {
                UnityEngine.Debug.LogError($"[FMODCadanceManager] Cannot stop event with empty ID");
                return false;
            }

            FMODEventInstanceTracker tracker = GetEventTracker(cadanceID);
            if (tracker == null)
            {
                UnityEngine.Debug.LogWarning($"[FMODCadanceManager] No event registered with ID '{cadanceID}'");
                return false;
            }

            return tracker.StopEvent();
        }

        /// <summary>
        /// Gets the event tracker for the specified Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID of the event tracker to get</param>
        /// <returns>The event tracker, or null if not found</returns>
        public FMODEventInstanceTracker GetEventTracker(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
                return null;

            if (_eventTrackers.TryGetValue(cadanceID, out FMODEventInstanceTracker tracker))
                return tracker;

            return null;
        }

        /// <summary>
        /// Registers an FMOD event with Cadance for synchronization.
        /// </summary>
        /// <param name="cadanceID">The unique identifier for this Cadance</param>
        /// <param name="fmodEventPath">The FMOD event path</param>
        /// <param name="cadanceAsset">Optional CadanceAsset to associate with this event</param>
        /// <returns>True if registration was successful</returns>
        public bool RegisterFMODEvent(string cadanceID, string fmodEventPath, CadanceAsset cadanceAsset = null)
        {
            if (string.IsNullOrEmpty(cadanceID) || string.IsNullOrEmpty(fmodEventPath))
            {
                UnityEngine.Debug.LogError("[FMODCadanceManager] Cannot register with null or empty ID or event path");
                return false;
            }

            if (_eventTrackers.ContainsKey(cadanceID))
            {
                UnityEngine.Debug.LogWarning($"[FMODCadanceManager] Cadance ID '{cadanceID}' is already registered");
                return false;
            }

            try
            {
                // CRITICAL FIX: Instead of creating a new FMOD event instance,
                // find existing StudioEventEmitter instances that match this event path
                EventInstance existingInstance = FindExistingEventInstance(fmodEventPath);
                
                if (!existingInstance.isValid())
                {
                    UnityEngine.Debug.LogWarning($"[FMODCadanceManager] No existing FMOD event instance found for '{fmodEventPath}'. The music might be played by a StudioEventEmitter component.");
                    
                    // Try to get the event description for future monitoring
                    EventDescription eventDescription;
                    if (!_eventDescriptionCache.TryGetValue(fmodEventPath, out eventDescription))
                    {
                        FMODCore.RESULT result = RuntimeManager.StudioSystem.getEvent(fmodEventPath, out eventDescription);
                        if (result != FMODCore.RESULT.OK || !eventDescription.isValid())
                        {
                            UnityEngine.Debug.LogError($"[FMODCadanceManager] Failed to get event description for '{fmodEventPath}': {result}");
                            return false;
                        }
                        _eventDescriptionCache[fmodEventPath] = eventDescription;
                    }
                    
                    // Create a placeholder tracker that will monitor for when the event starts playing
                    return CreateMonitoringTracker(cadanceID, fmodEventPath, cadanceAsset);
                }

                // Create a tracker for the existing instance
                GameObject trackerObject = new GameObject($"CadanceTracker_{cadanceID}");
                trackerObject.transform.SetParent(transform);

                FMODEventInstanceTracker tracker = trackerObject.AddComponent<FMODEventInstanceTracker>();
                if (!tracker.Initialize(existingInstance, cadanceID, fmodEventPath))
                {
                    Destroy(trackerObject);
                    return false;
                }

                // Load the CadanceAsset if provided
                if (cadanceAsset != null)
                {
                    Cadance.Instance.LoadCadance(cadanceAsset);
                    
                    // Load events from CadanceAsset into the tracker
                    LoadCadanceEventsIntoTracker(tracker, cadanceAsset);
                    
                    UnityEngine.Debug.Log($"[FMODCadanceManager] Loaded CadanceAsset '{cadanceAsset.name}' for existing event '{fmodEventPath}'");
                }

                _eventTrackers[cadanceID] = tracker;

                UnityEngine.Debug.Log($"[FMODCadanceManager] Successfully registered existing FMOD event '{fmodEventPath}' with Cadance ID '{cadanceID}'");
                return true;
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[FMODCadanceManager] Error registering FMOD event: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unregisters an FMOD event from Cadance.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID to unregister</param>
        public void UnregisterFMODEvent(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
                return;

            if (_eventTrackers.TryGetValue(cadanceID, out FMODEventInstanceTracker tracker))
            {
                tracker.Cleanup();
                Destroy(tracker.gameObject);
                _eventTrackers.Remove(cadanceID);

                UnityEngine.Debug.Log($"[FMODCadanceManager] Unregistered Cadance ID '{cadanceID}'");
            }
        }

        /// <summary>
        /// Adds a Cadance event to be triggered at the specified sample time.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <param name="eventID">The event ID</param>
        /// <param name="sampleTime">The sample time at which this event should trigger</param>
        /// <param name="payload">Optional payload data</param>
        /// <returns>True if the event was added successfully</returns>
        public bool AddCadanceEvent(string cadanceID, string eventID, int sampleTime, string payload = "")
        {
            if (string.IsNullOrEmpty(cadanceID) || string.IsNullOrEmpty(eventID))
                return false;

            if (_eventTrackers.TryGetValue(cadanceID, out FMODEventInstanceTracker tracker))
            {
                tracker.AddEvent(eventID, sampleTime, payload);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Clears all events with the specified ID from the given Cadance.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <param name="eventID">The event ID to clear</param>
        public void ClearCadanceEvents(string cadanceID, string eventID)
        {
            if (string.IsNullOrEmpty(cadanceID) || string.IsNullOrEmpty(eventID))
                return;

            if (_eventTrackers.TryGetValue(cadanceID, out FMODEventInstanceTracker tracker))
            {
                tracker.ClearEventsWithID(eventID);
            }
        }

        /// <summary>
        /// Gets the tracker for the specified Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <returns>The tracker, or null if not found</returns>
        public FMODEventInstanceTracker GetTracker(string cadanceID)
        {
            if (string.IsNullOrEmpty(cadanceID))
                return null;

            _eventTrackers.TryGetValue(cadanceID, out FMODEventInstanceTracker tracker);
            return tracker;
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            UnityEngine.Debug.Log("[Cadance] FMODCadanceManager Initialized");
        }

        private void Update()
        {
            // Update all active trackers
            foreach (var tracker in _eventTrackers.Values)
            {
                tracker.Update();
            }
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                // Clean up all trackers
                foreach (var tracker in _eventTrackers.Values)
                {
                    if (tracker != null)
                    {
                        tracker.Cleanup();
                    }
                }

                _eventTrackers.Clear();
                _eventDescriptionCache.Clear();
                _instance = null;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Loads events from a CadanceAsset into the specified tracker.
        /// This is the critical missing piece that connects CadanceAsset events to FMOD tracking.
        /// </summary>
        /// <param name="tracker">The tracker to load events into</param>
        /// <param name="cadanceAsset">The CadanceAsset containing the events</param>
        private void LoadCadanceEventsIntoTracker(FMODEventInstanceTracker tracker, CadanceAsset cadanceAsset)
        {
            if (tracker == null || cadanceAsset == null)
                return;

            int totalEventsLoaded = 0;

            // Iterate through all tracks in the CadanceAsset
            foreach (var track in cadanceAsset.Tracks)
            {
                if (track == null || string.IsNullOrEmpty(track.EventID))
                    continue;

                // Get all events from this track using the EventCount and GetEventAtIndex methods
                int eventCount = track.EventCount;
                for (int i = 0; i < eventCount; i++)
                {
                    var cadanceEvent = track.GetEventAtIndex(i);
                    if (cadanceEvent != null)
                    {
                        // Add each event to the tracker
                        tracker.AddEvent(cadanceEvent.EventID, cadanceEvent.StartSample, cadanceEvent.Payload?.ToString() ?? "");
                        totalEventsLoaded++;
                    }
                }

                UnityEngine.Debug.Log($"[FMODCadanceManager] Loaded {eventCount} '{track.EventID}' events from track into tracker");
            }

            UnityEngine.Debug.Log($"[FMODCadanceManager] Successfully loaded {totalEventsLoaded} total events from CadanceAsset '{cadanceAsset.name}' into tracker");
        }

        /// <summary>
        /// Finds an existing FMOD event instance that matches the specified event path.
        /// This looks for instances created by StudioEventEmitter components or other FMOD systems.
        /// </summary>
        /// <param name="eventPath">The FMOD event path to search for</param>
        /// <returns>The existing event instance, or an invalid instance if not found</returns>
        private EventInstance FindExistingEventInstance(string eventPath)
        {
            try
            {
                // First, try to find StudioEventEmitter components that might be playing this event
                var emitters = FindObjectsByType<FMODUnity.StudioEventEmitter>(FindObjectsSortMode.None);
                
                foreach (var emitter in emitters)
                {
                    if (emitter.EventReference.Path == eventPath)
                    {
                        // Check if this emitter has an active event instance
                        var eventInstance = emitter.EventInstance;
                        if (eventInstance.isValid())
                        {
                            // Check if it's playing
                            eventInstance.getPlaybackState(out FMODStudio.PLAYBACK_STATE state);
                            if (state == FMODStudio.PLAYBACK_STATE.PLAYING || state == FMODStudio.PLAYBACK_STATE.STARTING)
                            {
                                UnityEngine.Debug.Log($"[FMODCadanceManager] Found existing playing FMOD event instance from StudioEventEmitter on '{emitter.gameObject.name}'");
                                return eventInstance;
                            }
                        }
                    }
                }

                // If no StudioEventEmitter found, we could potentially scan all active event instances
                // but that's more complex and may not be necessary for most use cases
                
                UnityEngine.Debug.Log($"[FMODCadanceManager] No existing event instance found for '{eventPath}'");
                return new EventInstance(); // Invalid instance
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[FMODCadanceManager] Error finding existing event instance: {ex.Message}");
                return new EventInstance(); // Invalid instance
            }
        }

        /// <summary>
        /// Creates a monitoring tracker that will watch for when the specified event starts playing.
        /// This is used when no existing event instance is found but we want to be ready when it starts.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <param name="fmodEventPath">The FMOD event path</param>
        /// <param name="cadanceAsset">Optional CadanceAsset to load</param>
        /// <returns>True if the monitoring tracker was created successfully</returns>
        private bool CreateMonitoringTracker(string cadanceID, string fmodEventPath, CadanceAsset cadanceAsset)
        {
            // For now, we'll create a tracker with an invalid instance and periodically check for active instances
            // This is a simplified approach - a more robust solution would use FMOD callbacks
            
            GameObject trackerObject = new GameObject($"CadanceMonitor_{cadanceID}");
            trackerObject.transform.SetParent(transform);

            var monitor = trackerObject.AddComponent<FMODEventMonitor>();
            monitor.Initialize(cadanceID, fmodEventPath, cadanceAsset, this);

            UnityEngine.Debug.Log($"[FMODCadanceManager] Created monitoring tracker for '{fmodEventPath}' - will watch for when event starts playing");
            return true;
        }

        /// <summary>
        /// Called by FMODEventMonitor when it detects that an event has started playing.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <param name="eventInstance">The detected event instance</param>
        /// <param name="cadanceAsset">The CadanceAsset to load</param>
        internal void OnEventInstanceDetected(string cadanceID, EventInstance eventInstance, CadanceAsset cadanceAsset)
        {
            if (_eventTrackers.ContainsKey(cadanceID))
            {
                UnityEngine.Debug.LogWarning($"[FMODCadanceManager] Event instance detected for '{cadanceID}' but tracker already exists");
                return;
            }

            // Create a proper tracker for the detected instance
            GameObject trackerObject = new GameObject($"CadanceTracker_{cadanceID}");
            trackerObject.transform.SetParent(transform);

            FMODEventInstanceTracker tracker = trackerObject.AddComponent<FMODEventInstanceTracker>();
            if (tracker.Initialize(eventInstance, cadanceID, ""))
            {
                if (cadanceAsset != null)
                {
                    LoadCadanceEventsIntoTracker(tracker, cadanceAsset);
                }
                
                _eventTrackers[cadanceID] = tracker;
                UnityEngine.Debug.Log($"[FMODCadanceManager] Successfully created tracker for detected event instance: {cadanceID}");
            }
            else
            {
                Destroy(trackerObject);
                UnityEngine.Debug.LogError($"[FMODCadanceManager] Failed to initialize tracker for detected event instance: {cadanceID}");
            }
        }

        #endregion
    }
}
