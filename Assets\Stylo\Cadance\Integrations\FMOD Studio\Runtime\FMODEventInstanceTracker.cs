using UnityEngine;
using System;
using System.Collections.Generic;
using FMOD.Studio;
using FMOD;
using FMODCore = global::FMOD;
using FMODStudio = global::FMOD.Studio;

namespace Stylo.Cadance.FMOD
{
    /// <summary>
    /// Tracks FMOD Studio event instances and extracts timing information for Cadance.
    /// </summary>
    public class FMODEventInstanceTracker : MonoBehaviour
    {
        #region Properties and Fields

        // The FMOD event instance to track
        private EventInstance _eventInstance;

        // The FMOD event description associated with this instance
        private EventDescription _eventDescription;

        // The Cadance ID for this tracker
        private string _cadanceID;

        // The FMOD event path
        private string _fmodEventPath;

        // Sample rate for timing calculations
        private int _sampleRate = 48000;

        // Event tracking
        private Dictionary<string, List<CadanceEvent>> _eventTracks = new Dictionary<string, List<CadanceEvent>>();

        // Playback tracking
        private int _lastReportedPosition = 0;
        private bool _isPlaying = false;

        // Timing data caching
        private readonly List<int> _processedPositions = new List<int>();

        public string CadanceID => _cadanceID;

        public string FMODEventPath => _fmodEventPath;

        public bool IsValid => _eventInstance.isValid();

        public bool IsPlaying => _isPlaying;

        /// <summary>
        /// Gets the current sample position of the FMOD event.
        /// </summary>
        public int CurrentSamplePosition
        {
            get
            {
                if (!IsValid) return 0;

                _eventInstance.getTimelinePosition(out int currentPosition);
                return MillisecondsToSamples(currentPosition);
            }
        }

        /// <summary>
        /// Gets the total length of the FMOD event in samples.
        /// </summary>
        public int TotalSampleLength
        {
            get
            {
                if (!IsValid || !_eventDescription.isValid()) return 0;

                _eventDescription.getLength(out int lengthMs);
                return MillisecondsToSamples(lengthMs);
            }
        }

        /// <summary>
        /// Gets the current pitch of the FMOD event.
        /// </summary>
        public float CurrentPitch
        {
            get
            {
                if (!IsValid) return 1f;

                _eventInstance.getPitch(out float pitch);
                return pitch;
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes this tracker with the specified FMOD event instance.
        /// </summary>
        /// <param name="eventInstance">The FMOD event instance to track</param>
        /// <param name="cadanceID">The Cadance ID to associate with this tracker</param>
        /// <param name="fmodEventPath">The FMOD event path</param>
        /// <returns>True if initialization was successful</returns>
        public bool Initialize(EventInstance eventInstance, string cadanceID, string fmodEventPath)
        {
            if (!eventInstance.isValid())
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] Cannot initialize with invalid event instance for Cadance ID: {cadanceID}");
                return false;
            }

            _eventInstance = eventInstance;
            _cadanceID = cadanceID;
            _fmodEventPath = fmodEventPath;

            FMODCore.RESULT result = _eventInstance.getDescription(out _eventDescription);
            if (result != FMODCore.RESULT.OK)
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] Failed to get event description: {result}");
                return false;
            }

            // Get the sample rate from the FMOD system
            result = FMODUnity.RuntimeManager.CoreSystem.getSoftwareFormat(out _sampleRate, out _, out _);
            if (result != FMODCore.RESULT.OK)
            {
                UnityEngine.Debug.LogWarning($"[FMODEventInstanceTracker] Failed to get sample rate; using default: {_sampleRate}");
                _sampleRate = 48000; // Default to 48kHz
            }

            return true;
        }

        #endregion

        #region Event Management

        /// <summary>
        /// Plays the FMOD event instance being tracked.
        /// </summary>
        /// <returns>True if the event was successfully played</returns>
        public bool PlayEvent()
        {
            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** PlayEvent called for Cadance ID: {_cadanceID}");
            
            if (!_eventInstance.isValid())
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** Cannot play invalid event instance for Cadance ID: {_cadanceID}");
                return false;
            }

            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** Event instance is valid, calling _eventInstance.start()");
            
            FMODCore.RESULT result = _eventInstance.start();
            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** _eventInstance.start() returned: {result}");
            
            if (result != FMODCore.RESULT.OK)
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** Failed to play event: {result}");
                return false;
            }

            _isPlaying = true;
            _lastReportedPosition = 0;

            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** PLAY EVENT DEBUG *** Event started successfully for Cadance ID: {_cadanceID}");
            return true;
        }

        /// <summary>
        /// Stops the FMOD event instance being tracked.
        /// </summary>
        /// <returns>True if the event was successfully stopped</returns>
        public bool StopEvent()
        {
            if (!_eventInstance.isValid())
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] Cannot stop invalid event instance for Cadance ID: {_cadanceID}");
                return false;
            }

            FMODCore.RESULT result = _eventInstance.stop(FMODStudio.STOP_MODE.ALLOWFADEOUT);
            if (result != FMODCore.RESULT.OK)
            {
                UnityEngine.Debug.LogError($"[FMODEventInstanceTracker] Failed to stop event: {result}");
                return false;
            }

            _isPlaying = false;

            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] Event stopped for Cadance ID: {_cadanceID}");
            return true;
        }

        /// <summary>
        /// Adds a Cadance event to be tracked with this FMOD instance.
        /// </summary>
        /// <param name="eventID">The event ID</param>
        /// <param name="sampleTime">The sample time at which this event occurs</param>
        /// <param name="payload">Optional payload data</param>
        public void AddEvent(string eventID, int sampleTime, string payload = "")
        {
            if (string.IsNullOrEmpty(eventID))
                return;

            if (!_eventTracks.ContainsKey(eventID))
            {
                _eventTracks[eventID] = new List<CadanceEvent>();
            }

            var newEvent = new CadanceEvent(eventID, sampleTime, payload);
            _eventTracks[eventID].Add(newEvent);

            // Ensure events are sorted by sample time
            _eventTracks[eventID].Sort((a, b) => a.SampleTime.CompareTo(b.SampleTime));
        }

        /// <summary>
        /// Removes all events with the specified ID.
        /// </summary>
        /// <param name="eventID">The event ID to remove</param>
        public void ClearEventsWithID(string eventID)
        {
            if (_eventTracks.ContainsKey(eventID))
            {
                _eventTracks.Remove(eventID);
            }
        }

        /// <summary>
        /// Removes all tracked events.
        /// </summary>
        public void ClearAllEvents()
        {
            _eventTracks.Clear();
        }

        #endregion

        #region Update and Event Tracking

        /// <summary>
        /// Updates this tracker, processes events, and dispatches any triggered events.
        /// </summary>
        public void Update()
        {
            if (!IsValid)
                return;

            // Check playback state
            _eventInstance.getPlaybackState(out FMODStudio.PLAYBACK_STATE playbackState);
            _isPlaying = playbackState == FMODStudio.PLAYBACK_STATE.PLAYING;

            // Only log when state changes or every 60 frames to reduce spam
            if (Time.frameCount % 60 == 0 || (_isPlaying != (_lastReportedPosition > 0)))
            {
                UnityEngine.Debug.Log($"[FMODEventInstanceTracker] Cadance ID: {_cadanceID}, PlaybackState: {playbackState}, IsPlaying: {_isPlaying}");
            }

            if (!_isPlaying)
                return;

            // Get current playback position
            _eventInstance.getTimelinePosition(out int currentPosition);

            // Convert from milliseconds to samples
            int currentSamplePosition = MillisecondsToSamples(currentPosition);

            if (currentSamplePosition == _lastReportedPosition)
                return;

            // Calculate delta time slice
            DeltaSlice deltaSlice = new DeltaSlice(_lastReportedPosition, currentSamplePosition - _lastReportedPosition);

            UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** PROCESSING EVENTS *** Delta slice [{deltaSlice.StartSample}, {deltaSlice.EndSample}] with {_eventTracks.Count} event tracks");

            // Process all events within the current delta slice
            ProcessEvents(deltaSlice);

            // Update last position
            _lastReportedPosition = currentSamplePosition;
        }

        /// <summary>
        /// Processes all events that fall within the specified delta slice.
        /// </summary>
        /// <param name="deltaSlice">The delta slice to process</param>
        private void ProcessEvents(DeltaSlice deltaSlice)
        {
            _processedPositions.Clear();

            foreach (var eventTrackPair in _eventTracks)
            {
                string eventID = eventTrackPair.Key;
                List<CadanceEvent> events = eventTrackPair.Value;

                foreach (var evt in events)
                {
                    // Check if this event falls within the current delta slice
                    if (deltaSlice.ContainsSamplePosition(evt.SampleTime) && !_processedPositions.Contains(evt.SampleTime))
                    {
                        // Add comprehensive debugging
                        UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** EVENT TRIGGER DEBUG *** Event '{evt.EventID}' at sample {evt.SampleTime} within delta [{deltaSlice.StartSample}, {deltaSlice.EndSample}]");
                        
                        // Trigger the event through the Cadance system
                        Cadance.Instance?.TriggerEvent(evt);
                        _processedPositions.Add(evt.SampleTime);
                        
                        UnityEngine.Debug.Log($"[FMODEventInstanceTracker] *** EVENT TRIGGERED *** Event '{evt.EventID}' successfully triggered");
                    }
                }
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Converts milliseconds to sample count.
        /// </summary>
        /// <param name="milliseconds">The time in milliseconds</param>
        /// <returns>The equivalent sample count</returns>
        private int MillisecondsToSamples(int milliseconds)
        {
            return (int)((milliseconds / 1000.0f) * _sampleRate);
        }

        /// <summary>
        /// Converts sample count to milliseconds.
        /// </summary>
        /// <param name="samples">The sample count</param>
        /// <returns>The equivalent time in milliseconds</returns>
        private int SamplesToMilliseconds(int samples)
        {
            return (int)((samples / (float)_sampleRate) * 1000);
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Performs cleanup when this tracker is destroyed.
        /// </summary>
        public void Cleanup()
        {
            ClearAllEvents();
            _eventTracks = null;
            _processedPositions.Clear();
        }

        #endregion
    }
}
