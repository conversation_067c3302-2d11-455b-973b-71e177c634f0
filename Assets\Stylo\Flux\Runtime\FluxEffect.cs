#if URP_INSTALLED
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System;

namespace Stylo.Flux.Universal
{
    [System.Serializable]
    [VolumeComponentMenu("Post-processing/Flux")]
    public sealed class FluxEffect : VolumeComponent, IPostProcessComponent
    {
        [Header("🔧 Master Controls")]
        [Tooltip("Master control for overall effect intensity")]
        public ClampedFloatParameter EffectIntensity = new ClampedFloatParameter(0.35f, 0f, 1f);
        
        [Tooltip("Apply effect only to objects marked with stencil")]
        public BoolParameter OnlyStenciled = new BoolParameter(false);

        [Header("🎯 TRUE DATAMOSHING")]
        [Header("Compression Encoding")]
        [Tooltip("JPEG-style color quantization")]
        public ClampedFloatParameter ColorCrunch = new ClampedFloatParameter(0.5f, 0f, 1f);
        
        [Tooltip("Resolution division before processing")]
        public ClampedIntParameter Downscaling = new ClampedIntParameter(10, 1, 10);
        
        [Tooltip("Compression block size")]
        public FluxBlockSizeParameter BlockSize = new FluxBlockSizeParameter(_BlockSize._16x16);
        
        [Tooltip("Don't apply compression to skybox")]
        public BoolParameter DontCrunchSkybox = new BoolParameter(true);

        [Header("Motion Vector Reprojection")]
        [Tooltip("Random reprojection chance")]
        public ClampedFloatParameter ReprojectBaseNoise = new ClampedFloatParameter(0.1f, 0f, 1f);
        
        [Tooltip("Base reroll speed")]
        public ClampedFloatParameter ReprojectBaseRerollSpeed = new ClampedFloatParameter(3f, 0f, 20f);
        
        [Tooltip("Motion vector influence on reprojection")]
        public ClampedFloatParameter ReprojectLengthInfluence = new ClampedFloatParameter(0.1f, 0f, 5f);

        [Header("Authentic Compression Artifacts")]
        [Tooltip("I-frame simulation frequency")]
        public ClampedFloatParameter KeyframeResetRate = new ClampedFloatParameter(0.02f, 0f, 1f);
        
        [Tooltip("Motion vector noise")]
        public ClampedFloatParameter MotionVectorCorruption = new ClampedFloatParameter(0.5f, 0f, 2f);
        
        [Tooltip("Temporal error buildup")]
        public ClampedFloatParameter ErrorAccumulation = new ClampedFloatParameter(0.3f, 0f, 1f);
        
        [Tooltip("Frequency coefficient corruption")]
        public ClampedFloatParameter DCTCorruption = new ClampedFloatParameter(0f, 0f, 1f);
        
        [Tooltip("RGB channel separation")]
        public ClampedFloatParameter ChromaCorruption = new ClampedFloatParameter(0f, 0f, 1f);

        [Header("🌟 ENHANCED VISUAL EFFECTS")]
        [Header("Consolidated Motion Processing")]
        [Tooltip("Master motion sensitivity (replaces Camera Motion Amplification, Pixel Flow Intensity)")]
        public ClampedFloatParameter MotionAmplification = new ClampedFloatParameter(3f, 0f, 10f);
        
        [Tooltip("Minimum motion to trigger effects")]
        public ClampedFloatParameter MotionThreshold = new ClampedFloatParameter(0.001f, 0f, 0.1f);
        
        [Tooltip("0=objects only, 1=camera only")]
        public ClampedFloatParameter CameraObjectMotionBalance = new ClampedFloatParameter(0.3f, 0f, 1f);
        
        [Tooltip("Reduces jittery motion")]
        public ClampedFloatParameter MotionSmoothing = new ClampedFloatParameter(0.1f, 0f, 1f);

        [Header("Pixel Flow & Trailing")]
        [Tooltip("Primary trail strength (replaces Pixel Flow Intensity)")]
        public ClampedFloatParameter TrailIntensity = new ClampedFloatParameter(2f, 0f, 5f);
        
        [Tooltip("Blocky (0) vs smooth (1) trails")]
        public ClampedFloatParameter TrailSmoothness = new ClampedFloatParameter(0.5f, 0f, 1f);
        
        [Tooltip("How long trails last (replaces Motion Persistence + Temporal Accumulation)")]
        public ClampedFloatParameter TrailPersistence = new ClampedFloatParameter(0.8f, 0f, 1f);
        
        [Tooltip("Organic flow patterns around motion (replaces Flow Gradient)")]
        public ClampedFloatParameter FlowSpread = new ClampedFloatParameter(2f, 0f, 5f);

        [Header("Artistic Effects")]
        [Tooltip("Glitch transition")]
        public ClampedFloatParameter GlitchTransition = new ClampedFloatParameter(0f, 0f, 1f);
        
        [Tooltip("Feedback intensity")]
        public ClampedFloatParameter FeedbackIntensity = new ClampedFloatParameter(0f, 0f, 0.8f);
        
        [Tooltip("Multiple corruption scales")]
        public ClampedFloatParameter MultiScaleCorruption = new ClampedFloatParameter(0f, 0f, 1f);

        [Header("JPEG Quality Control")]
        [Tooltip("Compression quality (lower = more artifacts)")]
        public ClampedFloatParameter JPEGQuality = new ClampedFloatParameter(50f, 1f, 100f);
        
        [Tooltip("Brightness channel quantization")]
        public ClampedFloatParameter LuminanceQuantization = new ClampedFloatParameter(0.5f, 0f, 2f);
        
        [Tooltip("Color channel quantization")]
        public ClampedFloatParameter ChrominanceQuantization = new ClampedFloatParameter(0.7f, 0f, 2f);
        
        [Tooltip("4:2:0 chroma subsampling")]
        public BoolParameter ChromaSubsampling = new BoolParameter(false);

        [Header("Compression Artifacts")]
        [Tooltip("Edge ringing simulation")]
        public ClampedFloatParameter RingingArtifacts = new ClampedFloatParameter(0f, 0f, 1f);
        
        [Tooltip("High-frequency noise around edges")]
        public ClampedFloatParameter MosquitoNoise = new ClampedFloatParameter(0f, 0f, 1f);
        
        [Tooltip("Edge sensitivity")]
        public ClampedFloatParameter EdgeSensitivity = new ClampedFloatParameter(0.5f, 0.1f, 2f);

        [Header("💡 Brightness Control")]
        [Tooltip("Noise transparency")]
        public ClampedFloatParameter NoiseTransparency = new ClampedFloatParameter(0.05f, 0f, 0.5f);
        
        [Tooltip("Maximum noise brightness")]
        public ClampedFloatParameter MaxNoiseBrightness = new ClampedFloatParameter(0.9f, 0f, 1f);
        
        [Tooltip("Brightness threshold")]
        public ClampedFloatParameter BrightnessThreshold = new ClampedFloatParameter(0.7f, 0f, 1f);
        
        [Tooltip("Bright area masking")]
        public ClampedFloatParameter BrightAreaMasking = new ClampedFloatParameter(0.8f, 0f, 1f);

        [Header("🔧 Utility Controls")]
        [Tooltip("Oversharpening")]
        public ClampedFloatParameter Oversharpening = new ClampedFloatParameter(0f, 0f, 10f);
        
        [Tooltip("Visualize motion vectors")]
        public BoolParameter VisualizeMotionVectors = new BoolParameter(false);
        
        [Tooltip("Debug compression artifacts")]
        public BoolParameter DebugCompressionArtifacts = new BoolParameter(false);

        public enum _BlockSize
        {
            _2x2 = 0,
            _4x4 = 1,
            _8x8 = 2,
            _16x16 = 3,
            _32x32 = 4
        }

        [Serializable]
        public sealed class FluxBlockSizeParameter : VolumeParameter<_BlockSize>
        {
            public FluxBlockSizeParameter(_BlockSize value, bool overrideState = false) : base(value, overrideState) { }
        }

        public bool IsActive() => EffectIntensity.value > 0f;
        public bool IsTileCompatible() => false;

        /// <summary>
        /// Validates parameters for conflicts and performance issues
        /// </summary>
        public string ValidateParameters()
        {
            var warnings = new System.Collections.Generic.List<string>();
            
            // Motion system validation
            bool hasMotionFeatures = MotionAmplification.value > 0f || TrailIntensity.value > 0f;
            bool hasReprojection = ReprojectBaseNoise.value > 0f || ReprojectLengthInfluence.value > 0f;
            
            if (hasMotionFeatures && !hasReprojection)
            {
                warnings.Add("Motion features enabled but reprojection disabled - trails may not work properly");
            }
            
            // Performance warnings
            if (BlockSize.value == _BlockSize._32x32 && Downscaling.value < 5)
            {
                warnings.Add("32x32 blocks with low downscaling - severe performance impact");
            }
            
            if (BlockSize.value == _BlockSize._16x16 && Downscaling.value < 3)
            {
                warnings.Add("16x16 blocks with low downscaling - potential performance issues");
            }
            
            return warnings.Count > 0 ? string.Join("\n", warnings) : string.Empty;
        }

        /// <summary>
        /// Auto-fixes common parameter conflicts
        /// </summary>
        public string AutoFixParameters()
        {
            var fixes = new System.Collections.Generic.List<string>();
            
            // Auto-enable reprojection if motion features are used
            bool hasMotionFeatures = MotionAmplification.value > 0f || TrailIntensity.value > 0f;
            bool hasReprojection = ReprojectBaseNoise.value > 0f || ReprojectLengthInfluence.value > 0f;
            
            if (hasMotionFeatures && !hasReprojection)
            {
                ReprojectBaseNoise.value = 0.1f;
                ReprojectBaseNoise.overrideState = true;
                fixes.Add("Enabled Base Noise (0.1) to support motion features");
            }
            
            // Auto-adjust dangerous performance combinations
            if (BlockSize.value == _BlockSize._32x32 && Downscaling.value < 5)
            {
                Downscaling.value = 6;
                Downscaling.overrideState = true;
                fixes.Add("Increased downscaling to 6 for better 32x32 block performance");
            }
            
            if (BlockSize.value == _BlockSize._16x16 && Downscaling.value < 3)
            {
                Downscaling.value = 4;
                Downscaling.overrideState = true;
                fixes.Add("Increased downscaling to 4 for better 16x16 block performance");
            }
            
            return fixes.Count > 0 ? string.Join("\n", fixes) : string.Empty;
        }
    }
}
#endif