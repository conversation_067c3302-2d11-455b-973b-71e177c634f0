﻿using System;
using System.Collections;
using System.Collections.Generic;
using UltimateSpawner.Spawning;
using UnityEngine;
using XNode;
using Random = UnityEngine.Random;
using ZLinq; // For AsValueEnumerable() and zLinq extension methods

namespace UltimateSpawner.Waves
{
    public enum WaveContinueMode
    {
        Never = 0,
        Instant,
        WhenAllDead,
        WhenAllSpawned,
        MinimumDead,
    }

    public enum WaveCounterMode
    {
        OnWaveStart,
        OnWaveEnd,
    }

    [Serializable]
    [NodeTint(200, 200, 240)]
    [CreateNodeMenu("Waves/Wave")]
    public sealed class WaveMasterNode : WaveSpawnNode
    {
        // Private
        private HashSet<IEnumerator> runningSubWaves = new HashSet<IEnumerator>();
        
        // Cache for performance - avoid repeated allocations
        private List<WaveSubNode> cachedSubNodes = new List<WaveSubNode>();
        private WaveSubNode[] cachedSubNodesArray = new WaveSubNode[0];
        
        // Performance optimization: check alive status less frequently
        private int aliveCheckFrameCounter = 0;
        private const int ALIVE_CHECK_FREQUENCY = 10; // Check every 10 frames instead of every frame

        // Public
        public const string subWavesPortName = "SubWaves";

        [Input(ShowBackingValue.Never)]
        public WaveNode In;

        [Output]
        public WaveNode Out;

        [Output]
        public WaveSubNode SubWaves;

        public bool advanceWaveCounter = true;

        [DisplayCondition("advanceWaveCounter", true)]
        public WaveCounterMode waveCounterMode = WaveCounterMode.OnWaveStart;

        public WaveContinueMode continueMode = WaveContinueMode.WhenAllDead;

        [Tooltip("When enabled, the spawn controller will wait for the item spawn request to be completed before continuing. This make take some time as the target spawn point may be occupied")]
        public bool waitForItemSpawn = true;

        public int minimumKillCount = 3; // Added this line

        // Properties
        public override string NodeDisplayName
        {
            get { return "Wave"; }
        }

        // Methods
        public override bool CanConnectTo(NodePort from, NodePort to)
        {
            if (from.fieldName == subWavesPortName)
            {
                if (to.ValueType == typeof(WaveSubNode))
                {
                    return true;
                }
                return false;
            }

            return base.CanConnectTo(from, to);
        }

        public override bool CanHaveMultipleConnections(NodePort from)
        {
            if (from.fieldName == subWavesPortName)
            {
                return true;
            }

            return base.CanHaveMultipleConnections(from);
        }

        public override IEnumerator Evaluate(WaveSpawnController controller)
        {
            // Get the previous wave state
            WaveState previousState = controller.CurrentState;

            // Enter wave node
            controller.CurrentNode = this;
            controller.CurrentNodeType = WaveSpawnController.WaveNodeType.Wave;

            // Try to find the spawner
            Spawner targetSpawner = ResolveTargetSpawner(controller);

            // Try to find the spawnable item
            SpawnableItemRef targetItem = ResolveTargetSpawnableItemMultiple(controller, targetSpawner); //ResolveTargetSpawnableItem(controller);

            if (targetItem != null)
                UltimateSpawning.Log("using spawnable: " + targetItem.Name);

            // Get spawn count
            targetSpawnCount = GetInputValue(spawnCountField, spawnCount);

            // Check for multiplier mode
            if (waveMode == WaveSetupMode.Multiplier && previousState != null)
                targetSpawnCount = (int)(previousState.WaveSpawnCount * spawnCountMultiplier);

            targetSpawnFrequency = GetInputValue(spawnFrequencyField, spawnFrequency) * spawnFrequencyMultiplier;
            targetSpawnRandomness = GetInputValue(spawnRandomnessField, spawnRandomness) * spawnRandomnessMultiplier;


            // Move to the next wave state
            WaveState nextWave = WaveState.AdvanceWave(controller.CurrentState,
                name,
                targetSpawnCount,
                targetSpawnFrequency,
                targetSpawnRandomness,
                targetSpawner,
                targetItem);

            // Advance the wave counter
            if ((advanceWaveCounter == true && waveCounterMode == WaveCounterMode.OnWaveStart) || nextWave.WaveNumber == 0)
                nextWave.AdvanceWaveCounter();

            // Infom the controller of the wave change
            controller.SetNextWaveState(nextWave);

            // Invoke start event
            controller.OnWaveStarted.Invoke();


            // Track all spawned items this wave
            List<SpawnedItem> spawnedItems = new List<SpawnedItem>();

            // Start all subroutines
            foreach (WaveSubNode subNode in GetConnectedSubWaves())
            {
                nextWave.AddSubWave("Sub Wave", subNode.spawnCount, subNode.spawnFrequency, subNode.spawnRandomness);

                // Update sub wave
                subNode.UpdateState(previousState, spawnedItems, waitForItemSpawn);

                // Get the coroutine enumerator
                IEnumerator routine = subNode.Evaluate(controller);

                // Run the routine until completion - dont yield because we need to run spawning logic in this routine at the same time
                controller.StartCoroutine(EvaluateSubRoutine(controller, routine));
            }



            for (int i = 0; i < targetSpawnCount; i++)
            {
                // Wait a random amount of time
                float delay = targetSpawnFrequency + Random.Range(0, targetSpawnRandomness);

                // Wait for time to pass
                yield return WaitForSecondsNonAlloc.WaitFor(delay);



                // Try to spawn and item
                IEnumerator itemSpawnRoutine = controller.ItemSpawnRoutine(targetSpawner, targetItem, spawnedItems);

                if (waitForItemSpawn == true)
                {
                    // Wait for spawn to complete
                    yield return controller.StartCoroutine(itemSpawnRoutine);
                }
                else
                {
                    // Fire and forget
                    controller.StartCoroutine(itemSpawnRoutine);
                }

                // Update sub waves
                foreach (WaveSubNode subNode in GetConnectedSubWaves())
                    subNode.MasterSpawnedItem();

                // Always wait a frame
                yield return null;

                // Check for new spawnable required and that this is not the last item to be spawned
                if (HasMultipleConnections(GetInputPort(WaveSpawnableReferenceNode.spawnablePortName)) == true && i < targetSpawnCount - 1)
                {
                    targetItem = ResolveTargetSpawnableItemMultiple(controller, targetSpawner);
                    controller.CurrentState.UpdateSpawnable(targetItem);
                }
            }


            // Make sure all sub waves have ended before exiting the master wave
            while (runningSubWaves.Count > 0)
                yield return null;

            // Wait for all dead
            switch (continueMode)
            {
                case WaveContinueMode.WhenAllSpawned:
                    {
                        // Wait for all items to be fully spawned
                        while (controller.IsTryingToSpawn == true)
                            yield return null;

                        break;
                    }

                case WaveContinueMode.WhenAllDead:
                    {
                        // Cache the target count to avoid property access
                        int targetCount = targetSpawnCount;
                        
                        // Wait forever
                        while (true)
                        {
                            // Only check alive status every N frames for performance
                            if (aliveCheckFrameCounter % ALIVE_CHECK_FREQUENCY == 0)
                            {
                                // Check for dead items - zLinq optimized version
                                int deadCount = spawnedItems.AsValueEnumerable()
                                    .Count(item => !item.IsAlive());

                                // Check for all dead items
                                if (deadCount >= targetCount)
                                    break;
                            }
                            
                            aliveCheckFrameCounter++;

                            // Wait a frame
                            yield return null;
                        }
                        break;
                    }

                case WaveContinueMode.MinimumDead:
                    {
                        // Cache the minimum kill count
                        int targetMinimum = minimumKillCount;
                        
                        // Wait until the minimum number of enemies are killed
                        while (true)
                        {
                            // Only check alive status every N frames for performance
                            if (aliveCheckFrameCounter % ALIVE_CHECK_FREQUENCY == 0)
                            {
                                // Check for dead items - zLinq optimized version
                                int deadCount = spawnedItems.AsValueEnumerable()
                                    .Count(item => !item.IsAlive());

                                // Check if the minimum kill count is reached
                                if (deadCount >= targetMinimum)
                                    break;
                            }
                            
                            aliveCheckFrameCounter++;

                            // Wait a frame
                            yield return null;
                        }

                        // Despawn remaining enemies - use optimized for loop
                        for (int i = 0; i < spawnedItems.Count; i++)
                        {
                            var item = spawnedItems[i];
                            if (item.IsAlive())
                            {
                                UltimateSpawning.Despawn(item.spawnedTransform);
                            }
                        }
                        break;
                    }
            }


            // Invoke end event
            controller.OnWaveEnded.Invoke();

            // Advance the wave counter
            if (advanceWaveCounter == true && waveCounterMode == WaveCounterMode.OnWaveEnd)
                nextWave.AdvanceWaveCounter();

            // Do not continue
            if (continueMode == WaveContinueMode.Never)
                yield break;

            if (IsConnectedOutNode() == true)
            {
                // Evaluate all connected nodes
                yield return controller.StartCoroutine(EvaluateConnectedOutNode(controller));
            }
        }

        private IEnumerator EvaluateSubRoutine(WaveSpawnController controller, IEnumerator routine)
        {
            // Register the routine
            runningSubWaves.Add(routine);

            // Wait for completion
            yield return controller.StartCoroutine(routine);

            // Unregister the routine
            runningSubWaves.Remove(routine);
        }

        private WaveSubNode[] GetConnectedSubWaves()
        {
            // Get the output port
            NodePort port = GetOutputPort(subWavesPortName);

            // Check for error
            if (port == null)
                return cachedSubNodesArray;

            // Check for connection
            if (port.IsConnected == false)
                return cachedSubNodesArray;

            // Clear the cached list
            cachedSubNodes.Clear();

            // Get all connections
            for (int i = 0; i < port.ConnectionCount; i++)
            {
                // Get the connected port
                NodePort connected = port.GetConnection(i);

                // Check for error
                if (connected == null)
                    continue;

                // Get the node
                WaveSubNode node = connected.node as WaveSubNode;

                // Register the node
                if (node != null)
                    cachedSubNodes.Add(node);
            }

            // Return cached array to avoid allocation
            if (cachedSubNodesArray.Length != cachedSubNodes.Count)
                cachedSubNodesArray = new WaveSubNode[cachedSubNodes.Count];
                
            for (int i = 0; i < cachedSubNodes.Count; i++)
                cachedSubNodesArray[i] = cachedSubNodes[i];

            return cachedSubNodesArray;
        }
    }
}
