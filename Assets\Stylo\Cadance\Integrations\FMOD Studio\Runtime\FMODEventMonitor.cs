using UnityEngine;
using FMOD.Studio;
using FMODUnity;
using Stylo.Cadance;

namespace Stylo.Cadance.FMOD
{
    /// <summary>
    /// Monitors for when a specific FMOD event starts playing and creates a tracker for it.
    /// This is used when the Cadance system needs to hook into existing FMOD playback
    /// rather than creating its own event instances.
    /// </summary>
    public class FMODEventMonitor : MonoBehaviour
    {
        private string _cadanceID;
        private string _fmodEventPath;
        private CadanceAsset _cadanceAsset;
        private FMODCadanceManager _manager;
        private bool _hasDetectedEvent = false;
        
        /// <summary>
        /// Initializes the monitor with the specified parameters.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID to monitor for</param>
        /// <param name="fmodEventPath">The FMOD event path to monitor</param>
        /// <param name="cadanceAsset">The CadanceAsset to load when the event is detected</param>
        /// <param name="manager">The FMODCadanceManager that created this monitor</param>
        public void Initialize(string cadanceID, string fmodEventPath, CadanceAsset cadanceAsset, FMODCadanceManager manager)
        {
            _cadanceID = cadanceID;
            _fmodEventPath = fmodEventPath;
            _cadanceAsset = cadanceAsset;
            _manager = manager;
            
            UnityEngine.Debug.Log($"[FMODEventMonitor] Initialized monitor for Cadance ID: '{cadanceID}', Event Path: '{fmodEventPath}'");
        }
        
        /// <summary>
        /// Periodically checks for active FMOD event instances that match our target event.
        /// </summary>
        void Update()
        {
            if (_hasDetectedEvent || _manager == null)
                return;
                
            // Check every few frames to avoid performance impact
            if (Time.frameCount % 10 != 0)
                return;
                
            CheckForActiveEventInstance();
        }
        
        /// <summary>
        /// Checks if any StudioEventEmitter components are now playing our target event.
        /// </summary>
        private void CheckForActiveEventInstance()
        {
            try
            {
                var emitters = FindObjectsByType<StudioEventEmitter>(FindObjectsSortMode.None);
                
                UnityEngine.Debug.Log($"[FMODEventMonitor] *** SCANNING *** Found {emitters.Length} StudioEventEmitter components, looking for path: '{_fmodEventPath}'");
                
                foreach (var emitter in emitters)
                {
                    string emitterPath = emitter.EventReference.Path;
                    UnityEngine.Debug.Log($"[FMODEventMonitor] *** CHECKING *** Emitter on '{emitter.gameObject.name}' has path: '{emitterPath}'");
                    
                    if (emitterPath == _fmodEventPath)
                    {
                        UnityEngine.Debug.Log($"[FMODEventMonitor] *** PATH MATCH *** Found matching emitter on '{emitter.gameObject.name}'");
                        
                        var eventInstance = emitter.EventInstance;
                        bool isValid = eventInstance.isValid();
                        UnityEngine.Debug.Log($"[FMODEventMonitor] *** INSTANCE CHECK *** Event instance valid: {isValid}");
                        
                        if (isValid)
                        {
                            eventInstance.getPlaybackState(out PLAYBACK_STATE state);
                            UnityEngine.Debug.Log($"[FMODEventMonitor] *** PLAYBACK STATE *** State: {state}");
                            
                            if (state == PLAYBACK_STATE.PLAYING || state == PLAYBACK_STATE.STARTING)
                            {
                                UnityEngine.Debug.Log($"[FMODEventMonitor] *** DETECTED PLAYING EVENT *** Found active FMOD event instance on '{emitter.gameObject.name}' for path: {_fmodEventPath}");
                                
                                // Notify the manager that we found an active instance
                                _manager.OnEventInstanceDetected(_cadanceID, eventInstance, _cadanceAsset);
                                
                                _hasDetectedEvent = true;
                                
                                // Destroy this monitor since we've found what we were looking for
                                Destroy(gameObject);
                                return;
                            }
                        }
                    }
                }
                
                // Log what we're looking for vs what we found
                UnityEngine.Debug.Log($"[FMODEventMonitor] *** SCAN COMPLETE *** No active instances found for '{_fmodEventPath}' this frame");
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[FMODEventMonitor] Error checking for active event instance: {ex.Message}");
            }
        }
        
        void OnDestroy()
        {
            if (!_hasDetectedEvent)
            {
                UnityEngine.Debug.Log($"[FMODEventMonitor] Monitor destroyed without detecting event for: {_cadanceID}");
            }
        }
    }
}