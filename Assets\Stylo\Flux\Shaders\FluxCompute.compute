#pragma kernel CSEncodeMain
#pragma kernel CSDecodeMain
#pragma kernel CSEncodeMainMobile
#pragma kernel CSDecodeMainMobile
#pragma kernel CSDebugMotionVectors
#pragma kernel CSDebugCompression

// Define compute shader context for Shared.cginc
#define SHADER_STAGE_COMPUTE

// Mathematical constants
#define PI 3.14159265359

// Compute shader uniforms - must match FluxComputeUniforms C# struct exactly
// IMPORTANT: This must be declared BEFORE including Shared.cginc so Time field is accessible
cbuffer FluxComputeUniforms
{
    // Block processing parameters
    int BlockSize;
    int ThreadGroupCountX;
    int ThreadGroupCountY;
    
    // Compute-specific parameters
    float ComputeIntensity;
    float ComputeQuality;
    float MobileOptimization;
    int UseSharedMemory;
    int DebugMode;
    
    // Core Flux parameters
    float ColorCrunch;
    float EffectIntensity;
    int Downscaling;
    float ReprojectBaseNoise;
    float ReprojectLengthInfluence;
    float KeyframeResetRate;
    
    // Corruption parameters
    float MotionVectorCorruption;
    float ErrorAccumulation;
    float DCTCorruption;
    float ChromaCorruption;
    float MultiScaleCorruption;
    float CorruptionIntensity;
    
    // Motion processing parameters
    float MotionAmplification;
    float MotionThreshold;
    float CameraObjectMotionBalance;
    float MotionSmoothing;
    
    // Trail and flow parameters
    float TrailIntensity;
    float TrailSmoothness;
    float TrailPersistence;
    float FlowSpread;
    
    // JPEG compression parameters
    float JPEGQuality;
    float LuminanceQuantization;
    float ChrominanceQuantization;
    int ChromaSubsampling;
    float RingingArtifacts;
    float MosquitoNoise;
    float EdgeSensitivity;
    
    // Temporal parameters
    float TemporalBlend;
    float BrightnessMask;
    float Gamma;
    float Brightness;
    float Contrast;
    
    // Noise and brightness control
    float NoiseTransparency;
    float MaxNoiseBrightness;
    float BrightnessThreshold;
    float BrightAreaMasking;
    
    // Quantization parameters
    int QuantizationLevels;
    float CompressionIntensity;
    
    // Screen parameters
    float4 ScreenSize;
    float4 ScreenTexelSize;
    float4 DownscaledTexelSize;
    
    // Time and frame parameters
    float4 Time;
    int FrameCount;
    
    // Debug parameters
    int DebugCompressionArtifacts;
    int VisualizeMotionVectors;
};

// Input textures for compute shader - MUST be declared BEFORE including Shared.cginc
Texture2D<float4> _Input : register(t0);
Texture2D<float4> _PrevScreen : register(t1);
Texture2D<float4> _MotionVectorTexture : register(t2);
Texture2D<float4> _CameraDepthTexture : register(t3);
Texture2D<float4> _CorruptionMask : register(t4);
Texture2D<float4> _EncodedTex : register(t5);
Texture2D<float4> _MotionVectorTex : register(t6);
Texture2D<float4> _PrevTex : register(t7);
SamplerState sampler_LinearClamp : register(s0);
SamplerState sampler_PointClamp : register(s1);
SamplerState sampler_CorruptionMask : register(s2);

// Include shared functionality AFTER cbuffer and texture declarations
#include "Shared.cginc"

// Compute shader specific constants
#define THREAD_GROUP_SIZE_X 16
#define THREAD_GROUP_SIZE_Y 16
#define THREAD_GROUP_SIZE_MOBILE_X 8
#define THREAD_GROUP_SIZE_MOBILE_Y 8

// Block size variants for DCT processing
#define DCT_BLOCK_2x2 0
#define DCT_BLOCK_4x4 1
#define DCT_BLOCK_8x8 2
#define DCT_BLOCK_16x16 3
#define DCT_BLOCK_32x32 4

// UAV textures for compute shader output
RWTexture2D<float4> _EncodeOutput : register(u0);
RWTexture2D<float4> _DecodeOutput : register(u1);
RWTexture2D<float4> _DebugOutput : register(u2);

// Compute buffers for intermediate data
RWStructuredBuffer<float4> _IntermediateBuffer : register(u3);
RWStructuredBuffer<float> _DCTCoefficients : register(u4);

// Shared memory for DCT processing (16x16 max block)
groupshared float4 SharedPixelData[16][16];
groupshared float DCTSharedCoeffs[64]; // For 8x8 DCT blocks

// Enhanced hash function for compute shaders with better distribution
float4 ComputeHash(float2 coord, float time)
{
    float4 p = float4(coord.xy, time, coord.x + coord.y * time);
    p = frac(p * float4(443.897, 441.423, 437.195, 443.251));
    p += dot(p, p.yzwx + 19.19);
    return frac((p.xxyz + p.yzzw) * p.zywx);
}

// Optimized DCT processing using shared memory
float4 ProcessDCTBlock(uint2 blockCoord, uint2 localCoord, int blockSize)
{
    // Load block data into shared memory
    if (localCoord.x < blockSize && localCoord.y < blockSize)
    {
        uint2 globalCoord = blockCoord * blockSize + localCoord;
        SharedPixelData[localCoord.y][localCoord.x] = _Input.SampleLevel(sampler_LinearClamp, globalCoord / ScreenSize.xy, 0);
    }
    
    GroupMemoryBarrierWithGroupSync();
    
    float4 result = float4(0, 0, 0, 1);
    
    // DCT forward transform
    if (localCoord.x < blockSize && localCoord.y < blockSize)
    {
        for (int u = 0; u < blockSize; u++)
        {
            for (int v = 0; v < blockSize; v++)
            {
                float cu = (u == 0) ? 0.707107 : 1.0; // 1/sqrt(2)
                float cv = (v == 0) ? 0.707107 : 1.0;
                
                float sum = 0.0;
                for (int x = 0; x < blockSize; x++)
                {
                    for (int y = 0; y < blockSize; y++)
                    {
                        float cosU = cos((2 * x + 1) * u * PI / (2.0 * blockSize));
                        float cosV = cos((2 * y + 1) * v * PI / (2.0 * blockSize));
                        sum += SharedPixelData[y][x].r * cosU * cosV;
                    }
                }
                
                float coeff = (2.0 / blockSize) * cu * cv * sum;
                
                // Quantization simulation
                float quantStep = CompressionIntensity * (1.0 + (u + v) * 0.5);
                coeff = round(coeff / quantStep) * quantStep;
                
                if (u == localCoord.x && v == localCoord.y)
                {
                    result.r = coeff;
                }
            }
        }
    }
    
    float truncation = log10(lerp(1.0, 0.0001, ColorCrunch));
    
    return result;
}

// Fallback DCT processing function for compute shaders
float4 ApplyDCTProcessing(float2 uv, float4 color, int blockSize)
{
    // Simple DCT approximation for compute shaders
    float2 blockPos = floor(uv * ScreenSize.xy / blockSize);
    float2 localPos = fmod(uv * ScreenSize.xy, blockSize) / blockSize;

    // Apply basic frequency-based quantization
    float freqWeight = length(localPos - 0.5) * 2.0;
    float quantStep = CompressionIntensity * (1.0 + freqWeight);

    color.rgb = round(color.rgb / quantStep) * quantStep;
    return color;
}

// JPEG artifacts simulation for compute shaders
float4 ApplyJPEGArtifacts(float2 uv, float4 color)
{
    // Apply basic JPEG-style artifacts
    float2 blockPos = floor(uv * ScreenSize.xy / 8.0); // 8x8 blocks
    float blockNoise = hash1(uint(blockPos.x * 123 + blockPos.y * 456 + Time.y * 2.0));

    // Add subtle blocking artifacts
    if (blockNoise < JPEGQuality * 0.1)
    {
        float3 artifact = (blockNoise - 0.5) * 0.05;
        color.rgb += artifact;
    }

    return color;
}

// Simplified DCT for mobile compute shaders
float4 ApplySimplifiedDCT(float2 uv, float4 color, int blockSize)
{
    // Very simple DCT approximation for mobile
    float2 blockPos = floor(uv * ScreenSize.xy / blockSize);
    float quantStep = CompressionIntensity * 0.5; // Reduced intensity for mobile

    color.rgb = round(color.rgb / quantStep) * quantStep;
    return color;
}

// Optimized multi-scale corruption for compute shaders
float4 ApplyMultiScaleCorruption(uint2 coord, float4 originalColor, float time)
{
    float2 uv = coord / ScreenSize.xy;
    float4 result = originalColor;
    
    // Multi-scale hash-based corruption
    [unroll]
    for (int scale = 0; scale < 4; scale++)
    {
        float2 scaledUV = uv * pow(2.0, scale);
        float4 noise = ComputeHash(scaledUV, time + scale * 0.1);
        
        float corruption = CorruptionIntensity * pow(0.5, scale);
        result.rgb = lerp(result.rgb, noise.rgb, corruption * noise.a);
    }
    
    return result;
}

// Main encode kernel (desktop version)
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSEncodeMain(uint3 id : SV_DispatchThreadID, uint3 groupId : SV_GroupID, uint3 localId : SV_GroupThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float4 originalColor = _Input.SampleLevel(sampler_LinearClamp, uv, 0);
    
    // Apply DCT processing based on block size
    float4 processedColor = originalColor;
    
    if (UseSharedMemory > 0)
    {
        uint2 blockCoord = id.xy / BlockSize;
        uint2 localCoord = id.xy % BlockSize;
        processedColor = ProcessDCTBlock(blockCoord, localCoord, BlockSize);
    }
    else
    {
        // Fallback DCT without shared memory
        processedColor = ApplyDCTProcessing(uv, originalColor, BlockSize);
    }
    
    // Apply JPEG-style compression artifacts
    processedColor = ApplyJPEGArtifacts(uv, processedColor);
    
    // Apply color quantization
    processedColor.rgb = floor(processedColor.rgb * 256.0 / QuantizationLevels) * QuantizationLevels / 256.0;
    
    _EncodeOutput[id.xy] = processedColor;
}

// Main decode kernel (desktop version)
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDecodeMain(uint3 id : SV_DispatchThreadID, uint3 groupId : SV_GroupID, uint3 localId : SV_GroupThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float4 encodedColor = _EncodedTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Apply multi-scale corruption
    float4 corruptedColor = ApplyMultiScaleCorruption(id.xy, encodedColor, Time.y);

    // Apply motion vector processing
    float2 motionVector = _MotionVectorTex.SampleLevel(sampler_LinearClamp, uv, 0).xy;
    float2 prevUV = uv - motionVector;
    float4 prevColor = _PrevTex.SampleLevel(sampler_LinearClamp, prevUV, 0);
    
    // Temporal blending with brightness masking
    float brightness = dot(corruptedColor.rgb, float3(0.299, 0.587, 0.114));
    float blendFactor = TemporalBlend * saturate(brightness * BrightnessMask);
    
    float4 result = lerp(corruptedColor, prevColor, blendFactor);
    
    // Apply final color grading
    result.rgb = pow(result.rgb, 1.0 / Gamma);
    result.rgb = result.rgb * Brightness + Contrast * (result.rgb - 0.5);
    
    _DecodeOutput[id.xy] = result;
}

// Mobile optimized encode kernel (reduced thread group size and simplified processing)
[numthreads(THREAD_GROUP_SIZE_MOBILE_X, THREAD_GROUP_SIZE_MOBILE_Y, 1)]
void CSEncodeMainMobile(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float4 originalColor = _Input.SampleLevel(sampler_LinearClamp, uv, 0);
    
    // Simplified DCT processing for mobile
    float4 processedColor = ApplySimplifiedDCT(uv, originalColor, min(BlockSize, 4));
    
    // Reduced quantization levels for mobile
    int mobileQuantLevels = max(QuantizationLevels / 2, 4);
    processedColor.rgb = lerp(originalColor.rgb, processedColor.rgb, EffectIntensity);
    processedColor.rgb = floor(processedColor.rgb * 256.0 / mobileQuantLevels) * mobileQuantLevels / 256.0;
    
    _EncodeOutput[id.xy] = processedColor;
}

// Mobile optimized decode kernel
[numthreads(THREAD_GROUP_SIZE_MOBILE_X, THREAD_GROUP_SIZE_MOBILE_Y, 1)]
void CSDecodeMainMobile(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float4 encodedColor = _EncodedTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Simplified multi-scale corruption for mobile (2 scales instead of 4)
    float4 corruptedColor = encodedColor;
    [unroll]
    for (int scale = 0; scale < 2; scale++)
    {
        float2 scaledUV = uv * pow(2.0, scale);
        float4 noise = ComputeHash(scaledUV, Time.y + scale * 0.1);

        float corruption = CorruptionIntensity * 0.5 * pow(0.5, scale);
        corruptedColor.rgb = lerp(corruptedColor.rgb, noise.rgb, corruption * noise.a);
    }

    // Simplified temporal processing
    float2 motionVector = _MotionVectorTexture.SampleLevel(sampler_LinearClamp, uv, 0).xy * 0.5; // Reduced motion influence
    float2 prevUV = uv - motionVector;
    float4 prevColor = _PrevTex.SampleLevel(sampler_LinearClamp, prevUV, 0);
    
    float brightness = dot(corruptedColor.rgb, float3(0.299, 0.587, 0.114));
    float blendFactor = TemporalBlend * 0.5 * saturate(brightness);
    
    float4 result = lerp(corruptedColor, prevColor, blendFactor);
    
    _DecodeOutput[id.xy] = result;
}

// Debug kernel for motion vector visualization
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDebugMotionVectors(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float2 motionVector = _MotionVectorTex.SampleLevel(sampler_LinearClamp, uv, 0).xy;
    
    // Visualize motion vectors as colored arrows
    float magnitude = length(motionVector);
    float angle = atan2(motionVector.y, motionVector.x);
    
    float3 color = float3(
        (sin(angle) + 1.0) * 0.5,
        (cos(angle) + 1.0) * 0.5,
        magnitude * 10.0
    );
    
    _DebugOutput[id.xy] = float4(color, 1.0);
}

// Debug kernel for compression artifact visualization
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDebugCompression(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= ScreenSize.x || id.y >= ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / ScreenSize.xy;
    float4 originalColor = _Input.SampleLevel(sampler_LinearClamp, uv, 0);
    float4 compressedColor = _Input.SampleLevel(sampler_LinearClamp, uv, 0);
    
    // Visualize compression artifacts as difference map
    float4 difference = abs(originalColor - compressedColor);
    float intensity = dot(difference.rgb, float3(0.333, 0.333, 0.333));
    
    // Heat map visualization
    float3 heatColor = lerp(
        float3(0, 0, 1),  // Blue for low difference
        float3(1, 0, 0),  // Red for high difference
        saturate(intensity * 5.0)
    );
    
    _DebugOutput[id.xy] = float4(heatColor, 1.0);
}
