#if URP_INSTALLED
using UnityEngine;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Captures a snapshot of FluxEffect settings for comparison, including both values and override states
    /// </summary>
    [System.Serializable]
    public class FluxEffectSnapshot
    {
        // Component active state
        public bool active;

        // Core parameters - values and override states
        public float effectIntensity;
        public bool effectIntensityOverride;

        public bool onlyStenciled;
        public bool onlyStenciledOverride;

        // Block Encoding
        public float colorCrunch;
        public bool colorCrunchOverride;

        public int downscaling;
        public bool downscalingOverride;

        public int blockSize;
        public bool blockSizeOverride;

        public float oversharpening;
        public bool oversharpeningOverride;

        public bool dontCrunchSkybox;
        public bool dontCrunchSkyboxOverride;

        // Datamoshing Reprojection
        public float reprojectBaseNoise;
        public bool reprojectBaseNoiseOverride;

        public float reprojectBaseRerollSpeed;
        public bool reprojectBaseRerollSpeedOverride;

        public float reprojectLengthInfluence;
        public bool reprojectLengthInfluenceOverride;

        // Consolidated Motion Processing
        public float motionAmplification;
        public bool motionAmplificationOverride;

        public float motionThreshold;
        public bool motionThresholdOverride;

        public float cameraObjectMotionBalance;
        public bool cameraObjectMotionBalanceOverride;

        public float motionSmoothing;
        public bool motionSmoothingOverride;

        // Pixel Flow & Trailing
        public float trailIntensity;
        public bool trailIntensityOverride;

        public float trailSmoothness;
        public bool trailSmoothnessOverride;

        public float trailPersistence;
        public bool trailPersistenceOverride;

        public float flowSpread;
        public bool flowSpreadOverride;

        // Advanced Datamoshing
        public float keyframeResetRate;
        public bool keyframeResetRateOverride;

        public float motionVectorCorruption;
        public bool motionVectorCorruptionOverride;

        public float errorAccumulation;
        public bool errorAccumulationOverride;

        public float dctCorruption;
        public bool dctCorruptionOverride;

        // Enhanced Corruption
        public float chromaCorruption;
        public bool chromaCorruptionOverride;

        public float glitchTransition;
        public bool glitchTransitionOverride;

        public float feedbackIntensity;
        public bool feedbackIntensityOverride;

        public float multiScaleCorruption;
        public bool multiScaleCorruptionOverride;

        // Compression Artifacts Parameters
        public float ringingArtifacts;
        public bool ringingArtifactsOverride;

        public float mosquitoNoise;
        public bool mosquitoNoiseOverride;

        public float edgeSensitivity;
        public bool edgeSensitivityOverride;

        // JPEG Quality Control Parameters
        public float jpegQuality;
        public bool jpegQualityOverride;

        public float luminanceQuantization;
        public bool luminanceQuantizationOverride;

        public float chrominanceQuantization;
        public bool chrominanceQuantizationOverride;

        public bool chromaSubsampling;
        public bool chromaSubsamplingOverride;



        // Brightness Control Parameters
        public float noiseTransparency;
        public bool noiseTransparencyOverride;

        public float maxNoiseBrightness;
        public bool maxNoiseBrightnessOverride;

        public float brightnessThreshold;
        public bool brightnessThresholdOverride;

        public float brightAreaMasking;
        public bool brightAreaMaskingOverride;



        // Debug
        public bool visualizeMotionVectors;
        public bool visualizeMotionVectorsOverride;

        public bool debugCompressionArtifacts;
        public bool debugCompressionArtifactsOverride;

        public FluxEffectSnapshot(FluxEffect fluxEffect)
        {
            // Component active state
            active = fluxEffect.active;

            // Core parameters
            effectIntensity = fluxEffect.EffectIntensity.value;
            effectIntensityOverride = fluxEffect.EffectIntensity.overrideState;

            onlyStenciled = fluxEffect.OnlyStenciled.value;
            onlyStenciledOverride = fluxEffect.OnlyStenciled.overrideState;

            // Block Encoding
            colorCrunch = fluxEffect.ColorCrunch.value;
            colorCrunchOverride = fluxEffect.ColorCrunch.overrideState;

            downscaling = fluxEffect.Downscaling.value;
            downscalingOverride = fluxEffect.Downscaling.overrideState;

            blockSize = (int)fluxEffect.BlockSize.value;
            blockSizeOverride = fluxEffect.BlockSize.overrideState;

            oversharpening = fluxEffect.Oversharpening.value;
            oversharpeningOverride = fluxEffect.Oversharpening.overrideState;

            dontCrunchSkybox = fluxEffect.DontCrunchSkybox.value;
            dontCrunchSkyboxOverride = fluxEffect.DontCrunchSkybox.overrideState;

            // Datamoshing Reprojection
            reprojectBaseNoise = fluxEffect.ReprojectBaseNoise.value;
            reprojectBaseNoiseOverride = fluxEffect.ReprojectBaseNoise.overrideState;

            reprojectBaseRerollSpeed = fluxEffect.ReprojectBaseRerollSpeed.value;
            reprojectBaseRerollSpeedOverride = fluxEffect.ReprojectBaseRerollSpeed.overrideState;

            reprojectLengthInfluence = fluxEffect.ReprojectLengthInfluence.value;
            reprojectLengthInfluenceOverride = fluxEffect.ReprojectLengthInfluence.overrideState;

            // Consolidated Motion Processing
            motionAmplification = fluxEffect.MotionAmplification.value;
            motionAmplificationOverride = fluxEffect.MotionAmplification.overrideState;

            motionThreshold = fluxEffect.MotionThreshold.value;
            motionThresholdOverride = fluxEffect.MotionThreshold.overrideState;

            cameraObjectMotionBalance = fluxEffect.CameraObjectMotionBalance.value;
            cameraObjectMotionBalanceOverride = fluxEffect.CameraObjectMotionBalance.overrideState;

            motionSmoothing = fluxEffect.MotionSmoothing.value;
            motionSmoothingOverride = fluxEffect.MotionSmoothing.overrideState;

            // Pixel Flow & Trailing
            trailIntensity = fluxEffect.TrailIntensity.value;
            trailIntensityOverride = fluxEffect.TrailIntensity.overrideState;

            trailSmoothness = fluxEffect.TrailSmoothness.value;
            trailSmoothnessOverride = fluxEffect.TrailSmoothness.overrideState;

            trailPersistence = fluxEffect.TrailPersistence.value;
            trailPersistenceOverride = fluxEffect.TrailPersistence.overrideState;

            flowSpread = fluxEffect.FlowSpread.value;
            flowSpreadOverride = fluxEffect.FlowSpread.overrideState;

            // Advanced Datamoshing
            keyframeResetRate = fluxEffect.KeyframeResetRate.value;
            keyframeResetRateOverride = fluxEffect.KeyframeResetRate.overrideState;

            motionVectorCorruption = fluxEffect.MotionVectorCorruption.value;
            motionVectorCorruptionOverride = fluxEffect.MotionVectorCorruption.overrideState;

            errorAccumulation = fluxEffect.ErrorAccumulation.value;
            errorAccumulationOverride = fluxEffect.ErrorAccumulation.overrideState;

            dctCorruption = fluxEffect.DCTCorruption.value;
            dctCorruptionOverride = fluxEffect.DCTCorruption.overrideState;

            // Enhanced Corruption
            chromaCorruption = fluxEffect.ChromaCorruption.value;
            chromaCorruptionOverride = fluxEffect.ChromaCorruption.overrideState;

            glitchTransition = fluxEffect.GlitchTransition.value;
            glitchTransitionOverride = fluxEffect.GlitchTransition.overrideState;

            feedbackIntensity = fluxEffect.FeedbackIntensity.value;
            feedbackIntensityOverride = fluxEffect.FeedbackIntensity.overrideState;

            multiScaleCorruption = fluxEffect.MultiScaleCorruption.value;
            multiScaleCorruptionOverride = fluxEffect.MultiScaleCorruption.overrideState;

            // Compression Artifacts Parameters
            ringingArtifacts = fluxEffect.RingingArtifacts.value;
            ringingArtifactsOverride = fluxEffect.RingingArtifacts.overrideState;

            mosquitoNoise = fluxEffect.MosquitoNoise.value;
            mosquitoNoiseOverride = fluxEffect.MosquitoNoise.overrideState;

            edgeSensitivity = fluxEffect.EdgeSensitivity.value;
            edgeSensitivityOverride = fluxEffect.EdgeSensitivity.overrideState;

            // JPEG Quality Control Parameters
            jpegQuality = fluxEffect.JPEGQuality.value;
            jpegQualityOverride = fluxEffect.JPEGQuality.overrideState;

            luminanceQuantization = fluxEffect.LuminanceQuantization.value;
            luminanceQuantizationOverride = fluxEffect.LuminanceQuantization.overrideState;

            chrominanceQuantization = fluxEffect.ChrominanceQuantization.value;
            chrominanceQuantizationOverride = fluxEffect.ChrominanceQuantization.overrideState;

            chromaSubsampling = fluxEffect.ChromaSubsampling.value;
            chromaSubsamplingOverride = fluxEffect.ChromaSubsampling.overrideState;



            // Brightness Control Parameters
            noiseTransparency = fluxEffect.NoiseTransparency.value;
            noiseTransparencyOverride = fluxEffect.NoiseTransparency.overrideState;

            maxNoiseBrightness = fluxEffect.MaxNoiseBrightness.value;
            maxNoiseBrightnessOverride = fluxEffect.MaxNoiseBrightness.overrideState;

            brightnessThreshold = fluxEffect.BrightnessThreshold.value;
            brightnessThresholdOverride = fluxEffect.BrightnessThreshold.overrideState;

            brightAreaMasking = fluxEffect.BrightAreaMasking.value;
            brightAreaMaskingOverride = fluxEffect.BrightAreaMasking.overrideState;



            // Debug
            visualizeMotionVectors = fluxEffect.VisualizeMotionVectors.value;
            visualizeMotionVectorsOverride = fluxEffect.VisualizeMotionVectors.overrideState;

            debugCompressionArtifacts = fluxEffect.DebugCompressionArtifacts.value;
            debugCompressionArtifactsOverride = fluxEffect.DebugCompressionArtifacts.overrideState;
        }

        public void ApplyToFluxEffect(FluxEffect fluxEffect)
        {
            // Component active state
            fluxEffect.active = active;

            // Core parameters - restore both values and override states
            fluxEffect.EffectIntensity.value = effectIntensity;
            fluxEffect.EffectIntensity.overrideState = effectIntensityOverride;

            fluxEffect.OnlyStenciled.value = onlyStenciled;
            fluxEffect.OnlyStenciled.overrideState = onlyStenciledOverride;

            // Block Encoding
            fluxEffect.ColorCrunch.value = colorCrunch;
            fluxEffect.ColorCrunch.overrideState = colorCrunchOverride;

            fluxEffect.Downscaling.value = downscaling;
            fluxEffect.Downscaling.overrideState = downscalingOverride;

            fluxEffect.BlockSize.value = (FluxEffect._BlockSize)blockSize;
            fluxEffect.BlockSize.overrideState = blockSizeOverride;

            fluxEffect.Oversharpening.value = oversharpening;
            fluxEffect.Oversharpening.overrideState = oversharpeningOverride;

            fluxEffect.DontCrunchSkybox.value = dontCrunchSkybox;
            fluxEffect.DontCrunchSkybox.overrideState = dontCrunchSkyboxOverride;

            // Datamoshing Reprojection
            fluxEffect.ReprojectBaseNoise.value = reprojectBaseNoise;
            fluxEffect.ReprojectBaseNoise.overrideState = reprojectBaseNoiseOverride;

            fluxEffect.ReprojectBaseRerollSpeed.value = reprojectBaseRerollSpeed;
            fluxEffect.ReprojectBaseRerollSpeed.overrideState = reprojectBaseRerollSpeedOverride;

            fluxEffect.ReprojectLengthInfluence.value = reprojectLengthInfluence;
            fluxEffect.ReprojectLengthInfluence.overrideState = reprojectLengthInfluenceOverride;

            // Consolidated Motion Processing
            fluxEffect.MotionAmplification.value = motionAmplification;
            fluxEffect.MotionAmplification.overrideState = motionAmplificationOverride;

            fluxEffect.MotionThreshold.value = motionThreshold;
            fluxEffect.MotionThreshold.overrideState = motionThresholdOverride;

            fluxEffect.CameraObjectMotionBalance.value = cameraObjectMotionBalance;
            fluxEffect.CameraObjectMotionBalance.overrideState = cameraObjectMotionBalanceOverride;

            fluxEffect.MotionSmoothing.value = motionSmoothing;
            fluxEffect.MotionSmoothing.overrideState = motionSmoothingOverride;

            // Pixel Flow & Trailing
            fluxEffect.TrailIntensity.value = trailIntensity;
            fluxEffect.TrailIntensity.overrideState = trailIntensityOverride;

            fluxEffect.TrailSmoothness.value = trailSmoothness;
            fluxEffect.TrailSmoothness.overrideState = trailSmoothnessOverride;

            fluxEffect.TrailPersistence.value = trailPersistence;
            fluxEffect.TrailPersistence.overrideState = trailPersistenceOverride;

            fluxEffect.FlowSpread.value = flowSpread;
            fluxEffect.FlowSpread.overrideState = flowSpreadOverride;

            // Advanced Datamoshing
            fluxEffect.KeyframeResetRate.value = keyframeResetRate;
            fluxEffect.KeyframeResetRate.overrideState = keyframeResetRateOverride;

            fluxEffect.MotionVectorCorruption.value = motionVectorCorruption;
            fluxEffect.MotionVectorCorruption.overrideState = motionVectorCorruptionOverride;

            fluxEffect.ErrorAccumulation.value = errorAccumulation;
            fluxEffect.ErrorAccumulation.overrideState = errorAccumulationOverride;

            fluxEffect.DCTCorruption.value = dctCorruption;
            fluxEffect.DCTCorruption.overrideState = dctCorruptionOverride;

            // Enhanced Corruption
            fluxEffect.CorruptionMask.value = corruptionMask;
            fluxEffect.CorruptionMask.overrideState = corruptionMaskOverride;

            fluxEffect.ChromaCorruption.value = chromaCorruption;
            fluxEffect.ChromaCorruption.overrideState = chromaCorruptionOverride;

            fluxEffect.GlitchTransition.value = glitchTransition;
            fluxEffect.GlitchTransition.overrideState = glitchTransitionOverride;

            fluxEffect.FeedbackIntensity.value = feedbackIntensity;
            fluxEffect.FeedbackIntensity.overrideState = feedbackIntensityOverride;

            fluxEffect.MultiScaleCorruption.value = multiScaleCorruption;
            fluxEffect.MultiScaleCorruption.overrideState = multiScaleCorruptionOverride;

            // Compression Artifacts Parameters
            fluxEffect.RingingArtifacts.value = ringingArtifacts;
            fluxEffect.RingingArtifacts.overrideState = ringingArtifactsOverride;

            fluxEffect.MosquitoNoise.value = mosquitoNoise;
            fluxEffect.MosquitoNoise.overrideState = mosquitoNoiseOverride;

            fluxEffect.EdgeSensitivity.value = edgeSensitivity;
            fluxEffect.EdgeSensitivity.overrideState = edgeSensitivityOverride;

            // JPEG Quality Control Parameters
            fluxEffect.JPEGQuality.value = jpegQuality;
            fluxEffect.JPEGQuality.overrideState = jpegQualityOverride;

            fluxEffect.LuminanceQuantization.value = luminanceQuantization;
            fluxEffect.LuminanceQuantization.overrideState = luminanceQuantizationOverride;

            fluxEffect.ChrominanceQuantization.value = chrominanceQuantization;
            fluxEffect.ChrominanceQuantization.overrideState = chrominanceQuantizationOverride;

            fluxEffect.ChromaSubsampling.value = chromaSubsampling;
            fluxEffect.ChromaSubsampling.overrideState = chromaSubsamplingOverride;



            // Brightness Control Parameters
            fluxEffect.NoiseTransparency.value = noiseTransparency;
            fluxEffect.NoiseTransparency.overrideState = noiseTransparencyOverride;

            fluxEffect.MaxNoiseBrightness.value = maxNoiseBrightness;
            fluxEffect.MaxNoiseBrightness.overrideState = maxNoiseBrightnessOverride;

            fluxEffect.BrightnessThreshold.value = brightnessThreshold;
            fluxEffect.BrightnessThreshold.overrideState = brightnessThresholdOverride;

            fluxEffect.BrightAreaMasking.value = brightAreaMasking;
            fluxEffect.BrightAreaMasking.overrideState = brightAreaMaskingOverride;



            // Debug
            fluxEffect.VisualizeMotionVectors.value = visualizeMotionVectors;
            fluxEffect.VisualizeMotionVectors.overrideState = visualizeMotionVectorsOverride;

            fluxEffect.DebugCompressionArtifacts.value = debugCompressionArtifacts;
            fluxEffect.DebugCompressionArtifacts.overrideState = debugCompressionArtifactsOverride;
        }

        public override bool Equals(object obj)
        {
            if (obj is FluxEffectSnapshot other)
            {
                return active == other.active &&
                       // Core parameters - compare both values and override states
                       Mathf.Approximately(effectIntensity, other.effectIntensity) && effectIntensityOverride == other.effectIntensityOverride &&
                       onlyStenciled == other.onlyStenciled && onlyStenciledOverride == other.onlyStenciledOverride &&
                       // Block Encoding
                       Mathf.Approximately(colorCrunch, other.colorCrunch) && colorCrunchOverride == other.colorCrunchOverride &&
                       downscaling == other.downscaling && downscalingOverride == other.downscalingOverride &&
                       blockSize == other.blockSize && blockSizeOverride == other.blockSizeOverride &&
                       Mathf.Approximately(oversharpening, other.oversharpening) && oversharpeningOverride == other.oversharpeningOverride &&
                       dontCrunchSkybox == other.dontCrunchSkybox && dontCrunchSkyboxOverride == other.dontCrunchSkyboxOverride &&
                       // Datamoshing Reprojection
                       Mathf.Approximately(reprojectBaseNoise, other.reprojectBaseNoise) && reprojectBaseNoiseOverride == other.reprojectBaseNoiseOverride &&
                       Mathf.Approximately(reprojectBaseRerollSpeed, other.reprojectBaseRerollSpeed) && reprojectBaseRerollSpeedOverride == other.reprojectBaseRerollSpeedOverride &&
                       Mathf.Approximately(reprojectLengthInfluence, other.reprojectLengthInfluence) && reprojectLengthInfluenceOverride == other.reprojectLengthInfluenceOverride &&
                       // Consolidated Motion Processing
                       Mathf.Approximately(motionAmplification, other.motionAmplification) && motionAmplificationOverride == other.motionAmplificationOverride &&
                       Mathf.Approximately(motionThreshold, other.motionThreshold) && motionThresholdOverride == other.motionThresholdOverride &&
                       Mathf.Approximately(cameraObjectMotionBalance, other.cameraObjectMotionBalance) && cameraObjectMotionBalanceOverride == other.cameraObjectMotionBalanceOverride &&
                       Mathf.Approximately(motionSmoothing, other.motionSmoothing) && motionSmoothingOverride == other.motionSmoothingOverride &&
                       // Pixel Flow & Trailing
                       Mathf.Approximately(trailIntensity, other.trailIntensity) && trailIntensityOverride == other.trailIntensityOverride &&
                       Mathf.Approximately(trailSmoothness, other.trailSmoothness) && trailSmoothnessOverride == other.trailSmoothnessOverride &&
                       Mathf.Approximately(trailPersistence, other.trailPersistence) && trailPersistenceOverride == other.trailPersistenceOverride &&
                       Mathf.Approximately(flowSpread, other.flowSpread) && flowSpreadOverride == other.flowSpreadOverride &&
                       // Advanced Datamoshing
                       Mathf.Approximately(keyframeResetRate, other.keyframeResetRate) && keyframeResetRateOverride == other.keyframeResetRateOverride &&
                       Mathf.Approximately(motionVectorCorruption, other.motionVectorCorruption) && motionVectorCorruptionOverride == other.motionVectorCorruptionOverride &&
                       Mathf.Approximately(errorAccumulation, other.errorAccumulation) && errorAccumulationOverride == other.errorAccumulationOverride &&
                       Mathf.Approximately(dctCorruption, other.dctCorruption) && dctCorruptionOverride == other.dctCorruptionOverride &&
                       // Enhanced Corruption
                       corruptionMask == other.corruptionMask && corruptionMaskOverride == other.corruptionMaskOverride &&
                       Mathf.Approximately(chromaCorruption, other.chromaCorruption) && chromaCorruptionOverride == other.chromaCorruptionOverride &&
                       Mathf.Approximately(glitchTransition, other.glitchTransition) && glitchTransitionOverride == other.glitchTransitionOverride &&
                       Mathf.Approximately(feedbackIntensity, other.feedbackIntensity) && feedbackIntensityOverride == other.feedbackIntensityOverride &&
                       Mathf.Approximately(multiScaleCorruption, other.multiScaleCorruption) && multiScaleCorruptionOverride == other.multiScaleCorruptionOverride &&
                       // Compression Artifacts Parameters
                       Mathf.Approximately(ringingArtifacts, other.ringingArtifacts) && ringingArtifactsOverride == other.ringingArtifactsOverride &&
                       Mathf.Approximately(mosquitoNoise, other.mosquitoNoise) && mosquitoNoiseOverride == other.mosquitoNoiseOverride &&
                       Mathf.Approximately(edgeSensitivity, other.edgeSensitivity) && edgeSensitivityOverride == other.edgeSensitivityOverride &&
                       // JPEG Quality Control Parameters
                       Mathf.Approximately(jpegQuality, other.jpegQuality) && jpegQualityOverride == other.jpegQualityOverride &&
                       Mathf.Approximately(luminanceQuantization, other.luminanceQuantization) && luminanceQuantizationOverride == other.luminanceQuantizationOverride &&
                       Mathf.Approximately(chrominanceQuantization, other.chrominanceQuantization) && chrominanceQuantizationOverride == other.chrominanceQuantizationOverride &&
                       chromaSubsampling == other.chromaSubsampling && chromaSubsamplingOverride == other.chromaSubsamplingOverride &&

                       // Brightness Control Parameters
                       Mathf.Approximately(noiseTransparency, other.noiseTransparency) && noiseTransparencyOverride == other.noiseTransparencyOverride &&
                       Mathf.Approximately(maxNoiseBrightness, other.maxNoiseBrightness) && maxNoiseBrightnessOverride == other.maxNoiseBrightnessOverride &&
                       Mathf.Approximately(brightnessThreshold, other.brightnessThreshold) && brightnessThresholdOverride == other.brightnessThresholdOverride &&
                       Mathf.Approximately(brightAreaMasking, other.brightAreaMasking) && brightAreaMaskingOverride == other.brightAreaMaskingOverride &&

                       // Debug
                       visualizeMotionVectors == other.visualizeMotionVectors && visualizeMotionVectorsOverride == other.visualizeMotionVectorsOverride &&
                       debugCompressionArtifacts == other.debugCompressionArtifacts && debugCompressionArtifactsOverride == other.debugCompressionArtifactsOverride;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}
#endif
