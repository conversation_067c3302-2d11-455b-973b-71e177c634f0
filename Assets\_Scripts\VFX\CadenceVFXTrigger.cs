using Stylo.Cadance;
using UnityEngine;
using BTR;
using UnityEngine.VFX;

/// <summary>
/// Cadence VFX Trigger - Triggers Visual Effects based on Cadence musical events.
/// Migrated from Koreographer to Cadence system for improved musical timing integration.
/// </summary>
public class CadenceVFXTrigger : MonoBehaviour
{
    [Header("Cadence Event Settings")]
    [SerializeField, EventID, Tooltip("The Cadence event ID that triggers the VFX")]
    private string eventID = "Beat"; // Event ID to listen for - now with dropdown selection

    [Head<PERSON>("Debug Settings")]
    [SerializeField, Tooltip("Enable verbose logging for this specific trigger")]
    private bool enableVerboseLogging = true; // Default to true for debugging

    private VisualEffect vfxGraph; // Reference to the Visual Effect Graph component

    void Awake()
    {
        vfxGraph = GetComponent<VisualEffect>();
        if (vfxGraph == null)
        {
            Debug.LogError($"[{GetType().Name}] No VisualEffect found on GameObject '{gameObject.name}'. Please add a VisualEffect component.");
        }
        else if (enableVerboseLogging)
        {
            Debug.Log($"[{GetType().Name}] VisualEffect component found and ready on '{gameObject.name}'.");
        }
    }

    void OnEnable()
    {
        if (string.IsNullOrEmpty(eventID))
        {
            Debug.LogError($"[{GetType().Name}] Event ID is not set on GameObject '{gameObject.name}'. Please assign an Event ID in the inspector.");
            return;
        }

        if (Cadance.Instance != null)
        {
            Cadance.Instance.RegisterForEvents(eventID, TriggerVFX);
            if (enableVerboseLogging)
            {
                Debug.Log($"[{GetType().Name}] Registered for Cadence events with ID: '{eventID}' on GameObject '{gameObject.name}'");
                Debug.Log($"[{GetType().Name}] Cadance.Instance found: {Cadance.Instance != null}");
            }
        }
        else
        {
            Debug.LogError($"[{GetType().Name}] Cadance instance not found. Make sure Cadence is properly initialized.");
        }
    }

    void OnDisable()
    {
        if (Cadance.Instance != null && !string.IsNullOrEmpty(eventID))
        {
            Cadance.Instance.UnregisterForEvents(eventID, TriggerVFX);
            if (enableVerboseLogging)
            {
                Debug.Log($"[{GetType().Name}] Unregistered from Cadence events with ID: '{eventID}' on GameObject '{gameObject.name}'");
            }
        }
    }

    void Start()
    {
        // Additional debugging - check if Cadance is actually running and firing events
        if (enableVerboseLogging && Cadance.Instance != null)
        {
            Debug.Log($"[{GetType().Name}] Start() - Cadance system status check:");
            Debug.Log($"[{GetType().Name}] - Cadance.Instance exists: {Cadance.Instance != null}");
            Debug.Log($"[{GetType().Name}] - Looking for event ID: '{eventID}'");
            Debug.Log($"[{GetType().Name}] - GameObject: '{gameObject.name}'");
            Debug.Log($"[{GetType().Name}] - VFX Component: {vfxGraph != null}");
            
            // Register for ALL events temporarily to see what's actually firing (like CadanceEventDebugger does)
            Debug.Log($"[{GetType().Name}] *** REGISTERING FOR ALL EVENTS TO DEBUG ***");
            Cadance.Instance.RegisterForEvents("*", OnDebugAllEvents);
            
            // Check FMOD Cadance Manager status
            CheckFMODCadanceManagerStatus();
            
            // Auto-cleanup after 10 seconds
            Invoke(nameof(CleanupDebugListener), 10f);
        }
    }
    
    private void OnDebugAllEvents(CadanceEvent evt)
    {
        Debug.Log($"[{GetType().Name}] *** ALL EVENTS DEBUG *** Event '{evt.EventID}' fired at sample {evt.StartSample} with payload: {evt.Payload}");
        
        // Check if this is the event we're looking for
        if (evt.EventID == eventID)
        {
            Debug.Log($"[{GetType().Name}] *** FOUND TARGET EVENT *** '{eventID}' is firing! But our main listener isn't catching it.");
        }
    }
    
    private void CleanupDebugListener()
    {
        if (Cadance.Instance != null)
        {
            Cadance.Instance.UnregisterForEvents("*", OnDebugAllEvents);
            Debug.Log($"[{GetType().Name}] *** DEBUG CLEANUP *** Unregistered from all events debug listener");
        }
    }
    
    private void CheckFMODCadanceManagerStatus()
    {
        Debug.Log($"[{GetType().Name}] *** CHECKING FMOD CADANCE MANAGER STATUS ***");
        
        var fmodManager = Stylo.Cadance.FMOD.FMODCadanceManager.Instance;
        if (fmodManager != null)
        {
            Debug.Log($"[{GetType().Name}] FMODCadanceManager found!");
            
            var registeredEvents = fmodManager.GetRegisteredEvents();
            Debug.Log($"[{GetType().Name}] Registered FMOD events count: {registeredEvents.Count}");
            
            foreach (var kvp in registeredEvents)
            {
                Debug.Log($"[{GetType().Name}] - Cadance ID: '{kvp.Key}' -> FMOD Event: '{kvp.Value}'");
                
                var tracker = fmodManager.GetEventTracker(kvp.Key);
                if (tracker != null)
                {
                    Debug.Log($"[{GetType().Name}] - Tracker found for '{kvp.Key}': IsPlaying={tracker.IsPlaying}, CurrentSample={tracker.CurrentSamplePosition}");
                }
                else
                {
                    Debug.Log($"[{GetType().Name}] - No tracker found for '{kvp.Key}'");
                }
            }
        }
        else
        {
            Debug.LogError($"[{GetType().Name}] FMODCadanceManager not found!");
        }
        
        // Check if any FMOD Cadance Visor is present
        var visor = FindObjectOfType<Stylo.Cadance.FMOD.FMODCadanceVisor>();
        if (visor != null)
        {
            Debug.Log($"[{GetType().Name}] FMODCadanceVisor found: IsInitialized={visor.IsInitialized}");
            
            // Try to get runtime inspection data
            try
            {
                var inspectionData = visor.GetRuntimeInspectionData();
                Debug.Log($"[{GetType().Name}] Visor inspection: {inspectionData}");
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[{GetType().Name}] Could not get visor inspection data: {e.Message}");
            }
        }
        else
        {
            Debug.LogError($"[{GetType().Name}] FMODCadanceVisor not found!");
        }
    }

    void TriggerVFX(CadanceEvent evt)
    {
        // ALWAYS log when this method is called to verify events are firing
        Debug.Log($"[{GetType().Name}] *** TriggerVFX CALLED *** Event: '{evt.EventID}' at sample {evt.StartSample} on GameObject '{gameObject.name}'");
        
        if (enableVerboseLogging)
        {
            Debug.Log($"[{GetType().Name}] Event details - StartSample: {evt.StartSample}, EndSample: {evt.EndSample}, Payload: {evt.Payload}");
        }

        if (vfxGraph != null)
        {
            // Stop and restart the effect to ensure it triggers properly on each event
            vfxGraph.Stop();
            vfxGraph.Play();
            
            Debug.Log($"[{GetType().Name}] *** VFX TRIGGERED *** VisualEffect played for event '{evt.EventID}' on '{gameObject.name}'");
        }
        else
        {
            Debug.LogError($"[{GetType().Name}] *** VFX FAILED *** VisualEffect is null! Cannot trigger VFX for event '{evt.EventID}' on '{gameObject.name}'");
        }
    }
}