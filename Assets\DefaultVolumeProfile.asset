%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9176191809257864429
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5ba363214cc0a3a40ac4e7626c83881c, type: 3}
  m_Name: BloomOverride
  m_EditorClassIdentifier: 
  active: 1
  enabled:
    m_OverrideState: 1
    m_Value: 0
  internalBlend:
    m_OverrideState: 1
    m_Value: 0.85
  finalBlend:
    m_OverrideState: 1
    m_Value: 0.02
  bloomMaxIterations:
    m_OverrideState: 1
    m_Value: 6
  thresholdEdge:
    m_OverrideState: 1
    m_Value: 1
  thresholdRange:
    m_OverrideState: 1
    m_Value: 32
  ghostIntensity:
    m_OverrideState: 1
    m_Value: 0.3
  ghostTint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  ghostChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.0213, y: 0, z: 0.032}
  ghostTint1:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
  ghostSpread1:
    m_OverrideState: 1
    m_Value: -0.142
  ghostTint2:
    m_OverrideState: 1
    m_Value: {r: 0.231, g: 0.827, b: 0.384, a: 1}
  ghostSpread2:
    m_OverrideState: 1
    m_Value: 0.1
  ghostTint3:
    m_OverrideState: 1
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread3:
    m_OverrideState: 1
    m_Value: -0.7
  ghostTint4:
    m_OverrideState: 1
    m_Value: {r: 0.094, g: 0.654, b: 0.862, a: 1}
  ghostSpread4:
    m_OverrideState: 1
    m_Value: 0.78
  ghostTint5:
    m_OverrideState: 1
    m_Value: {r: 0.811, g: 0.243, b: 0.678, a: 1}
  ghostSpread5:
    m_OverrideState: 1
    m_Value: 0.23
  ghostTint6:
    m_OverrideState: 1
    m_Value: {r: 0.427, g: 0.792, b: 0.156, a: 1}
  ghostSpread6:
    m_OverrideState: 1
    m_Value: -0.1235
  ghostTint7:
    m_OverrideState: 1
    m_Value: {r: 0.921, g: 0.305, b: 0.058, a: 1}
  ghostSpread7:
    m_OverrideState: 1
    m_Value: 0.53
  ghostTint8:
    m_OverrideState: 1
    m_Value: {r: 0.956, g: 0.478, b: 0.129, a: 1}
  ghostSpread8:
    m_OverrideState: 1
    m_Value: -0.412
  haloIntensity:
    m_OverrideState: 1
    m_Value: 0.3
  haloFisheyeStrength:
    m_OverrideState: 1
    m_Value: 0.5
  haloFisheyeWidth:
    m_OverrideState: 1
    m_Value: 0.4
  haloChromaSpread:
    m_OverrideState: 1
    m_Value: {x: -0.02314, y: 0, z: 0.04213}
  haloTint:
    m_OverrideState: 1
    m_Value: {r: 0.623, g: 0.145, b: 0.894, a: 1}
--- !u!114 &-9050768547719734826
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.35
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 1
  Downscaling:
    m_OverrideState: 1
    m_Value: 10
  BlockSize:
    m_OverrideState: 1
    m_Value: 3
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 0
  KeyframeResetRate:
    m_OverrideState: 1
    m_Value: 0
  MotionVectorCorruption:
    m_OverrideState: 1
    m_Value: 0
  ErrorAccumulation:
    m_OverrideState: 1
    m_Value: 0
  DCTCorruption:
    m_OverrideState: 1
    m_Value: 0
  ChromaCorruption:
    m_OverrideState: 1
    m_Value: 0
  MotionAmplification:
    m_OverrideState: 1
    m_Value: 3
  MotionThreshold:
    m_OverrideState: 1
    m_Value: 0.001
  CameraObjectMotionBalance:
    m_OverrideState: 1
    m_Value: 0.3
  MotionSmoothing:
    m_OverrideState: 1
    m_Value: 0.1
  TrailIntensity:
    m_OverrideState: 1
    m_Value: 2
  TrailSmoothness:
    m_OverrideState: 1
    m_Value: 0.5
  TrailPersistence:
    m_OverrideState: 1
    m_Value: 0.8
  FlowSpread:
    m_OverrideState: 1
    m_Value: 2
  CorruptionMask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  GlitchTransition:
    m_OverrideState: 1
    m_Value: 0
  FeedbackIntensity:
    m_OverrideState: 1
    m_Value: 0
  MultiScaleCorruption:
    m_OverrideState: 1
    m_Value: 0
  JPEGQuality:
    m_OverrideState: 1
    m_Value: 25
  LuminanceQuantization:
    m_OverrideState: 1
    m_Value: 0.8
  ChrominanceQuantization:
    m_OverrideState: 1
    m_Value: 1.2
  ChromaSubsampling:
    m_OverrideState: 1
    m_Value: 1
  RingingArtifacts:
    m_OverrideState: 1
    m_Value: 0.3
  MosquitoNoise:
    m_OverrideState: 1
    m_Value: 0.2
  EdgeSensitivity:
    m_OverrideState: 1
    m_Value: 0.8
  NoiseTransparency:
    m_OverrideState: 1
    m_Value: 0.05
  MaxNoiseBrightness:
    m_OverrideState: 1
    m_Value: 0.9
  BrightnessThreshold:
    m_OverrideState: 1
    m_Value: 0.7
  BrightAreaMasking:
    m_OverrideState: 1
    m_Value: 0.8
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.2
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
  DebugCompressionArtifacts:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8990916391055664120
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &-8743054661567825574
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-8697994446419294100
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2303f781f6c04e428d9eb352fa4be37e, type: 3}
  m_Name: OutlineVol
  m_EditorClassIdentifier: 
  active: 1
  m_Sensitive:
    m_OverrideState: 1
    m_Value: 0
  m_Thickness:
    m_OverrideState: 1
    m_Value: 0.15
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 0, g: 0, b: 0, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
  m_Fill:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_Sharp:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0
  m_Remap:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8675414456923828172
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 221518ef91623a7438a71fef23660601, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  temperature:
    m_OverrideState: 1
    m_Value: 0
  tint:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8521462762252591541
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8516637950199427543
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &-8431576175603817164
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96468967439741488ad1b1af1ab44b9b, type: 3}
  m_Name: ScanlinesVol
  m_EditorClassIdentifier: 
  active: 1
  m_Intensity:
    m_OverrideState: 1
    m_Value: 0
  m_Count:
    m_OverrideState: 1
    m_Value: 570
  m_Speed:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value: 1
  m_Flicker:
    m_OverrideState: 1
    m_Value: 0
  m_Grad:
    m_OverrideState: 1
    m_Value: 0.33
  m_Animation:
    m_OverrideState: 1
    m_Value: 1
  m_GradSpeed:
    m_OverrideState: 1
    m_Value: 0.2
  m_Flip:
    m_OverrideState: 1
    m_Value: 0
  m_GradColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0.07}
--- !u!114 &-8429206602139580038
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d94e4d16c32d4354919ca310e9b07591, type: 3}
  m_Name: AdjustmentsVol
  m_EditorClassIdentifier: 
  active: 1
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_Threshold:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Hue:
    m_OverrideState: 1
    m_Value: 0
  m_Saturation:
    m_OverrideState: 1
    m_Value: 0
  m_Brightness:
    m_OverrideState: 1
    m_Value: 0
  m_Contrast:
    m_OverrideState: 1
    m_Value: 0
  m_Alpha:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-8391915792481051711
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &-8332138434331751923
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e3b43e232564d52b710c4bdc0cd6e42, type: 3}
  m_Name: ColorMapVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Gradient:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Offset:
    m_OverrideState: 1
    m_Value: 0
  m_Mask:
    m_OverrideState: 1
    m_Value: {x: 0, y: 1}
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
--- !u!114 &-8314571273235026838
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 03592b0b6d784e6da6ea74233ca33d52, type: 3}
  m_Name: GrainVol
  m_EditorClassIdentifier: 
  active: 1
  m_Grain:
    m_OverrideState: 1
    m_Value: 0
  m_Response:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
  m_Brightness:
    m_OverrideState: 1
    m_Value: 0
  m_Hue:
    m_OverrideState: 1
    m_Value: 0
  m_Saturation:
    m_OverrideState: 1
    m_Value: 0
  m_GainTex:
    m_OverrideState: 1
    m_Value: 8
  m_Alpha:
    m_OverrideState: 1
    m_Value: 1
  m_Color:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Fps:
    m_OverrideState: 1
    m_Value: 60
  m_Scale:
    m_OverrideState: 1
    m_Value: 0
  m_Texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-8295165454565937433
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdfbdbb87d3286943a057f7791b43141, type: 3}
  m_Name: ChannelMixer
  m_EditorClassIdentifier: 
  active: 1
  redOutRedIn:
    m_OverrideState: 1
    m_Value: 100
  redOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  redOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutGreenIn:
    m_OverrideState: 1
    m_Value: 100
  greenOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutBlueIn:
    m_OverrideState: 1
    m_Value: 100
--- !u!114 &-8227133943205580223
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06437c1ff663d574d9447842ba0a72e4, type: 3}
  m_Name: ScreenSpaceLensFlare
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  tintColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  bloomMip:
    m_OverrideState: 1
    m_Value: 1
  firstFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  secondaryFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  warpedFlareIntensity:
    m_OverrideState: 1
    m_Value: 1
  warpedFlareScale:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1}
  samples:
    m_OverrideState: 1
    m_Value: 1
  sampleDimmer:
    m_OverrideState: 1
    m_Value: 0.5
  vignetteEffect:
    m_OverrideState: 1
    m_Value: 1
  startingPosition:
    m_OverrideState: 1
    m_Value: 1.25
  scale:
    m_OverrideState: 1
    m_Value: 1.5
  streaksIntensity:
    m_OverrideState: 1
    m_Value: 0
  streaksLength:
    m_OverrideState: 1
    m_Value: 0.5
  streaksOrientation:
    m_OverrideState: 1
    m_Value: 0
  streaksThreshold:
    m_OverrideState: 1
    m_Value: 0.25
  resolution:
    m_OverrideState: 1
    m_Value: 4
  chromaticAbberationIntensity:
    m_OverrideState: 1
    m_Value: 0.5
--- !u!114 &-7952951411440254421
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb28ea5c871044b4c90a10169555151b, type: 3}
  m_Name: OldMovieVol
  m_EditorClassIdentifier: 
  active: 1
  m_Grain:
    m_OverrideState: 1
    m_Value: 0
  m_Noise:
    m_OverrideState: 1
    m_Value: 0
  m_NoiseAlpha:
    m_OverrideState: 1
    m_Value: 0
  m_Jolt:
    m_OverrideState: 1
    m_Value: 0
  m_Fps:
    m_OverrideState: 1
    m_Value: 16
  m_Vignette:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
  m_GrainTex:
    m_OverrideState: 1
    m_Value: 8
  m_Audio:
    m_OverrideState: 1
    m_Value: 0
  m_Volume:
    m_OverrideState: 1
    m_Value: 0
  m_Pich:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7884959629649752361
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2aebac967aa993140ab1982d43b8450a, type: 3}
  m_Name: GlitchVol
  m_EditorClassIdentifier: 
  active: 1
  _weight:
    m_OverrideState: 1
    m_Value: 0
  _power:
    m_OverrideState: 1
    m_Value: 0
  _scale:
    m_OverrideState: 1
    m_Value: 0
  _dispersion:
    m_OverrideState: 1
    m_Value: 0
  _period:
    m_OverrideState: 1
    m_Value: 0
  _color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0.773, g: 0.693, b: 1, a: 1}
        key1: {r: 0, g: 0.98, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0.773, g: 0.693, b: 1, a: 1}
      - {r: 0.7480645, g: 0.7022581, b: 1, a: 1}
      - {r: 0.72312903, g: 0.71151614, b: 1, a: 1}
      - {r: 0.69819355, g: 0.72077423, b: 1, a: 1}
      - {r: 0.67325807, g: 0.73003227, b: 1, a: 1}
      - {r: 0.6483226, g: 0.73929036, b: 1, a: 1}
      - {r: 0.6233871, g: 0.7485484, b: 1, a: 1}
      - {r: 0.5984516, g: 0.7578065, b: 1, a: 1}
      - {r: 0.57351613, g: 0.7670645, b: 1, a: 1}
      - {r: 0.54858065, g: 0.7763226, b: 1, a: 1}
      - {r: 0.52364516, g: 0.78558064, b: 1, a: 1}
      - {r: 0.49870968, g: 0.7948387, b: 1, a: 1}
      - {r: 0.4737742, g: 0.8040968, b: 1, a: 1}
      - {r: 0.4488387, g: 0.81335485, b: 1, a: 1}
      - {r: 0.42390323, g: 0.8226129, b: 1, a: 1}
      - {r: 0.39896774, g: 0.831871, b: 1, a: 1}
      - {r: 0.37403226, g: 0.84112906, b: 1, a: 1}
      - {r: 0.34909678, g: 0.8503871, b: 1, a: 1}
      - {r: 0.3241613, g: 0.8596452, b: 1, a: 1}
      - {r: 0.2992258, g: 0.8689033, b: 1, a: 1}
      - {r: 0.27429032, g: 0.8781613, b: 1, a: 1}
      - {r: 0.24935484, g: 0.88741934, b: 1, a: 1}
      - {r: 0.22441936, g: 0.89667743, b: 1, a: 1}
      - {r: 0.19948387, g: 0.9059355, b: 1, a: 1}
      - {r: 0.17454839, g: 0.91519356, b: 1, a: 1}
      - {r: 0.1496129, g: 0.92445165, b: 1, a: 1}
      - {r: 0.12467742, g: 0.9337097, b: 1, a: 1}
      - {r: 0.099741936, g: 0.9429678, b: 1, a: 1}
      - {r: 0.07480645, g: 0.9522258, b: 1, a: 1}
      - {r: 0.049870968, g: 0.9614839, b: 1, a: 1}
      - {r: 0.024935484, g: 0.970742, b: 1, a: 1}
      - {r: 0, g: 0.98, b: 1, a: 1}
  _chaotic:
    m_OverrideState: 1
    m_Value: 0
  _noLockNoise:
    m_OverrideState: 1
    m_Value: 0
  _density:
    m_OverrideState: 1
    m_Value: 0
  _lock:
    m_OverrideState: 1
    m_Value: 0
  _sharpen:
    m_OverrideState: 1
    m_Value: 0
  _crush:
    m_OverrideState: 1
    m_Value: 0
  _grid:
    m_OverrideState: 1
    m_Value: 0
  _bleed:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0.37, g: 0.29, b: 0.25, a: 0}
        key1: {r: 1, g: 0.94, b: 0.5, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0.37, g: 0.29, b: 0.25, a: 0}
      - {r: 0.3903226, g: 0.31096774, b: 0.2580645, a: 0}
      - {r: 0.41064516, g: 0.33193547, b: 0.26612902, a: 0}
      - {r: 0.43096775, g: 0.35290322, b: 0.27419356, a: 0}
      - {r: 0.4512903, g: 0.37387097, b: 0.28225806, a: 0}
      - {r: 0.4716129, g: 0.3948387, b: 0.29032257, a: 0}
      - {r: 0.4919355, g: 0.4158064, b: 0.2983871, a: 0}
      - {r: 0.51225805, g: 0.4367742, b: 0.30645162, a: 0}
      - {r: 0.5325806, g: 0.45774192, b: 0.31451613, a: 0}
      - {r: 0.55290323, g: 0.47870964, b: 0.32258064, a: 0}
      - {r: 0.5732258, g: 0.4996774, b: 0.33064514, a: 0}
      - {r: 0.5935484, g: 0.52064514, b: 0.33870968, a: 0}
      - {r: 0.613871, g: 0.54161286, b: 0.3467742, a: 0}
      - {r: 0.63419354, g: 0.5625806, b: 0.3548387, a: 0}
      - {r: 0.6545161, g: 0.58354837, b: 0.36290324, a: 0}
      - {r: 0.67483866, g: 0.6045161, b: 0.37096775, a: 0}
      - {r: 0.6951613, g: 0.6254839, b: 0.37903225, a: 0}
      - {r: 0.7154839, g: 0.6464516, b: 0.38709676, a: 0}
      - {r: 0.73580647, g: 0.6674193, b: 0.39516127, a: 0}
      - {r: 0.756129, g: 0.6883871, b: 0.4032258, a: 0}
      - {r: 0.7764516, g: 0.70935476, b: 0.41129032, a: 0}
      - {r: 0.7967742, g: 0.7303226, b: 0.41935486, a: 0}
      - {r: 0.81709677, g: 0.7512903, b: 0.42741936, a: 0}
      - {r: 0.8374194, g: 0.77225804, b: 0.43548387, a: 0}
      - {r: 0.85774195, g: 0.79322577, b: 0.44354838, a: 0}
      - {r: 0.8780645, g: 0.8141935, b: 0.4516129, a: 0}
      - {r: 0.8983871, g: 0.8351612, b: 0.4596774, a: 0}
      - {r: 0.9187097, g: 0.85612905, b: 0.46774194, a: 0}
      - {r: 0.93903226, g: 0.8770968, b: 0.47580644, a: 0}
      - {r: 0.9593548, g: 0.8980645, b: 0.48387098, a: 0}
      - {r: 0.9796774, g: 0.9190322, b: 0.4919355, a: 0}
      - {r: 1, g: 0.93999994, b: 0.5, a: 0}
  _screen:
    m_OverrideState: 1
    m_Value: 0
  _noLock:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7779584490226480321
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb28ea5c871044b4c90a10169555151b, type: 3}
  m_Name: OldMovieVol
  m_EditorClassIdentifier: 
  active: 1
  m_Grain:
    m_OverrideState: 1
    m_Value: 0
  m_Noise:
    m_OverrideState: 1
    m_Value: 0
  m_NoiseAlpha:
    m_OverrideState: 1
    m_Value: 0
  m_Jolt:
    m_OverrideState: 1
    m_Value: 0
  m_Fps:
    m_OverrideState: 1
    m_Value: 16
  m_Vignette:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
  m_GrainTex:
    m_OverrideState: 1
    m_Value: 8
  m_Audio:
    m_OverrideState: 1
    m_Value: 0
  m_Volume:
    m_OverrideState: 1
    m_Value: 0
  m_Pich:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7504418347886888666
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f0e86679a2284a4ca49d75916bcd95c3, type: 3}
  m_Name: SliceVol
  m_EditorClassIdentifier: 
  active: 1
  m_Value:
    m_OverrideState: 1
    m_Value: 0
  m_Tiling:
    m_OverrideState: 1
    m_Value: 500
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7374895477392139716
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.35
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 1
  Downscaling:
    m_OverrideState: 1
    m_Value: 10
  BlockSize:
    m_OverrideState: 1
    m_Value: 3
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 0
  KeyframeResetRate:
    m_OverrideState: 1
    m_Value: 0
  MotionVectorCorruption:
    m_OverrideState: 1
    m_Value: 0
  ErrorAccumulation:
    m_OverrideState: 1
    m_Value: 0
  DCTCorruption:
    m_OverrideState: 1
    m_Value: 0
  ChromaCorruption:
    m_OverrideState: 1
    m_Value: 0
  MotionAmplification:
    m_OverrideState: 0
    m_Value: 3
  MotionThreshold:
    m_OverrideState: 0
    m_Value: 0.001
  CameraObjectMotionBalance:
    m_OverrideState: 0
    m_Value: 0.3
  MotionSmoothing:
    m_OverrideState: 0
    m_Value: 0.1
  TrailIntensity:
    m_OverrideState: 0
    m_Value: 2
  TrailSmoothness:
    m_OverrideState: 1
    m_Value: 0.5
  TrailPersistence:
    m_OverrideState: 0
    m_Value: 0.8
  FlowSpread:
    m_OverrideState: 0
    m_Value: 2
  CorruptionMask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  GlitchTransition:
    m_OverrideState: 1
    m_Value: 0
  FeedbackIntensity:
    m_OverrideState: 1
    m_Value: 0
  MultiScaleCorruption:
    m_OverrideState: 1
    m_Value: 0
  JPEGQuality:
    m_OverrideState: 1
    m_Value: 25
  LuminanceQuantization:
    m_OverrideState: 1
    m_Value: 0.8
  ChrominanceQuantization:
    m_OverrideState: 1
    m_Value: 1.2
  ChromaSubsampling:
    m_OverrideState: 1
    m_Value: 1
  RingingArtifacts:
    m_OverrideState: 1
    m_Value: 0.3
  MosquitoNoise:
    m_OverrideState: 1
    m_Value: 0.2
  EdgeSensitivity:
    m_OverrideState: 1
    m_Value: 0.8
  NoiseTransparency:
    m_OverrideState: 0
    m_Value: 0.05
  MaxNoiseBrightness:
    m_OverrideState: 0
    m_Value: 0.9
  BrightnessThreshold:
    m_OverrideState: 0
    m_Value: 0.7
  BrightAreaMasking:
    m_OverrideState: 0
    m_Value: 0.8
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.2
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
  DebugCompressionArtifacts:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-7297090143076045022
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b447969f02741db89b76840f68e7812, type: 3}
  m_Name: DistortVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value: 0
  m_Tiling:
    m_OverrideState: 1
    m_Value: 0
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Motion:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-7187065683549268971
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-6969694517249164220
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2303f781f6c04e428d9eb352fa4be37e, type: 3}
  m_Name: OutlineVol
  m_EditorClassIdentifier: 
  active: 1
  m_Sensitive:
    m_OverrideState: 1
    m_Value: 0
  m_Thickness:
    m_OverrideState: 1
    m_Value: 0.15
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 0, g: 0, b: 0, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
  m_Fill:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_Sharp:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0
  m_Remap:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-6781775619798150211
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a10f618ab8a42b54a8aa755b345798c0, type: 3}
  m_Name: InvertVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0.5
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0.5
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
--- !u!114 &-6774743062877760036
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3049d7fe12ad3f248b06c5523cca638e, type: 3}
  m_Name: DissolveVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Mask:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
    hdr: 0
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Velocity:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  m_Shade:
    m_OverrideState: 1
    m_Value: 1
  m_Disolve:
    m_OverrideState: 1
    m_Value: 1
  m_Overlay:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  m_Custom:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-6723992901057550629
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0
  smoothness:
    m_OverrideState: 1
    m_Value: 0.2
  rounded:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-6669317113833689463
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b447969f02741db89b76840f68e7812, type: 3}
  m_Name: DistortVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value: 0
  m_Tiling:
    m_OverrideState: 1
    m_Value: 0
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Motion:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-6481690183073092585
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3049d7fe12ad3f248b06c5523cca638e, type: 3}
  m_Name: DissolveVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Mask:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
    hdr: 0
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Velocity:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  m_Shade:
    m_OverrideState: 1
    m_Value: 1
  m_Disolve:
    m_OverrideState: 1
    m_Value: 1
  m_Overlay:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Custom:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-6479825379983740399
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29fa0085f50d5e54f8144f766051a691, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  type:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
  response:
    m_OverrideState: 1
    m_Value: 0.8
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-6080236906858883424
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eb4b772797da9440885e8bd939e9560, type: 3}
  m_Name: ColorCurves
  m_EditorClassIdentifier: 
  active: 1
  master:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  red:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  green:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  blue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsHue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  satVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  lumVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
--- !u!114 &-6011903566279663067
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  neutralHDRRangeReductionMode:
    m_OverrideState: 1
    m_Value: 2
  acesPreset:
    m_OverrideState: 1
    m_Value: 3
  hueShiftAmount:
    m_OverrideState: 1
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 1
    m_Value: 0
  paperWhite:
    m_OverrideState: 1
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 1
    m_Value: 1
  minNits:
    m_OverrideState: 1
    m_Value: 0.005
  maxNits:
    m_OverrideState: 1
    m_Value: 1000
--- !u!114 &-5003712500446951850
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c276c365a884f5a4293f9acd7b9ea680, type: 3}
  m_Name: AutoExposureOverride
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  evMin:
    m_OverrideState: 1
    m_Value: 0
  evMax:
    m_OverrideState: 1
    m_Value: 12
  evCompensation:
    m_OverrideState: 1
    m_Value: 0
  compensationCurveParameter:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 1
    m_Value: 0
  darkToLightSpeed:
    m_OverrideState: 1
    m_Value: 3
  lightToDarkSpeed:
    m_OverrideState: 1
    m_Value: 1
  meteringMaskMode:
    m_OverrideState: 1
    m_Value: 0
  meteringMaskTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  meteringProceduralFalloff:
    m_OverrideState: 1
    m_Value: 2
  renderingMode:
    m_OverrideState: 1
    m_Value: 0
  sampleCount:
    m_OverrideState: 1
    m_Value: 30
  animateSamplePositions:
    m_OverrideState: 1
    m_Value: 0
  response:
    m_OverrideState: 1
    m_Value: 0.03
  clampingEnabled:
    m_OverrideState: 1
    m_Value: 0
  clampingBracket:
    m_OverrideState: 1
    m_Value: 2
--- !u!114 &-4848043260240122332
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bba95ee541be4fe3b76c758f1424e975, type: 3}
  m_Name: FlowVol
  m_EditorClassIdentifier: 
  active: 1
  m_Fade:
    m_OverrideState: 1
    m_Value: 0
  m_Strain:
    m_OverrideState: 1
    m_Value: 0
  m_Samples:
    m_OverrideState: 1
    m_Value: 1
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Flow:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Print:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0
  m_Focus:
    m_OverrideState: 1
    m_Value: 0
  m_Fps:
    m_OverrideState: 1
    m_Value: 60
--- !u!114 &-4810053698259113156
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2aebac967aa993140ab1982d43b8450a, type: 3}
  m_Name: GlitchVol
  m_EditorClassIdentifier: 
  active: 1
  _weight:
    m_OverrideState: 1
    m_Value: 0
  _power:
    m_OverrideState: 1
    m_Value: 0
  _scale:
    m_OverrideState: 1
    m_Value: 0
  _dispersion:
    m_OverrideState: 1
    m_Value: 0
  _period:
    m_OverrideState: 1
    m_Value: 0
  _color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0.773, g: 0.693, b: 1, a: 1}
        key1: {r: 0, g: 0.98, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0.773, g: 0.693, b: 1, a: 1}
      - {r: 0.7480645, g: 0.7022581, b: 1, a: 1}
      - {r: 0.72312903, g: 0.71151614, b: 1, a: 1}
      - {r: 0.69819355, g: 0.72077423, b: 1, a: 1}
      - {r: 0.67325807, g: 0.73003227, b: 1, a: 1}
      - {r: 0.6483226, g: 0.73929036, b: 1, a: 1}
      - {r: 0.6233871, g: 0.7485484, b: 1, a: 1}
      - {r: 0.5984516, g: 0.7578065, b: 1, a: 1}
      - {r: 0.57351613, g: 0.7670645, b: 1, a: 1}
      - {r: 0.54858065, g: 0.7763226, b: 1, a: 1}
      - {r: 0.52364516, g: 0.78558064, b: 1, a: 1}
      - {r: 0.49870968, g: 0.7948387, b: 1, a: 1}
      - {r: 0.4737742, g: 0.8040968, b: 1, a: 1}
      - {r: 0.4488387, g: 0.81335485, b: 1, a: 1}
      - {r: 0.42390323, g: 0.8226129, b: 1, a: 1}
      - {r: 0.39896774, g: 0.831871, b: 1, a: 1}
      - {r: 0.37403226, g: 0.84112906, b: 1, a: 1}
      - {r: 0.34909678, g: 0.8503871, b: 1, a: 1}
      - {r: 0.3241613, g: 0.8596452, b: 1, a: 1}
      - {r: 0.2992258, g: 0.8689033, b: 1, a: 1}
      - {r: 0.27429032, g: 0.8781613, b: 1, a: 1}
      - {r: 0.24935484, g: 0.88741934, b: 1, a: 1}
      - {r: 0.22441936, g: 0.89667743, b: 1, a: 1}
      - {r: 0.19948387, g: 0.9059355, b: 1, a: 1}
      - {r: 0.17454839, g: 0.91519356, b: 1, a: 1}
      - {r: 0.1496129, g: 0.92445165, b: 1, a: 1}
      - {r: 0.12467742, g: 0.9337097, b: 1, a: 1}
      - {r: 0.099741936, g: 0.9429678, b: 1, a: 1}
      - {r: 0.07480645, g: 0.9522258, b: 1, a: 1}
      - {r: 0.049870968, g: 0.9614839, b: 1, a: 1}
      - {r: 0.024935484, g: 0.970742, b: 1, a: 1}
      - {r: 0, g: 0.98, b: 1, a: 1}
  _chaotic:
    m_OverrideState: 1
    m_Value: 0
  _noLockNoise:
    m_OverrideState: 1
    m_Value: 0
  _density:
    m_OverrideState: 1
    m_Value: 0
  _lock:
    m_OverrideState: 1
    m_Value: 0
  _sharpen:
    m_OverrideState: 1
    m_Value: 0
  _crush:
    m_OverrideState: 1
    m_Value: 0
  _grid:
    m_OverrideState: 1
    m_Value: 0
  _bleed:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0.37, g: 0.29, b: 0.25, a: 0}
        key1: {r: 1, g: 0.94, b: 0.5, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0.37, g: 0.29, b: 0.25, a: 0}
      - {r: 0.3903226, g: 0.31096774, b: 0.2580645, a: 0}
      - {r: 0.41064516, g: 0.33193547, b: 0.26612902, a: 0}
      - {r: 0.43096775, g: 0.35290322, b: 0.27419356, a: 0}
      - {r: 0.4512903, g: 0.37387097, b: 0.28225806, a: 0}
      - {r: 0.4716129, g: 0.3948387, b: 0.29032257, a: 0}
      - {r: 0.4919355, g: 0.4158064, b: 0.2983871, a: 0}
      - {r: 0.51225805, g: 0.4367742, b: 0.30645162, a: 0}
      - {r: 0.5325806, g: 0.45774192, b: 0.31451613, a: 0}
      - {r: 0.55290323, g: 0.47870964, b: 0.32258064, a: 0}
      - {r: 0.5732258, g: 0.4996774, b: 0.33064514, a: 0}
      - {r: 0.5935484, g: 0.52064514, b: 0.33870968, a: 0}
      - {r: 0.613871, g: 0.54161286, b: 0.3467742, a: 0}
      - {r: 0.63419354, g: 0.5625806, b: 0.3548387, a: 0}
      - {r: 0.6545161, g: 0.58354837, b: 0.36290324, a: 0}
      - {r: 0.67483866, g: 0.6045161, b: 0.37096775, a: 0}
      - {r: 0.6951613, g: 0.6254839, b: 0.37903225, a: 0}
      - {r: 0.7154839, g: 0.6464516, b: 0.38709676, a: 0}
      - {r: 0.73580647, g: 0.6674193, b: 0.39516127, a: 0}
      - {r: 0.756129, g: 0.6883871, b: 0.4032258, a: 0}
      - {r: 0.7764516, g: 0.70935476, b: 0.41129032, a: 0}
      - {r: 0.7967742, g: 0.7303226, b: 0.41935486, a: 0}
      - {r: 0.81709677, g: 0.7512903, b: 0.42741936, a: 0}
      - {r: 0.8374194, g: 0.77225804, b: 0.43548387, a: 0}
      - {r: 0.85774195, g: 0.79322577, b: 0.44354838, a: 0}
      - {r: 0.8780645, g: 0.8141935, b: 0.4516129, a: 0}
      - {r: 0.8983871, g: 0.8351612, b: 0.4596774, a: 0}
      - {r: 0.9187097, g: 0.85612905, b: 0.46774194, a: 0}
      - {r: 0.93903226, g: 0.8770968, b: 0.47580644, a: 0}
      - {r: 0.9593548, g: 0.8980645, b: 0.48387098, a: 0}
      - {r: 0.9796774, g: 0.9190322, b: 0.4919355, a: 0}
      - {r: 1, g: 0.93999994, b: 0.5, a: 0}
  _screen:
    m_OverrideState: 1
    m_Value: 0
  _noLock:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-4721238499221657664
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba41c240f778d164abc40f8dca90fc16, type: 3}
  m_Name: AsciiVol
  m_EditorClassIdentifier: 
  active: 1
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Ascii:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Mapping:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0.032258064}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0.06451613}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0.09677419}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0.12903225}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0.16129032}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0.19354838}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0.22580644}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0.2580645}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0.29032257}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0.32258064}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0.3548387}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0.38709676}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0.41935483}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0.4516129}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0.48387095}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0.516129}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0.5483871}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0.58064514}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0.61290324}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0.6451613}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0.67741936}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0.7096774}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0.7419355}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0.7741935}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0.8064516}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0.83870965}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0.87096775}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0.9032258}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0.9354839}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0.9677419}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Image:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_Solid:
    m_OverrideState: 1
    m_Value: 0
  m_Gradient:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Depth:
    m_OverrideState: 1
    m_Value: 1
  m_Fps:
    m_OverrideState: 1
    m_Value: 0
  m_Noise:
    m_OverrideState: 1
    m_Value: 1
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-4715286981302351028
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f87d2427ed3459e8c4345aacc689427, type: 3}
  m_Name: BlurVol
  m_EditorClassIdentifier: 
  active: 1
  m_Radius:
    m_OverrideState: 1
    m_Value: 0
  m_Radial:
    m_OverrideState: 1
    m_Value: 0
  m_Samples:
    m_OverrideState: 1
    m_Value: 9
  m_Aspect:
    m_OverrideState: 1
    m_Value: 0
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
--- !u!114 &-4405982785398066721
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &-4297709812900762894
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &-4187864996791517520
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb2952b95454955ba507c3d96778a76, type: 3}
  m_Name: VhsVol
  m_EditorClassIdentifier: 
  active: 1
  _weight:
    m_OverrideState: 1
    m_Value: 0
  _tape:
    m_OverrideState: 1
    m_Value: 0
  _shades:
    m_OverrideState: 1
    m_Value: 0
  _rocking:
    m_OverrideState: 1
    m_Value: 0
  _squeeze:
    m_OverrideState: 1
    m_Value: 0
  _density:
    m_OverrideState: 1
    m_Value: 0
  _intensity:
    m_OverrideState: 1
    m_Value: 0
  _scale:
    m_OverrideState: 1
    m_Value: 1
  _flickering:
    m_OverrideState: 1
    m_Value: 0
  _color:
    m_OverrideState: 1
    m_Value: {r: 1, g: 0, b: 0, a: 1}
  _bleed:
    m_OverrideState: 1
    m_Value: 0.7
  _flow:
    m_OverrideState: 1
    m_Value: 1
  _pulsation:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-4108351680140900910
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f51d4ab7f92eb9f47a34d089e35b58d3, type: 3}
  m_Name: WeatherSettings
  m_EditorClassIdentifier: 
  active: 1
  _snow:
    m_OverrideState: 1
    m_Value: 0
  _sun:
    m_OverrideState: 1
    m_Value: 0
  _clouds:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-3484325141788122557
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-3472970424870872158
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f51d4ab7f92eb9f47a34d089e35b58d3, type: 3}
  m_Name: WeatherSettings
  m_EditorClassIdentifier: 
  active: 1
  _snow:
    m_OverrideState: 1
    m_Value: 0
  _sun:
    m_OverrideState: 1
    m_Value: 0
  _clouds:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-3426981279801294241
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-2765096762664561248
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-2628960443528027430
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a10f618ab8a42b54a8aa755b345798c0, type: 3}
  m_Name: InvertVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0.5
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0.5
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
      - {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
--- !u!114 &-2495327742645499210
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  gaussianStart:
    m_OverrideState: 1
    m_Value: 10
  gaussianEnd:
    m_OverrideState: 1
    m_Value: 30
  gaussianMaxRadius:
    m_OverrideState: 1
    m_Value: 1
  highQualitySampling:
    m_OverrideState: 1
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 10
  aperture:
    m_OverrideState: 1
    m_Value: 5.6
  focalLength:
    m_OverrideState: 1
    m_Value: 50
  bladeCount:
    m_OverrideState: 1
    m_Value: 5
  bladeCurvature:
    m_OverrideState: 1
    m_Value: 1
  bladeRotation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-2491197206017329356
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &-2280682184319698430
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e3b43e232564d52b710c4bdc0cd6e42, type: 3}
  m_Name: ColorMapVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
  m_Gradient:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 0, g: 0, b: 0, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Offset:
    m_OverrideState: 0
    m_Value: 0
  m_Mask:
    m_OverrideState: 1
    m_Value: {x: 0, y: 1}
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
--- !u!114 &-2259466169856599562
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6bd486065ce11414fa40e631affc4900, type: 3}
  m_Name: ProbeVolumesOptions
  m_EditorClassIdentifier: 
  active: 1
  normalBias:
    m_OverrideState: 1
    m_Value: 0.05
  viewBias:
    m_OverrideState: 1
    m_Value: 0.1
  scaleBiasWithMinProbeDistance:
    m_OverrideState: 1
    m_Value: 0
  samplingNoise:
    m_OverrideState: 1
    m_Value: 0.1
  animateSamplingNoise:
    m_OverrideState: 1
    m_Value: 1
  leakReductionMode:
    m_OverrideState: 1
    m_Value: 2
  minValidDotProductValue:
    m_OverrideState: 1
    m_Value: 0.1
  occlusionOnlyReflectionNormalization:
    m_OverrideState: 1
    m_Value: 1
  intensityMultiplier:
    m_OverrideState: 1
    m_Value: 1
  skyOcclusionIntensityMultiplier:
    m_OverrideState: 1
    m_Value: 1
  worldOffset:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
--- !u!114 &-1507773925935781579
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f78702826206e0c478e7ba859ee1aa90, type: 3}
  m_Name: SharpenVol
  m_EditorClassIdentifier: 
  active: 1
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Thikness:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_OffsetX:
    m_OverrideState: 1
    m_Value: 0
  m_OffsetY:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-1142129263180103255
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 310ff0a41f6a46cba06a0c6985ead2d5, type: 3}
  m_Name: MaskVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 1
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_Inverse:
    m_OverrideState: 1
    m_Value: 0
  m_Mask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-1037214093971058648
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 310ff0a41f6a46cba06a0c6985ead2d5, type: 3}
  m_Name: MaskVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 1
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_Inverse:
    m_OverrideState: 1
    m_Value: 0
  m_Mask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-825032788343056512
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb60a22f311433c4c962b888d1393f88, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  distance:
    m_OverrideState: 1
    m_Value: 0
  cropToFit:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-652681362111125584
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-283536173001738810
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69d55d8b2a03ee44483311417b016932, type: 3}
  m_Name: RadialBlurPostProcess
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  center:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0}
  delay:
    m_OverrideState: 1
    m_Value: 0.1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
--- !u!114 &-238687248405543720
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f51d4ab7f92eb9f47a34d089e35b58d3, type: 3}
  m_Name: WeatherSettings
  m_EditorClassIdentifier: 
  active: 1
  _snow:
    m_OverrideState: 1
    m_Value: 0
  _sun:
    m_OverrideState: 1
    m_Value: 0
  _clouds:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-176097355384647360
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-169838225319294596
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70afe9e12c7a7ed47911bb608a23a8ff, type: 3}
  m_Name: SplitToning
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  highlights:
    m_OverrideState: 1
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  balance:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-124863886705547868
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f6b7cb6e2467214c960a05b4f1809e6, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &-19017835320503818
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 558a8e2b6826cf840aae193990ba9f2e, type: 3}
  m_Name: ShadowsMidtonesHighlights
  m_EditorClassIdentifier: 
  active: 1
  shadows:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  midtones:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  highlights:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  shadowsStart:
    m_OverrideState: 1
    m_Value: 0
  shadowsEnd:
    m_OverrideState: 1
    m_Value: 0.3
  highlightsStart:
    m_OverrideState: 1
    m_Value: 0.55
  highlightsEnd:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DefaultVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: -8295165454565937433}
  - {fileID: -6080236906858883424}
  - {fileID: -6723992901057550629}
  - {fileID: -6479825379983740399}
  - {fileID: 6831701496790480987}
  - {fileID: 6459374762129610120}
  - {fileID: -7187065683549268971}
  - {fileID: -2495327742645499210}
  - {fileID: -8227133943205580223}
  - {fileID: -8675414456923828172}
  - {fileID: 2347387627721807673}
  - {fileID: 3796350403514174358}
  - {fileID: 7427524314734265010}
  - {fileID: -6011903566279663067}
  - {fileID: -169838225319294596}
  - {fileID: 1783302360972816989}
  - {fileID: -825032788343056512}
  - {fileID: -19017835320503818}
  - {fileID: -2259466169856599562}
  - {fileID: -124863886705547868}
  - {fileID: 7301798642634593294}
  - {fileID: -9176191809257864429}
  - {fileID: -283536173001738810}
  - {fileID: 4528285826708338632}
  - {fileID: -5003712500446951850}
  - {fileID: 4624364012776978991}
  - {fileID: -9050768547719734826}
  - {fileID: 267444636819981768}
  - {fileID: -8743054661567825574}
  - {fileID: -8990916391055664120}
  - {fileID: 7531683603816726437}
--- !u!114 &243778182156550420
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 16
  fogDensity:
    m_OverrideState: 1
    m_Value: 0
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.5
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 1
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &267444636819981768
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &408942672861135691
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &606866056551950733
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &947545613644277219
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &1102741922048641518
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 58b92b6b40be8f645b8b9775076b7fb3, type: 3}
  m_Name: JpegVol
  m_EditorClassIdentifier: 
  active: 1
  _intensity:
    m_OverrideState: 1
    m_Value: 0
  _blockSize:
    m_OverrideState: 1
    m_Value: 7
  _quantization:
    m_OverrideState: 1
    m_Value: 15
  _distortionScale:
    m_OverrideState: 1
    m_Value: 0
  _applyToY:
    m_OverrideState: 1
    m_Value: 0
  _applyToChroma:
    m_OverrideState: 1
    m_Value: 0
  _applyToGlitch:
    m_OverrideState: 1
    m_Value: 0
  _fps:
    m_OverrideState: 1
    m_Value: 0
  _fpsBreak:
    m_OverrideState: 1
    m_Value: 0
  _fpsLag:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  _fpsBlending:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  _quantSpread:
    m_OverrideState: 1
    m_Value: 0
  _scanlineDrift:
    m_OverrideState: 1
    m_Value: 0
  _scanlineRes:
    m_OverrideState: 1
    m_Value: 720
  _scanlinesFps:
    m_OverrideState: 1
    m_Value: 120
  _channelShiftPow:
    m_OverrideState: 1
    m_Value: 0
  _channelShiftX:
    m_OverrideState: 1
    m_Value: 0
  _channelShiftY:
    m_OverrideState: 1
    m_Value: 0
  _channelShiftSpread:
    m_OverrideState: 1
    m_Value: 0
  _noise:
    m_OverrideState: 1
    m_Value: 0
  _noiseBilinear:
    m_OverrideState: 1
    m_Value: 0
  _distortionTex:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
--- !u!114 &1160461651306429425
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f87d2427ed3459e8c4345aacc689427, type: 3}
  m_Name: BlurVol
  m_EditorClassIdentifier: 
  active: 1
  m_Radius:
    m_OverrideState: 1
    m_Value: 0
  m_Radial:
    m_OverrideState: 1
    m_Value: 0
  m_Samples:
    m_OverrideState: 1
    m_Value: 9
  m_Aspect:
    m_OverrideState: 1
    m_Value: 0
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
--- !u!114 &1320791676966586620
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &1496599639898835795
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e6276bb40355174d8ef50b1aa690158, type: 3}
  m_Name: PosterizeVol
  m_EditorClassIdentifier: 
  active: 1
  m_Count:
    m_OverrideState: 1
    m_Value: 64
--- !u!114 &1759950628005717650
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d94e4d16c32d4354919ca310e9b07591, type: 3}
  m_Name: AdjustmentsVol
  m_EditorClassIdentifier: 
  active: 1
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_Threshold:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Hue:
    m_OverrideState: 1
    m_Value: 0
  m_Saturation:
    m_OverrideState: 1
    m_Value: 0
  m_Brightness:
    m_OverrideState: 1
    m_Value: 0
  m_Contrast:
    m_OverrideState: 1
    m_Value: 0
  m_Alpha:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &1783302360972816989
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e021b4c809a781e468c2988c016ebbea, type: 3}
  m_Name: ColorLookup
  m_EditorClassIdentifier: 
  active: 1
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  contribution:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2102273860731944015
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4cf4295a500eda458c2576fd17945a6, type: 3}
  m_Name: BFIEffect
  m_EditorClassIdentifier: 
  active: 1
  Mode:
    m_OverrideState: 1
    m_Value: 0
  Intensity:
    m_OverrideState: 1
    m_Value: 1
  BrightnessCompensation:
    m_OverrideState: 1
    m_Value: 1.5
  ManualBrightness:
    m_OverrideState: 1
    m_Value: 1
  GammaCorrection:
    m_OverrideState: 1
    m_Value: 1
  BlackFrameDuration:
    m_OverrideState: 1
    m_Value: 0.5
  PhaseOffset:
    m_OverrideState: 1
    m_Value: 0
  SyncToRefreshRate:
    m_OverrideState: 1
    m_Value: 1
  FlickerReductionMode:
    m_OverrideState: 1
    m_Value: 2
  TemporalSmoothing:
    m_OverrideState: 1
    m_Value: 0.2
  EdgePreservation:
    m_OverrideState: 1
    m_Value: 0.8
  MotionAdaptive:
    m_OverrideState: 1
    m_Value: 0
  MotionSensitivity:
    m_OverrideState: 1
    m_Value: 1
  MotionThreshold:
    m_OverrideState: 1
    m_Value: 0.01
  AdaptationSpeed:
    m_OverrideState: 1
    m_Value: 2
  DitheringStrength:
    m_OverrideState: 1
    m_Value: 0.1
  SubpixelPrecision:
    m_OverrideState: 1
    m_Value: 1
  DebugVisualization:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2347387627721807673
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  skipIterations:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0.9
  intensity:
    m_OverrideState: 1
    m_Value: 0
  scatter:
    m_OverrideState: 1
    m_Value: 0.7
  clamp:
    m_OverrideState: 1
    m_Value: 65472
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 1
    m_Value: 0
  downscale:
    m_OverrideState: 1
    m_Value: 0
  maxIterations:
    m_OverrideState: 1
    m_Value: 6
  dirtTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2460199399599408622
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba41c240f778d164abc40f8dca90fc16, type: 3}
  m_Name: AsciiVol
  m_EditorClassIdentifier: 
  active: 1
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Ascii:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Mapping:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.032258064, g: 0.032258064, b: 0.032258064, a: 0.032258064}
      - {r: 0.06451613, g: 0.06451613, b: 0.06451613, a: 0.06451613}
      - {r: 0.09677419, g: 0.09677419, b: 0.09677419, a: 0.09677419}
      - {r: 0.12903225, g: 0.12903225, b: 0.12903225, a: 0.12903225}
      - {r: 0.16129032, g: 0.16129032, b: 0.16129032, a: 0.16129032}
      - {r: 0.19354838, g: 0.19354838, b: 0.19354838, a: 0.19354838}
      - {r: 0.22580644, g: 0.22580644, b: 0.22580644, a: 0.22580644}
      - {r: 0.2580645, g: 0.2580645, b: 0.2580645, a: 0.2580645}
      - {r: 0.29032257, g: 0.29032257, b: 0.29032257, a: 0.29032257}
      - {r: 0.32258064, g: 0.32258064, b: 0.32258064, a: 0.32258064}
      - {r: 0.3548387, g: 0.3548387, b: 0.3548387, a: 0.3548387}
      - {r: 0.38709676, g: 0.38709676, b: 0.38709676, a: 0.38709676}
      - {r: 0.41935483, g: 0.41935483, b: 0.41935483, a: 0.41935483}
      - {r: 0.4516129, g: 0.4516129, b: 0.4516129, a: 0.4516129}
      - {r: 0.48387095, g: 0.48387095, b: 0.48387095, a: 0.48387095}
      - {r: 0.516129, g: 0.516129, b: 0.516129, a: 0.516129}
      - {r: 0.5483871, g: 0.5483871, b: 0.5483871, a: 0.5483871}
      - {r: 0.58064514, g: 0.58064514, b: 0.58064514, a: 0.58064514}
      - {r: 0.61290324, g: 0.61290324, b: 0.61290324, a: 0.61290324}
      - {r: 0.6451613, g: 0.6451613, b: 0.6451613, a: 0.6451613}
      - {r: 0.67741936, g: 0.67741936, b: 0.67741936, a: 0.67741936}
      - {r: 0.7096774, g: 0.7096774, b: 0.7096774, a: 0.7096774}
      - {r: 0.7419355, g: 0.7419355, b: 0.7419355, a: 0.7419355}
      - {r: 0.7741935, g: 0.7741935, b: 0.7741935, a: 0.7741935}
      - {r: 0.8064516, g: 0.8064516, b: 0.8064516, a: 0.8064516}
      - {r: 0.83870965, g: 0.83870965, b: 0.83870965, a: 0.83870965}
      - {r: 0.87096775, g: 0.87096775, b: 0.87096775, a: 0.87096775}
      - {r: 0.9032258, g: 0.9032258, b: 0.9032258, a: 0.9032258}
      - {r: 0.9354839, g: 0.9354839, b: 0.9354839, a: 0.9354839}
      - {r: 0.9677419, g: 0.9677419, b: 0.9677419, a: 0.9677419}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Image:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_Solid:
    m_OverrideState: 1
    m_Value: 0
  m_Gradient:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Depth:
    m_OverrideState: 1
    m_Value: 1
  m_Fps:
    m_OverrideState: 1
    m_Value: 0
  m_Noise:
    m_OverrideState: 1
    m_Value: 1
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2473084068176322484
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &3381414175099987829
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb2952b95454955ba507c3d96778a76, type: 3}
  m_Name: VhsVol
  m_EditorClassIdentifier: 
  active: 1
  _weight:
    m_OverrideState: 1
    m_Value: 0
  _tape:
    m_OverrideState: 1
    m_Value: 0
  _shades:
    m_OverrideState: 1
    m_Value: 0
  _rocking:
    m_OverrideState: 1
    m_Value: 0
  _squeeze:
    m_OverrideState: 1
    m_Value: 0
  _density:
    m_OverrideState: 1
    m_Value: 0
  _intensity:
    m_OverrideState: 1
    m_Value: 0
  _scale:
    m_OverrideState: 1
    m_Value: 1
  _flickering:
    m_OverrideState: 1
    m_Value: 0
  _color:
    m_OverrideState: 1
    m_Value: {r: 1, g: 0, b: 0, a: 1}
  _bleed:
    m_OverrideState: 1
    m_Value: 0.7
  _flow:
    m_OverrideState: 1
    m_Value: 1
  _pulsation:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &3796350403514174358
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &3806777522716925009
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bba95ee541be4fe3b76c758f1424e975, type: 3}
  m_Name: FlowVol
  m_EditorClassIdentifier: 
  active: 1
  m_Fade:
    m_OverrideState: 1
    m_Value: 0
  m_Strain:
    m_OverrideState: 1
    m_Value: 0
  m_Samples:
    m_OverrideState: 1
    m_Value: 1
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Flow:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Print:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0
  m_Focus:
    m_OverrideState: 1
    m_Value: 0
  m_Fps:
    m_OverrideState: 0
    m_Value: 60
--- !u!114 &3877634464965758382
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 584f4d053daa4e17a331821f306111c9, type: 3}
  m_Name: ChromaticVol
  m_EditorClassIdentifier: 
  active: 1
  _Weight:
    m_OverrideState: 1
    m_Value: 1
  _Intensity:
    m_OverrideState: 1
    m_Value: 0
  _Radial:
    m_OverrideState: 1
    m_Value: 0
  _Alpha:
    m_OverrideState: 1
    m_Value: 0
  _Angle:
    m_OverrideState: 1
    m_Value: 0
  _Split:
    m_OverrideState: 1
    m_Value: 0
  _Sat:
    m_OverrideState: 1
    m_Value: 1
  _Mono:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &3912256996531507949
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b1f0f34434172684f90e4c0903cf4488, type: 3}
  m_Name: DitherVol
  m_EditorClassIdentifier: 
  active: 1
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Power:
    m_OverrideState: 1
    m_Value: 0
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Pixelate:
    m_OverrideState: 1
    m_Value: 1
  m_Fps:
    m_OverrideState: 1
    m_Value: 0
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Pattern:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_NoiseScale:
    m_OverrideState: 1
    m_Value: 1
  m_Noise:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &3976784111411738820
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &4281322432347418339
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b1f0f34434172684f90e4c0903cf4488, type: 3}
  m_Name: DitherVol
  m_EditorClassIdentifier: 
  active: 1
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Power:
    m_OverrideState: 1
    m_Value: 0
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Pixelate:
    m_OverrideState: 1
    m_Value: 1
  m_Fps:
    m_OverrideState: 1
    m_Value: 0
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Pattern:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Mode:
    m_OverrideState: 1
    m_Value: 0
  m_NoiseScale:
    m_OverrideState: 1
    m_Value: 1
  m_Noise:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &4528285826708338632
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4af4565792a93d349b07be6fdb9c1fba, type: 3}
  m_Name: HazeFX
  m_EditorClassIdentifier: 
  active: 1
  enabled:
    m_OverrideState: 1
    m_Value: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  density:
    m_OverrideState: 1
    m_Value: 0.02
  startDistance:
    m_OverrideState: 1
    m_Value: 0
  endDistance:
    m_OverrideState: 1
    m_Value: 1000
  directionalFalloff:
    m_OverrideState: 1
    m_Value: 4
  useAbsoluteY:
    m_OverrideState: 1
    m_Value: 1
  blurEnabled:
    m_OverrideState: 1
    m_Value: 1
  blurRadius:
    m_OverrideState: 1
    m_Value: 1.2
  blurIterations:
    m_OverrideState: 1
    m_Value: 1
  heatHazeEnabled:
    m_OverrideState: 1
    m_Value: 1
  heatHazeStrength:
    m_OverrideState: 1
    m_Value: 10
  heatHazeSpeed:
    m_OverrideState: 1
    m_Value: 1
  heatHazeTiling:
    m_OverrideState: 1
    m_Value: 10
  heatHazeTextureOverride:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  heatHazeTextureOffset:
    m_OverrideState: 1
    m_Value: -0.75
  contrastAdjustmentsEnabled:
    m_OverrideState: 1
    m_Value: 1
  midpoint:
    m_OverrideState: 1
    m_Value: 1
  contrastAmount:
    m_OverrideState: 1
    m_Value: 0.6
  fogEnabled:
    m_OverrideState: 1
    m_Value: 1
  fogColor:
    m_OverrideState: 1
    m_Value: {r: 0.9137255, g: 0.7647059, b: 0.5411765, a: 1}
  fogAmount:
    m_OverrideState: 1
    m_Value: 0.2
--- !u!114 &4624364012776978991
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.35
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 1
  Downscaling:
    m_OverrideState: 1
    m_Value: 10
  BlockSize:
    m_OverrideState: 1
    m_Value: 2
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.2
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 0
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &4897687950919398378
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &4916065642470841155
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56bfbb06e13699f4fb9caf74d92cae3d, type: 3}
  m_Name: GrayscaleVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5127650644843510164
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f8317041eee140a38873daddb4b3a5f, type: 3}
  m_Name: StylizedDetail
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blur:
    m_OverrideState: 1
    m_Value: 1
  edgePreserve:
    m_OverrideState: 1
    m_Value: 1.25
  rangeStart:
    m_OverrideState: 1
    m_Value: 10
  rangeEnd:
    m_OverrideState: 1
    m_Value: 30
--- !u!114 &5139143342481282926
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.35
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 1
  Downscaling:
    m_OverrideState: 1
    m_Value: 10
  BlockSize:
    m_OverrideState: 1
    m_Value: 2
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.2
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 0
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5141377215476061751
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f78702826206e0c478e7ba859ee1aa90, type: 3}
  m_Name: SharpenVol
  m_EditorClassIdentifier: 
  active: 1
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Thikness:
    m_OverrideState: 1
    m_Value: 0
  m_Value:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  m_OffsetX:
    m_OverrideState: 1
    m_Value: 0
  m_OffsetY:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5194083927408611635
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 772ef47f3fce34346bbb42fb4f5434b8, type: 3}
  m_Name: PixelationVol
  m_EditorClassIdentifier: 
  active: 1
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Grid:
    m_OverrideState: 1
    m_Value: 1
  m_Roundness:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
    hdr: 0
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Crisp:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5476134329396565252
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5762333216285562689
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56bfbb06e13699f4fb9caf74d92cae3d, type: 3}
  m_Name: GrayscaleVol
  m_EditorClassIdentifier: 
  active: 1
  m_Weight:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &5846766636499542624
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f0e86679a2284a4ca49d75916bcd95c3, type: 3}
  m_Name: SliceVol
  m_EditorClassIdentifier: 
  active: 1
  m_Value:
    m_OverrideState: 1
    m_Value: 0
  m_Tiling:
    m_OverrideState: 1
    m_Value: 500
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &6147169066059631111
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f51d4ab7f92eb9f47a34d089e35b58d3, type: 3}
  m_Name: WeatherSettings
  m_EditorClassIdentifier: 
  active: 1
  _snow:
    m_OverrideState: 1
    m_Value: 0
  _sun:
    m_OverrideState: 1
    m_Value: 0
  _clouds:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &6278658090990400523
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96468967439741488ad1b1af1ab44b9b, type: 3}
  m_Name: ScanlinesVol
  m_EditorClassIdentifier: 
  active: 1
  m_Intensity:
    m_OverrideState: 1
    m_Value: 0
  m_Count:
    m_OverrideState: 1
    m_Value: 570
  m_Speed:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value: 1
  m_Flicker:
    m_OverrideState: 1
    m_Value: 0
  m_Grad:
    m_OverrideState: 1
    m_Value: 0.33
  m_Animation:
    m_OverrideState: 1
    m_Value: 1
  m_GradSpeed:
    m_OverrideState: 1
    m_Value: 0.2
  m_Flip:
    m_OverrideState: 1
    m_Value: 0
  m_GradColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0.07}
--- !u!114 &6335840025787173121
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f8f6e43869a48f991285995d4df1e93, type: 3}
  m_Name: ColorGrading
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  blueShadows:
    m_OverrideState: 1
    m_Value: 0
  greenShadows:
    m_OverrideState: 1
    m_Value: 0
  redHighlights:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  vibrance:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &6386944235364999091
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 03592b0b6d784e6da6ea74233ca33d52, type: 3}
  m_Name: GrainVol
  m_EditorClassIdentifier: 
  active: 1
  m_Grain:
    m_OverrideState: 1
    m_Value: 0
  m_Response:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
  m_Brightness:
    m_OverrideState: 1
    m_Value: 0
  m_Hue:
    m_OverrideState: 1
    m_Value: 0
  m_Saturation:
    m_OverrideState: 1
    m_Value: 0
  m_GainTex:
    m_OverrideState: 1
    m_Value: 8
  m_Alpha:
    m_OverrideState: 1
    m_Value: 1
  m_Color:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_Fps:
    m_OverrideState: 1
    m_Value: 60
  m_Scale:
    m_OverrideState: 1
    m_Value: 0
  m_Texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &6428363532799863184
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 318746fcd28a4c69b04bd76bb679e99e, type: 3}
  m_Name: BloomVol
  m_EditorClassIdentifier: 
  active: 1
  m_Intencity:
    m_OverrideState: 1
    m_Value: 0
  m_Threshold:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0.57
          value: 0
          inSlope: 8
          outSlope: 8
          tangentMode: 0
          weightedMode: 3
          inWeight: 0
          outWeight: 0.1732
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0.3
          outSlope: 0.3
          tangentMode: 0
          weightedMode: 3
          inWeight: 0.32
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.07728752, g: 0.07728752, b: 0.07728752, a: 0.07728752}
      - {r: 0.25423908, g: 0.25423908, b: 0.25423908, a: 0.25423908}
      - {r: 0.38693804, g: 0.38693804, b: 0.38693804, a: 0.38693804}
      - {r: 0.49503732, g: 0.49503732, b: 0.49503732, a: 0.49503732}
      - {r: 0.5865047, g: 0.5865047, b: 0.5865047, a: 0.5865047}
      - {r: 0.66545904, g: 0.66545904, b: 0.66545904, a: 0.66545904}
      - {r: 0.7343008, g: 0.7343008, b: 0.7343008, a: 0.7343008}
      - {r: 0.7945121, g: 0.7945121, b: 0.7945121, a: 0.7945121}
      - {r: 0.84701, g: 0.84701, b: 0.84701, a: 0.84701}
      - {r: 0.8923147, g: 0.8923147, b: 0.8923147, a: 0.8923147}
      - {r: 0.93061954, g: 0.93061954, b: 0.93061954, a: 0.93061954}
      - {r: 0.9617921, g: 0.9617921, b: 0.9617921, a: 0.9617921}
      - {r: 0.9852997, g: 0.9852997, b: 0.9852997, a: 0.9852997}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Scatter:
    m_OverrideState: 1
    m_Value: 0.7
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Flicker:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &6442382305085324491
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 584f4d053daa4e17a331821f306111c9, type: 3}
  m_Name: ChromaticVol
  m_EditorClassIdentifier: 
  active: 1
  _Weight:
    m_OverrideState: 1
    m_Value: 1
  _Intensity:
    m_OverrideState: 1
    m_Value: 0
  _Radial:
    m_OverrideState: 1
    m_Value: 0
  _Alpha:
    m_OverrideState: 1
    m_Value: 0
  _Angle:
    m_OverrideState: 1
    m_Value: 0
  _Split:
    m_OverrideState: 1
    m_Value: 0
  _Sat:
    m_OverrideState: 1
    m_Value: 1
  _Mono:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &6459374762129610120
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccf1aba9553839d41ae37dd52e9ebcce, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
  clamp:
    m_OverrideState: 1
    m_Value: 0.05
--- !u!114 &6831701496790480987
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5485954d14dfb9a4c8ead8edb0ded5b1, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 1
  lift:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gamma:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gain:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
--- !u!114 &6996808672472957592
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e6276bb40355174d8ef50b1aa690158, type: 3}
  m_Name: PosterizeVol
  m_EditorClassIdentifier: 
  active: 1
  m_Count:
    m_OverrideState: 1
    m_Value: 64
--- !u!114 &7023383392023874865
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &7301798642634593294
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9392aacf3349be549bb1fd9eece49154, type: 3}
  m_Name: ButoVolumetricFog
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  qualityLevel:
    m_OverrideState: 1
    m_Value: 2
  gridPixelSize:
    m_OverrideState: 1
    m_Value: 12
  gridSizeZ:
    m_OverrideState: 1
    m_Value: 160
  maxDistanceVolumetric:
    m_OverrideState: 1
    m_Value: 64
  depthRatio:
    m_OverrideState: 1
    m_Value: 2
  temporalAALighting:
    m_OverrideState: 1
    m_Value: 0.05
  temporalAAMedia:
    m_OverrideState: 1
    m_Value: 0.05
  fogDensity:
    m_OverrideState: 1
    m_Value: 5
  anisotropy:
    m_OverrideState: 1
    m_Value: 0.2
  lightIntensity:
    m_OverrideState: 1
    m_Value: 1
  densityInLight:
    m_OverrideState: 1
    m_Value: 1
  densityInShadow:
    m_OverrideState: 1
    m_Value: 1
  overrideDefaultMaxLightDistance:
    m_OverrideState: 1
    m_Value: 0
  maxLightDistance:
    m_OverrideState: 1
    m_Value: 64
  baseHeight:
    m_OverrideState: 1
    m_Value: 0
  attenuationBoundarySize:
    m_OverrideState: 1
    m_Value: 10
  litColor:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  shadowedColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  emitColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  colorRamp:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  colorRampId:
    m_OverrideState: 1
    m_Value: 0
  colorInfluence:
    m_OverrideState: 1
    m_Value: 0
  directionalForward:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  directionalBack:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  directionalRatio:
    m_OverrideState: 1
    m_Value: 1
  noiseTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  octaves:
    m_OverrideState: 1
    m_Value: 1
  lacunarity:
    m_OverrideState: 1
    m_Value: 2
  gain:
    m_OverrideState: 1
    m_Value: 0.3
  noiseTiling:
    m_OverrideState: 1
    m_Value: 30
  noiseWindSpeed:
    m_OverrideState: 1
    m_Value: {x: 0, y: -1, z: 0}
  noiseMap:
    m_OverrideState: 1
    m_Value: {x: 0, y: 1}
  volumeNoise:
    m_OverrideState: 1
    m_Value:
      frequency: 8
      octaves: 4
      lacunarity: 2
      gain: 0.35
      seed: 0
      noiseType: 2
      noiseQuality: 2
      invert: 0
      userTexture: {fileID: 0}
--- !u!114 &7427524314734265010
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  colorFilter:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &7531683603816726437
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &7738516465883471966
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 772ef47f3fce34346bbb42fb4f5434b8, type: 3}
  m_Name: PixelationVol
  m_EditorClassIdentifier: 
  active: 1
  m_Scale:
    m_OverrideState: 1
    m_Value: 1
  m_Grid:
    m_OverrideState: 1
    m_Value: 1
  m_Roundness:
    m_OverrideState: 1
    m_Value: 0
  m_Color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 0}
    hdr: 0
  m_Palette:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  m_Impact:
    m_OverrideState: 1
    m_Value: 0
  m_Crisp:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &7984869015748158364
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 318746fcd28a4c69b04bd76bb679e99e, type: 3}
  m_Name: BloomVol
  m_EditorClassIdentifier: 
  active: 1
  m_Intencity:
    m_OverrideState: 1
    m_Value: 0
  m_Threshold:
    m_OverrideState: 1
    m_Value:
      _curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0.57
          value: 0
          inSlope: 8
          outSlope: 8
          tangentMode: 0
          weightedMode: 3
          inWeight: 0
          outWeight: 0.1732
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0.3
          outSlope: 0.3
          tangentMode: 0
          weightedMode: 3
          inWeight: 0.32
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _pixels:
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0, g: 0, b: 0, a: 0}
      - {r: 0.07728752, g: 0.07728752, b: 0.07728752, a: 0.07728752}
      - {r: 0.25423908, g: 0.25423908, b: 0.25423908, a: 0.25423908}
      - {r: 0.38693804, g: 0.38693804, b: 0.38693804, a: 0.38693804}
      - {r: 0.49503732, g: 0.49503732, b: 0.49503732, a: 0.49503732}
      - {r: 0.5865047, g: 0.5865047, b: 0.5865047, a: 0.5865047}
      - {r: 0.66545904, g: 0.66545904, b: 0.66545904, a: 0.66545904}
      - {r: 0.7343008, g: 0.7343008, b: 0.7343008, a: 0.7343008}
      - {r: 0.7945121, g: 0.7945121, b: 0.7945121, a: 0.7945121}
      - {r: 0.84701, g: 0.84701, b: 0.84701, a: 0.84701}
      - {r: 0.8923147, g: 0.8923147, b: 0.8923147, a: 0.8923147}
      - {r: 0.93061954, g: 0.93061954, b: 0.93061954, a: 0.93061954}
      - {r: 0.9617921, g: 0.9617921, b: 0.9617921, a: 0.9617921}
      - {r: 0.9852997, g: 0.9852997, b: 0.9852997, a: 0.9852997}
      - {r: 1, g: 1, b: 1, a: 1}
  m_Scatter:
    m_OverrideState: 1
    m_Value: 0.7
  m_Color:
    m_OverrideState: 1
    m_Value:
      _grad:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 0}
        key1: {r: 1, g: 1, b: 1, a: 0}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 0
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      _pixels:
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
      - {r: 1, g: 1, b: 1, a: 0}
  m_Flicker:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &8342121184460883171
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fbf49926625114084d7e0c0cffe8d1, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  active: 1
  preset:
    m_OverrideState: 1
    m_Value: 2
  mode:
    m_OverrideState: 1
    m_Value: 1
  renderingPath:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  deinterleaving:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 0
  noiseType:
    m_OverrideState: 1
    m_Value: 0
  debugMode:
    m_OverrideState: 1
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.8
  maxRadiusPixels:
    m_OverrideState: 1
    m_Value: 128
  bias:
    m_OverrideState: 1
    m_Value: 0.05
  intensity:
    m_OverrideState: 1
    m_Value: 0
  useMultiBounce:
    m_OverrideState: 1
    m_Value: 0
  multiBounceInfluence:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0.25
  offscreenSamplesContribution:
    m_OverrideState: 1
    m_Value: 0
  maxDistance:
    m_OverrideState: 1
    m_Value: 150
  distanceFalloff:
    m_OverrideState: 1
    m_Value: 50
  perPixelNormals:
    m_OverrideState: 1
    m_Value: 2
  baseColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  temporalFilterEnabled:
    m_OverrideState: 1
    m_Value: 0
  varianceClipping:
    m_OverrideState: 1
    m_Value: 1
  blurType:
    m_OverrideState: 1
    m_Value: 2
  sharpness:
    m_OverrideState: 1
    m_Value: 8
  colorBleedingEnabled:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 1
  brightnessMask:
    m_OverrideState: 1
    m_Value: 1
  brightnessMaskRange:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0.5}
    min: 0
    max: 2
--- !u!114 &8543301193609782139
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f73d2631a9741743935bb8e4363a105, type: 3}
  m_Name: MoshVol
  m_EditorClassIdentifier: 
  active: 1
  m_Fade:
    m_OverrideState: 1
    m_Value: 0
  m_Strain:
    m_OverrideState: 1
    m_Value: 0
  m_Samples:
    m_OverrideState: 1
    m_Value: 1
  m_Angle:
    m_OverrideState: 1
    m_Value: 0
  m_Flow:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  m_Tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 0}
  m_BlockIntensity:
    m_OverrideState: 1
    m_Value: 0
  m_BlockSize:
    m_OverrideState: 1
    m_Value: 8
  m_Retention:
    m_OverrideState: 1
    m_Value: 0.5
  m_MotionThreshold:
    m_OverrideState: 1
    m_Value: 0.1
  m_GlitchIntensity:
    m_OverrideState: 1
    m_Value: 0
  m_TimeScale:
    m_OverrideState: 1
    m_Value: 1
  m_Compression:
    m_OverrideState: 1
    m_Value: 0
  m_Print:
    m_OverrideState: 1
    m_Value: 0
  m_Adaptive:
    m_OverrideState: 1
    m_Value: 0
  m_Focus:
    m_OverrideState: 1
    m_Value: 0
