{"scopedRegistries": [{"name": "A* Pathfinding Project", "url": "https://arongranberg.com/packages/81c7e795f8545d505aebdbb793fd55456283d908871e1/", "scopes": ["com.arongranberg.astar"]}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://registry.npmjs.com", "scopes": ["jp.kei<PERSON>ro"]}], "dependencies": {"com.arongranberg.astar": "5.4.1", "com.cysharp.csprojmodifier": "https://github.com/Cysharp/CsprojModifier.git?path=src/CsprojModifier/Assets/CsprojModifier", "com.cysharp.unitask": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "com.github-glitchenzo.nugetforunity": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "com.justinpbarnett.unity-mcp": "https://github.com/justinpbarnett/unity-mcp.git?path=/UnityMcpBridge", "com.kyrylokuzyk.primetween": "file:../Assets/Plugins/PrimeTween/internal/com.kyrylokuzyk.primetween.tgz", "com.matthewminer.position-visualizer": "https://github.com/mminer/position-visualizer.git", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.tilemap": "1.0.0", "com.unity.analytics": "3.8.1", "com.unity.burst": "1.8.24", "com.unity.cinemachine": "3.1.4", "com.unity.collections": "2.5.1", "com.unity.editorcoroutines": "1.0.0", "com.unity.entities": "1.3.14", "com.unity.formats.fbx": "5.1.3", "com.unity.ide.cursor": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "com.unity.ide.rider": "3.0.36", "com.unity.ide.visualstudio": "2.0.23", "com.unity.inputsystem": "1.14.2", "com.unity.mathematics": "1.3.2", "com.unity.memoryprofiler": "1.1.7", "com.unity.multiplayer.center": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.performance.profile-analyzer": "1.2.3", "com.unity.postprocessing": "3.5.0", "com.unity.project-auditor": "1.0.1", "com.unity.render-pipelines.universal": "17.1.0", "com.unity.render-pipelines.universal-config": "17.0.3", "com.unity.services.cloud-build": "2.0.4", "com.unity.splines": "2.8.1", "com.unity.test-framework": "1.5.1", "com.unity.timeline": "1.8.9", "com.unity.ugui": "2.0.0", "jp.keijiro.duotone": "2.2.1", "jp.keijiro.kino.post-processing.eight.universal": "1.0.0", "jp.keijiro.klak.ui-toolkit-assets": "0.2.0", "org.happy-turtle.order-independent-transparency": "file:org.happy-turtle.order-independent-transparency", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.nvidia": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}}