using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance
{
    /// <summary>
    /// Core class for the Cadance system. Handles event registration, dispatching, and music playback control.
    /// Provides full compatibility with Koreographer's feature set including FMOD and Unity Audio integration.
    /// </summary>
    [DefaultExecutionOrder(-200)]
    public class Cadance : MonoBehaviour
    {
        #region Singleton Pattern

        private static Cadance _instance;

        /// <summary>
        /// Gets the singleton instance of Cadance.
        /// </summary>
        public static Cadance Instance
        {
            get
            {
                if (_instance == null)
                {
                    // Find or create an instance at runtime
                    _instance = FindObjectOfType<Cadance>();

                    if (_instance == null)
                    {
                        GameObject go = new GameObject("Cadance");
                        _instance = go.AddComponent<Cadance>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            _instance = null;
            Debug.Log("[Cadance] Static data reset for domain reload");
        }

        #endregion

        #region Fields and Properties

        [Header("Event Delay Configuration")]
        [SerializeField] private float eventDelayInSeconds = 0f;

        /// <summary>
        /// The music playback controller for this Cadance instance.
        /// </summary>
        public ICadancePlayer musicPlaybackController;

        /// <summary>
        /// Gets or sets the event delay in seconds.
        /// </summary>
        public float EventDelayInSeconds
        {
            get => eventDelayInSeconds;
            set => eventDelayInSeconds = value;
        }

        // Loaded Cadance assets
        private List<CadanceAsset> loadedCadances = new List<CadanceAsset>();

        // Event processing data
        private Dictionary<string, EventObj> eventObjects = new Dictionary<string, EventObj>();

        // Timing records for delayed processing
        private Queue<TimingRecord> delayQueue = new Queue<TimingRecord>();

        // Current processing state
        private int currentSampleTime = 0;
        private int currentSampleTimeDelta = 0;
        private double currentProcessingTime = 0.0;

        #endregion

        #region Event Systems

        /// <summary>
        /// Delegate for Cadance event callbacks
        /// </summary>
        /// <param name="evt">The event being processed</param>
        public delegate void CadanceEventCallback(CadanceEvent evt);

        /// <summary>
        /// Delegate for Cadance event callbacks with timing information
        /// </summary>
        /// <param name="evt">The event being processed</param>
        /// <param name="sampleTime">The current sample time</param>
        /// <param name="sampleTimeDelta">The sample time delta</param>
        /// <param name="deltaSlice">The delta slice</param>
        public delegate void CadanceEventCallbackWithTime(CadanceEvent evt, int sampleTime, int sampleTimeDelta, DeltaSlice deltaSlice);

        /// <summary>
        /// Registers for events with the specified ID.
        /// </summary>
        /// <param name="eventID">The event ID to register for</param>
        /// <param name="callback">The callback to invoke when the event occurs</param>
        public void RegisterForEvents(string eventID, CadanceEventCallback callback)
        {
            if (string.IsNullOrEmpty(eventID))
            {
                Debug.LogError("[Cadance] Cannot register for events with null or empty eventID");
                return;
            }

            if (callback == null)
            {
                Debug.LogError("[Cadance] Cannot register null callback for event: " + eventID);
                return;
            }

            // Get or create event object
            if (!eventObjects.ContainsKey(eventID))
            {
                eventObjects[eventID] = new EventObj(eventID);
            }

            eventObjects[eventID].RegisterCallback(callback);
        }

        /// <summary>
        /// Registers for events with timing information.
        /// </summary>
        /// <param name="eventID">The event ID to register for</param>
        /// <param name="callback">The callback to invoke when the event occurs</param>
        public void RegisterForEventsWithTime(string eventID, CadanceEventCallbackWithTime callback)
        {
            if (string.IsNullOrEmpty(eventID))
            {
                Debug.LogError("[Cadance] Cannot register for events with null or empty eventID");
                return;
            }

            if (callback == null)
            {
                Debug.LogError("[Cadance] Cannot register null callback for event: " + eventID);
                return;
            }

            // Get or create event object
            if (!eventObjects.ContainsKey(eventID))
            {
                eventObjects[eventID] = new EventObj(eventID);
            }

            eventObjects[eventID].RegisterCallbackWithTime(callback);
        }

        /// <summary>
        /// Unregisters from events with the specified ID.
        /// </summary>
        /// <param name="eventID">The event ID to unregister from</param>
        /// <param name="callback">The callback to remove</param>
        public void UnregisterForEvents(string eventID, CadanceEventCallback callback)
        {
            if (string.IsNullOrEmpty(eventID) || callback == null)
            {
                return;
            }

            if (eventObjects.ContainsKey(eventID))
            {
                eventObjects[eventID].UnregisterCallback(callback);
            }
        }

        /// <summary>
        /// Unregisters from events with timing information.
        /// </summary>
        /// <param name="eventID">The event ID to unregister from</param>
        /// <param name="callback">The callback to remove</param>
        public void UnregisterForEventsWithTime(string eventID, CadanceEventCallbackWithTime callback)
        {
            if (string.IsNullOrEmpty(eventID) || callback == null)
            {
                return;
            }

            if (eventObjects.ContainsKey(eventID))
            {
                eventObjects[eventID].UnregisterCallbackWithTime(callback);
            }
        }

        /// <summary>
        /// Unregisters all events for the specified object.
        /// </summary>
        /// <param name="obj">The object to unregister</param>
        public void UnregisterForAllEvents(object obj)
        {
            if (obj == null) return;

            foreach (var eventObj in eventObjects.Values)
            {
                eventObj.UnregisterAllCallbacksForObject(obj);
            }
        }

        /// <summary>
        /// Triggers the event for all registered callbacks.
        /// </summary>
        /// <param name="evt">The event to trigger</param>
        internal void TriggerEvent(CadanceEvent evt)
        {
            if (evt == null || string.IsNullOrEmpty(evt.EventID))
            {
                Debug.LogWarning("[Cadance] TriggerEvent called with null event or empty EventID");
                return;
            }

            Debug.Log($"[Cadance] *** TRIGGER EVENT DEBUG *** Event '{evt.EventID}' received for triggering");

            if (eventObjects.ContainsKey(evt.EventID))
            {
                var deltaSlice = new DeltaSlice(currentSampleTime, currentSampleTimeDelta);
                Debug.Log($"[Cadance] *** TRIGGERING CALLBACKS *** Found {eventObjects[evt.EventID].GetCallbackCount()} callbacks for event '{evt.EventID}'");
                eventObjects[evt.EventID].TriggerEvent(evt, currentSampleTime, currentSampleTimeDelta, deltaSlice);
            }
            else
            {
                Debug.LogWarning($"[Cadance] *** NO CALLBACKS REGISTERED *** No callbacks found for event '{evt.EventID}'. Available events: {string.Join(", ", eventObjects.Keys)}");
            }
        }

        #endregion

        #region Cadance Asset Management

        /// <summary>
        /// Loads a Koreography asset into the system for backward compatibility.
        /// </summary>
        /// <param name="koreography">The Koreography asset to load</param>
        public void LoadKoreography(SonicBloom.Koreo.Koreography koreography)
        {
            if (koreography == null)
            {
                Debug.LogError("[Cadance] Cannot load null Koreography asset");
                return;
            }

            // Check if already loaded by converting to a unique identifier
            string koreographyId = $"Koreography_{koreography.GetInstanceID()}";

            // Convert Koreography to CadanceAsset format
            CadanceAsset convertedCadance = ConvertKoreographyToCadance(koreography);
            if (convertedCadance != null)
            {
                convertedCadance.name = koreographyId;
                LoadCadance(convertedCadance);
                Debug.Log($"[Cadance] Loaded Koreography '{koreography.name}' as CadanceAsset");
            }
        }



        /// <summary>
        /// Unloads a Koreography asset from the system for backward compatibility.
        /// </summary>
        /// <param name="koreography">The Koreography asset to unload</param>
        public void UnloadKoreography(SonicBloom.Koreo.Koreography koreography)
        {
            if (koreography == null)
            {
                Debug.LogError("[Cadance] Cannot unload null Koreography asset");
                return;
            }

            // Find the converted CadanceAsset by name
            string koreographyId = $"Koreography_{koreography.GetInstanceID()}";

            // Find and unload the converted CadanceAsset
            for (int i = loadedCadances.Count - 1; i >= 0; i--)
            {
                if (loadedCadances[i].name == koreographyId)
                {
                    UnloadCadance(loadedCadances[i]);
                    Debug.Log($"[Cadance] Unloaded Koreography '{koreography.name}' from CadanceAsset");
                    break;
                }
            }
        }

        /// <summary>
        /// Loads an FMODKoreographySet into the system for backward compatibility.
        /// </summary>
        /// <param name="koreographySet">The FMODKoreographySet to load</param>
        public void LoadFMODKoreographySet(SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet koreographySet)
        {
            if (koreographySet == null)
            {
                Debug.LogError("[Cadance] Cannot load null FMODKoreographySet");
                return;
            }

            int loadedCount = 0;
            foreach (var koreoEntry in koreographySet.koreographies)
            {
                if (koreoEntry.koreo != null)
                {
                    LoadKoreography(koreoEntry.koreo);
                    loadedCount++;
                }
            }

            Debug.Log($"[Cadance] Loaded FMODKoreographySet '{koreographySet.name}' with {loadedCount} Koreography files");
        }

        /// <summary>
        /// Unloads an FMODKoreographySet from the system for backward compatibility.
        /// </summary>
        /// <param name="koreographySet">The FMODKoreographySet to unload</param>
        public void UnloadFMODKoreographySet(SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet koreographySet)
        {
            if (koreographySet == null)
            {
                Debug.LogError("[Cadance] Cannot unload null FMODKoreographySet");
                return;
            }

            int unloadedCount = 0;
            foreach (var koreoEntry in koreographySet.koreographies)
            {
                if (koreoEntry.koreo != null)
                {
                    UnloadKoreography(koreoEntry.koreo);
                    unloadedCount++;
                }
            }

            Debug.Log($"[Cadance] Unloaded FMODKoreographySet '{koreographySet.name}' with {unloadedCount} Koreography files");
        }

        /// <summary>
        /// Determines whether the specified FMODKoreographySet is loaded.
        /// </summary>
        /// <param name="koreographySet">The FMODKoreographySet to check</param>
        /// <returns>True if all Koreography files in the set are loaded</returns>
        public bool IsFMODKoreographySetLoaded(SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet koreographySet)
        {
            if (koreographySet == null) return false;

            foreach (var koreoEntry in koreographySet.koreographies)
            {
                if (koreoEntry.koreo != null)
                {
                    string koreographyId = $"Koreography_{koreoEntry.koreo.GetInstanceID()}";
                    bool isLoaded = loadedCadances.Any(c => c.name == koreographyId);
                    if (!isLoaded) return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Loads a Cadance asset into the system.
        /// </summary>
        /// <param name="cadance">The Cadance asset to load</param>
        /// <param name="isSceneSpecific">Whether this asset should be unloaded when the scene changes</param>
        public void LoadCadance(CadanceAsset cadance, bool isSceneSpecific = false)
        {
            if (cadance == null)
            {
                Debug.LogError("[Cadance] Cannot load null Cadance asset");
                return;
            }

            if (loadedCadances.Contains(cadance))
            {
                Debug.LogWarning($"[Cadance] Cadance asset '{cadance.name}' is already loaded");
                return;
            }

            // Set scene tracking information
            if (isSceneSpecific)
            {
                cadance.IsSceneSpecific = true;
                cadance.LoadedFromScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            }

            loadedCadances.Add(cadance);

            // Register event objects for all tracks in the Cadance
            foreach (var track in cadance.Tracks)
            {
                if (track != null && !string.IsNullOrEmpty(track.EventID))
                {
                    if (!eventObjects.ContainsKey(track.EventID))
                    {
                        eventObjects[track.EventID] = new EventObj(track.EventID);
                    }
                }
            }

            Debug.Log($"[Cadance] Loaded Cadance asset: {cadance.name} (Scene-specific: {isSceneSpecific})");
        }

        /// <summary>
        /// Unloads a Cadance asset from the system.
        /// </summary>
        /// <param name="cadance">The Cadance asset to unload</param>
        public void UnloadCadance(CadanceAsset cadance)
        {
            if (cadance == null) return;

            loadedCadances.Remove(cadance);
            Debug.Log($"[Cadance] Unloaded Cadance asset: {cadance.name}");
        }

        /// <summary>
        /// Determines whether the specified Cadance is loaded.
        /// </summary>
        /// <param name="cadance">The Cadance to check</param>
        /// <returns>True if the Cadance is loaded</returns>
        public bool IsCadanceLoaded(CadanceAsset cadance)
        {
            return cadance != null && loadedCadances.Contains(cadance);
        }

        /// <summary>
        /// Gets the number of loaded Cadance assets.
        /// </summary>
        /// <returns>The number of loaded Cadance assets</returns>
        public int GetNumLoadedCadances()
        {
            return loadedCadances.Count;
        }

        /// <summary>
        /// Gets the Cadance asset at the specified index.
        /// </summary>
        /// <param name="index">The index</param>
        /// <returns>The Cadance asset at the specified index</returns>
        public CadanceAsset GetCadanceAtIndex(int index)
        {
            if (index >= 0 && index < loadedCadances.Count)
                return loadedCadances[index];
            return null;
        }

        /// <summary>
        /// Gets a read-only list of all loaded Cadance assets.
        /// </summary>
        /// <returns>Read-only list of loaded Cadance assets</returns>
        public System.Collections.Generic.IReadOnlyList<CadanceAsset> GetLoadedCadances()
        {
            return loadedCadances.AsReadOnly();
        }

        #endregion

        #region Music Time APIs

        /// <summary>
        /// Gets the current sample time for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <returns>The current sample time</returns>
        public int GetSampleTimeForClip(string clipName)
        {
            if (musicPlaybackController != null)
                return musicPlaybackController.GetSampleTimeForClip(clipName);
            return 0;
        }

        /// <summary>
        /// Gets the total sample time for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <returns>The total sample time</returns>
        public int GetTotalSampleTimeForClip(string clipName)
        {
            if (musicPlaybackController != null)
                return musicPlaybackController.GetTotalSampleTimeForClip(clipName);
            return 0;
        }

        /// <summary>
        /// Determines whether the specified clip is playing.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <returns>True if the clip is playing</returns>
        public bool GetIsPlaying(string clipName)
        {
            if (musicPlaybackController != null)
                return musicPlaybackController.GetIsPlaying(clipName);
            return false;
        }

        /// <summary>
        /// Gets the pitch of the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <returns>The pitch value</returns>
        public float GetPitch(string clipName)
        {
            if (musicPlaybackController != null)
                return musicPlaybackController.GetPitch(clipName);
            return 1f;
        }

        /// <summary>
        /// Gets the name of the currently playing clip.
        /// </summary>
        /// <returns>The name of the current clip</returns>
        public string GetCurrentClipName()
        {
            if (musicPlaybackController != null)
                return musicPlaybackController.GetCurrentClipName();
            return string.Empty;
        }

        #endregion

        #region Beat Timing APIs

        /// <summary>
        /// Gets the current beat time for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat (default: 1)</param>
        /// <returns>The current beat time</returns>
        public float GetBeatTime(string clipName = null, int subdivisions = 1)
        {
            if (musicPlaybackController == null) return 0f;

            string targetClip = clipName ?? GetCurrentClipName();
            if (string.IsNullOrEmpty(targetClip)) return 0f;

            int currentSample = GetSampleTimeForClip(targetClip);
            return GetBeatTimeAtSample(targetClip, currentSample, subdivisions);
        }

        /// <summary>
        /// Gets the beat time in seconds for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat (default: 1)</param>
        /// <returns>The current beat time in seconds</returns>
        public float GetBeatTimeInSeconds(string clipName = null, int subdivisions = 1)
        {
            float beatTime = GetBeatTime(clipName, subdivisions);
            float bpm = GetBPMForClip(clipName);

            if (bpm <= 0f) return 0f;

            // Convert beats to seconds: (beats / BPM) * 60
            return (beatTime / bpm) * 60f;
        }

        /// <summary>
        /// Gets the percentage between beats (0.0 to 1.0) for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat (default: 1)</param>
        /// <returns>The percentage between beats in the range [0,1)</returns>
        public float GetBeatPercentage(string clipName = null, int subdivisions = 1)
        {
            float beatTime = GetBeatTime(clipName, subdivisions);
            return beatTime - Mathf.Floor(beatTime);
        }

        /// <summary>
        /// Gets the normalized beat time (0.0 to 1.0) within the current beat.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat (default: 1)</param>
        /// <returns>The normalized position within the current beat [0,1)</returns>
        public float GetBeatTimeNormalized(string clipName = null, int subdivisions = 1)
        {
            return GetBeatPercentage(clipName, subdivisions);
        }

        /// <summary>
        /// Gets the beat time delta for the current processing pass.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat (default: 1)</param>
        /// <returns>The beat time delta</returns>
        public float GetBeatTimeDelta(string clipName = null, int subdivisions = 1)
        {
            if (currentSampleTimeDelta <= 0) return 0f;

            string targetClip = clipName ?? GetCurrentClipName();
            if (string.IsNullOrEmpty(targetClip)) return 0f;

            float samplesPerBeat = GetSamplesPerBeat(targetClip);
            if (samplesPerBeat <= 0f) return 0f;

            return (currentSampleTimeDelta * subdivisions) / samplesPerBeat;
        }

        /// <summary>
        /// Gets the BPM (Beats Per Minute) for the specified clip at the current time.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <returns>The current BPM</returns>
        public float GetBPMForClip(string clipName = null)
        {
            string targetClip = clipName ?? GetCurrentClipName();
            if (string.IsNullOrEmpty(targetClip)) return 120f; // Default BPM

            int currentSample = GetSampleTimeForClip(targetClip);
            return GetBPMAtSampleTime(targetClip, currentSample);
        }

        /// <summary>
        /// Gets the BPM at a specific sample time for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <param name="sampleTime">The sample time</param>
        /// <returns>The BPM at the specified time</returns>
        public float GetBPMAtSampleTime(string clipName, int sampleTime)
        {
            // Find the loaded Cadance asset for this clip
            foreach (var cadance in loadedCadances)
            {
                if (cadance.SourceClipName == clipName)
                {
                    return cadance.GetBPMAtSampleTime(sampleTime);
                }
            }

            return 120f; // Default BPM
        }

        /// <summary>
        /// Gets the number of samples per beat for the specified clip at the current time.
        /// </summary>
        /// <param name="clipName">The name of the clip. If null, uses the current main clip.</param>
        /// <returns>The samples per beat</returns>
        public float GetSamplesPerBeat(string clipName = null)
        {
            string targetClip = clipName ?? GetCurrentClipName();
            if (string.IsNullOrEmpty(targetClip)) return 44100f; // Default for 44.1kHz at 60 BPM

            float bpm = GetBPMForClip(targetClip);
            int sampleRate = GetSampleRateForClip(targetClip);

            if (bpm <= 0f || sampleRate <= 0) return 44100f;

            // Calculate samples per beat: (sampleRate * 60) / BPM
            return (sampleRate * 60f) / bpm;
        }

        /// <summary>
        /// Gets the sample rate for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <returns>The sample rate</returns>
        public int GetSampleRateForClip(string clipName)
        {
            // Find the loaded Cadance asset for this clip
            foreach (var cadance in loadedCadances)
            {
                if (cadance.SourceClipName == clipName)
                {
                    return cadance.SampleRate;
                }
            }

            return 44100; // Default sample rate
        }

        /// <summary>
        /// Gets the beat time at a specific sample for the specified clip.
        /// </summary>
        /// <param name="clipName">The name of the clip</param>
        /// <param name="sampleTime">The sample time</param>
        /// <param name="subdivisions">The number of subdivisions of a standard beat</param>
        /// <returns>The beat time at the specified sample</returns>
        private float GetBeatTimeAtSample(string clipName, int sampleTime, int subdivisions = 1)
        {
            float samplesPerBeat = GetSamplesPerBeat(clipName);
            if (samplesPerBeat <= 0f) return 0f;

            // Calculate beat time with subdivisions
            return (sampleTime * subdivisions) / samplesPerBeat;
        }

        #endregion





        #region Tempo Conversion
        /// <summary>
        /// Converts Koreographer tempo sections to Cadance tempo sections.
        /// </summary>
        /// <param name="koreography">The source Koreography</param>
        /// <param name="cadanceAsset">The target CadanceAsset</param>
        private void ConvertTempoSections(SonicBloom.Koreo.Koreography koreography, CadanceAsset cadanceAsset)
        {
            try
            {
                // Get tempo sections from Koreography using reflection
                var tempoSectionsField = koreography.GetType().GetField("tempoSections",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (tempoSectionsField != null)
                {
                    var koreographyTempoSections = tempoSectionsField.GetValue(koreography);
                    if (koreographyTempoSections != null)
                    {
                        // Handle different possible types for tempo sections
                        if (koreographyTempoSections is System.Collections.IList tempoList)
                        {
                            foreach (var tempoSection in tempoList)
                            {
                                if (tempoSection != null)
                                {
                                    var cadanceTempoSection = ConvertTempoSection(tempoSection);
                                    if (cadanceTempoSection != null)
                                    {
                                        cadanceAsset.TempoSections.Add(cadanceTempoSection);
                                    }
                                }
                            }
                        }
                    }
                }

                // If no tempo sections found, create a default one
                if (cadanceAsset.TempoSections.Count == 0)
                {
                    cadanceAsset.TempoSections.Add(new TempoSection(0, 120f, 4, true));
                    Debug.Log($"[Cadance] No tempo sections found in Koreography '{koreography.name}', created default 120 BPM section");
                }
                else
                {
                    Debug.Log($"[Cadance] Converted {cadanceAsset.TempoSections.Count} tempo sections from Koreography '{koreography.name}'");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Cadance] Failed to convert tempo sections from Koreography '{koreography.name}': {ex.Message}");

                // Fallback: create default tempo section
                if (cadanceAsset.TempoSections.Count == 0)
                {
                    cadanceAsset.TempoSections.Add(new TempoSection(0, 120f, 4, true));
                }
            }
        }

        /// <summary>
        /// Converts a single Koreographer tempo section to a Cadance tempo section.
        /// </summary>
        /// <param name="koreographyTempoSection">The Koreographer tempo section</param>
        /// <returns>A Cadance TempoSection or null if conversion fails</returns>
        private TempoSection ConvertTempoSection(object koreographyTempoSection)
        {
            try
            {
                var type = koreographyTempoSection.GetType();

                // Extract properties using reflection
                var startSampleField = type.GetField("StartSample");
                var startSampleProperty = type.GetProperty("StartSample");
                var samplesPerBeatField = type.GetField("SamplesPerBeat");
                var samplesPerBeatProperty = type.GetProperty("SamplesPerBeat");
                var startNewMeasureField = type.GetField("StartNewMeasure");
                var startNewMeasureProperty = type.GetProperty("StartNewMeasure");

                if ((startSampleField != null || startSampleProperty != null) &&
                    (samplesPerBeatField != null || samplesPerBeatProperty != null))
                {
                    int startSample = 0;
                    float samplesPerBeat = 44100f; // Default
                    bool startNewMeasure = true;

                    // Get start sample
                    if (startSampleField != null)
                        startSample = (int)startSampleField.GetValue(koreographyTempoSection);
                    else if (startSampleProperty != null)
                        startSample = (int)startSampleProperty.GetValue(koreographyTempoSection);

                    // Get samples per beat
                    if (samplesPerBeatField != null)
                        samplesPerBeat = (float)samplesPerBeatField.GetValue(koreographyTempoSection);
                    else if (samplesPerBeatProperty != null)
                        samplesPerBeat = (float)samplesPerBeatProperty.GetValue(koreographyTempoSection);

                    // Get start new measure (optional)
                    if (startNewMeasureField != null)
                        startNewMeasure = (bool)startNewMeasureField.GetValue(koreographyTempoSection);
                    else if (startNewMeasureProperty != null)
                        startNewMeasure = (bool)startNewMeasureProperty.GetValue(koreographyTempoSection);

                    // Calculate BPM from samples per beat
                    float bpm = 120f; // Default
                    if (samplesPerBeat > 0)
                    {
                        int sampleRate = 44100; // Default, will be updated if source clip is available
                        if (loadedCadances.Count > 0 && loadedCadances[0].SourceClip != null)
                        {
                            sampleRate = loadedCadances[0].SourceClip.frequency;
                        }

                        // BPM = (sampleRate * 60) / samplesPerBeat
                        bpm = (sampleRate * 60f) / samplesPerBeat;
                    }

                    return new TempoSection(startSample, bpm, 4, startNewMeasure);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Cadance] Failed to convert individual tempo section: {ex.Message}");
            }

            return null;
        }

        #endregion

        #region Core Processing

        /// <summary>
        /// Processes Cadance events for the specified clip and time range.
        /// </summary>
        /// <param name="clip">The AudioClip to process</param>
        /// <param name="startTime">The start time in samples</param>
        /// <param name="endTime">The end time in samples</param>
        /// <param name="deltaSlice">The delta slice containing timing information</param>
        public void ProcessCadance(AudioClip clip, int startTime, int endTime, DeltaSlice deltaSlice)
        {
            if (clip == null) return;
            ProcessCadance(clip.name, startTime, endTime, deltaSlice);
        }

        /// <summary>
        /// Processes Cadance events for the specified clip name and time range.
        /// </summary>
        /// <param name="clipName">The name of the clip to process</param>
        /// <param name="startTime">The start time in samples</param>
        /// <param name="endTime">The end time in samples</param>
        /// <param name="deltaSlice">The delta slice containing timing information</param>
        public void ProcessCadance(string clipName, int startTime, int endTime, DeltaSlice deltaSlice)
        {
            if (string.IsNullOrEmpty(clipName)) return;

            // Update current processing state
            currentSampleTime = startTime;
            currentSampleTimeDelta = endTime - startTime;
            currentProcessingTime = Time.timeAsDouble;

            // Process delayed events first
            ProcessDelayedEvents();

            // Find and process events in loaded Cadances
            foreach (var cadance in loadedCadances)
            {
                if (cadance.SourceClipName == clipName)
                {
                    ProcessCadanceAsset(cadance, startTime, endTime, deltaSlice);
                }
            }
        }

        /// <summary>
        /// Processes events in a specific Cadance asset.
        /// </summary>
        /// <param name="cadance">The Cadance asset to process</param>
        /// <param name="startTime">The start time in samples</param>
        /// <param name="endTime">The end time in samples</param>
        /// <param name="deltaSlice">The delta slice</param>
        private void ProcessCadanceAsset(CadanceAsset cadance, int startTime, int endTime, DeltaSlice deltaSlice)
        {
            foreach (var track in cadance.Tracks)
            {
                if (track == null) continue;

                var eventsInRange = track.GetEventsInRange(startTime, endTime);

                foreach (var evt in eventsInRange)
                {
                    if (eventDelayInSeconds > 0f)
                    {
                        // Queue for delayed processing
                        var record = new TimingRecord(cadance.SourceClipName, evt.StartSample, evt.EndSample, deltaSlice,
                                                    currentProcessingTime + eventDelayInSeconds);
                        delayQueue.Enqueue(record);
                    }
                    else
                    {
                        // Process immediately
                        TriggerEvent(evt);
                    }
                }
            }
        }

        /// <summary>
        /// Processes delayed events that are ready to be triggered.
        /// </summary>
        private void ProcessDelayedEvents()
        {
            double currentTime = Time.timeAsDouble;

            while (delayQueue.Count > 0)
            {
                var record = delayQueue.Peek();
                if (record.scheduledTime <= currentTime)
                {
                    delayQueue.Dequeue();

                    // Find the event and trigger it
                    foreach (var cadance in loadedCadances)
                    {
                        if (cadance.SourceClipName == record.clipName)
                        {
                            foreach (var track in cadance.Tracks)
                            {
                                if (track == null) continue;

                                var evt = track.GetEventAtStartSample(record.startSample);
                                if (evt != null)
                                {
                                    TriggerEvent(evt);
                                    break;
                                }
                            }
                            break;
                        }
                    }
                }
                else
                {
                    break; // No more events ready
                }
            }
        }

        /// <summary>
        /// Clears the delay queue for the specified Cadance.
        /// </summary>
        /// <param name="cadance">The Cadance to clear delays for</param>
        /// <returns>True if any delays were cleared</returns>
        public bool FlushDelayQueue(CadanceAsset cadance)
        {
            if (cadance == null) return false;

            bool clearedAny = false;
            var tempQueue = new Queue<TimingRecord>();

            while (delayQueue.Count > 0)
            {
                var record = delayQueue.Dequeue();
                if (record.clipName != cadance.SourceClipName)
                {
                    tempQueue.Enqueue(record);
                }
                else
                {
                    clearedAny = true;
                }
            }

            delayQueue = tempQueue;
            return clearedAny;
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);

                // Subscribe to scene change events for proper cleanup
                UnityEngine.SceneManagement.SceneManager.sceneUnloaded += OnSceneUnloaded;
                UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoaded;

                Debug.Log("[Cadance] Initialized with scene change listeners");
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                // Unsubscribe from scene events
                UnityEngine.SceneManagement.SceneManager.sceneUnloaded -= OnSceneUnloaded;
                UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoaded;

                // Cleanup registrations
                CleanupAllRegistrations();

                _instance = null;
                Debug.Log("[Cadance] Destroyed and cleaned up");
            }
        }

        /// <summary>
        /// Called when a scene is unloaded. Cleans up scene-specific registrations.
        /// </summary>
        /// <param name="scene">The unloaded scene</param>
        private void OnSceneUnloaded(UnityEngine.SceneManagement.Scene scene)
        {
            Debug.Log($"[Cadance] Scene '{scene.name}' unloaded, performing cleanup");
            
            // Clean up event registrations for destroyed objects
            CleanupDestroyedObjectRegistrations();
            
            // Unload any Cadance assets that were scene-specific
            CleanupSceneSpecificAssets(scene);
        }

        /// <summary>
        /// Called when a scene is loaded. Can be used for scene-specific initialization.
        /// </summary>
        /// <param name="scene">The loaded scene</param>
        /// <param name="mode">The load mode</param>
        private void OnSceneLoaded(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
        {
            Debug.Log($"[Cadance] Scene '{scene.name}' loaded");
            // Scene-specific initialization can be added here if needed
        }

        /// <summary>
        /// Cleans up all registrations and loaded assets.
        /// </summary>
        private void CleanupAllRegistrations()
        {
            eventObjects.Clear();
            loadedCadances.Clear();
            delayQueue.Clear();
        }

        /// <summary>
        /// Cleans up event registrations for objects that have been destroyed.
        /// </summary>
        private void CleanupDestroyedObjectRegistrations()
        {
            foreach (var eventObj in eventObjects.Values)
            {
                eventObj.CleanupDestroyedObjects();
            }
        }

        /// <summary>
        /// Cleans up Cadance assets that were specific to the unloaded scene.
        /// </summary>
        /// <param name="scene">The unloaded scene</param>
        private void CleanupSceneSpecificAssets(UnityEngine.SceneManagement.Scene scene)
        {
            int removedCount = 0;
            
            // Remove any loaded Cadance assets that were scene-specific
            for (int i = loadedCadances.Count - 1; i >= 0; i--)
            {
                var cadance = loadedCadances[i];
                if (cadance == null)
                {
                    loadedCadances.RemoveAt(i);
                    removedCount++;
                    continue;
                }

                // Check if this asset should be unloaded with the scene
                if (cadance.IsSceneSpecific && cadance.LoadedFromScene == scene.name)
                {
                    Debug.Log($"[Cadance] Unloading scene-specific asset '{cadance.name}' from scene '{scene.name}'");
                    loadedCadances.RemoveAt(i);
                    removedCount++;
                }
            }

            if (removedCount > 0)
            {
                Debug.Log($"[Cadance] Cleaned up {removedCount} scene-specific assets from scene '{scene.name}'");
            }
        }

        #endregion

        #region Validation and Debug Methods

        /// <summary>
        /// Validates the current state of the Cadance system and reports any issues.
        /// </summary>
        [ContextMenu("Validate Cadance State")]
        public void ValidateSystemState()
        {
            Debug.Log("=== Cadance System Validation ===");
            Debug.Log($"Loaded Cadance Assets: {loadedCadances.Count}");
            Debug.Log($"Registered Event Objects: {eventObjects.Count}");
            Debug.Log($"Delay Queue Size: {delayQueue.Count}");

            // Check for null assets
            int nullAssets = 0;
            foreach (var asset in loadedCadances)
            {
                if (asset == null) nullAssets++;
            }
            if (nullAssets > 0)
            {
                Debug.LogWarning($"Found {nullAssets} null Cadance assets - cleanup needed");
            }

            // Check event objects for destroyed callbacks
            int totalCallbacks = 0;
            foreach (var eventObj in eventObjects.Values)
            {
                totalCallbacks += eventObj.GetCallbackCount();
            }
            Debug.Log($"Total Event Callbacks: {totalCallbacks}");

            Debug.Log("=== Validation Complete ===");
        }

        /// <summary>
        /// Forces cleanup of all destroyed object references.
        /// </summary>
        [ContextMenu("Force Cleanup Destroyed Objects")]
        public void ForceCleanupDestroyedObjects()
        {
            CleanupDestroyedObjectRegistrations();
            Debug.Log("[Cadance] Forced cleanup of destroyed object references");
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the current sample time and delta for timing calculations.
        /// </summary>
        /// <param name="sampleTime">Output: current sample time</param>
        /// <param name="sampleTimeDelta">Output: current sample time delta</param>
        public void GetCurrentSampleTimeAndDelta(out int sampleTime, out int sampleTimeDelta)
        {
            sampleTime = currentSampleTime;
            sampleTimeDelta = currentSampleTimeDelta;
        }

        /// <summary>
        /// Gets the current processing time.
        /// </summary>
        /// <returns>The current processing time</returns>
        public double GetCurrentProcessingTime()
        {
            return currentProcessingTime;
        }

        /// <summary>
        /// Converts a Koreography asset to a CadanceAsset for compatibility.
        /// </summary>
        /// <param name="koreography">The Koreography to convert</param>
        /// <returns>A new CadanceAsset with equivalent data</returns>
        public CadanceAsset ConvertKoreographyToCadance(SonicBloom.Koreo.Koreography koreography)
        {
            try
            {
                // Create a new CadanceAsset
                var cadanceAsset = ScriptableObject.CreateInstance<CadanceAsset>();
                cadanceAsset.name = $"{koreography.name}_CadanceAsset";

                // Copy basic properties
                cadanceAsset.SourceClip = koreography.SourceClip;
                cadanceAsset.SourceClipName = koreography.SourceClipName;

                if (koreography.SourceClip != null)
                {
                    cadanceAsset.SampleRate = koreography.SourceClip.frequency;
                }

                // Convert tempo sections
                ConvertTempoSections(koreography, cadanceAsset);

                // Convert tracks
                string[] eventIDs = koreography.GetEventIDs();
                foreach (string eventID in eventIDs)
                {
                    var koreographyTrack = koreography.GetTrackByID(eventID);
                    if (koreographyTrack != null)
                    {
                        var cadanceTrack = ConvertKoreographyTrackToCadanceTrack(koreographyTrack, eventID);
                        if (cadanceTrack != null)
                        {
                            cadanceAsset.AddTrack(cadanceTrack);
                        }
                    }
                }

                return cadanceAsset;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance] Failed to convert Koreography '{koreography.name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Converts a KoreographyTrack to a CadanceTrack.
        /// </summary>
        /// <param name="koreographyTrack">The KoreographyTrack to convert</param>
        /// <param name="eventID">The event ID for the track</param>
        /// <returns>A new CadanceTrack with equivalent data</returns>
        public CadanceTrack ConvertKoreographyTrackToCadanceTrack(SonicBloom.Koreo.KoreographyTrackBase koreographyTrack, string eventID)
        {
            try
            {
                // Use the proper CreateTrack method which sets EventID and name correctly
                var cadanceTrack = CadanceTrack.CreateTrack(eventID);

                // Ensure the track has a proper name for display
                cadanceTrack.name = $"Track_{eventID}";

                // Get all events from the koreography track
                var events = koreographyTrack.GetAllEvents();
                int eventCount = 0;

                foreach (var koreographyEvent in events)
                {
                    // Convert KoreographyEvent to CadanceEvent
                    var cadanceEvent = ConvertKoreographyEventToCadanceEvent(koreographyEvent, eventID);
                    if (cadanceEvent != null)
                    {
                        cadanceTrack.AddEvent(cadanceEvent);
                        eventCount++;
                    }
                }

                Debug.Log($"[Cadance] Converted track '{eventID}' with {eventCount} events");
                return cadanceTrack;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance] Failed to convert KoreographyTrack '{eventID}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Converts a KoreographyEvent to a CadanceEvent.
        /// </summary>
        /// <param name="koreographyEvent">The KoreographyEvent to convert</param>
        /// <param name="eventID">The event ID</param>
        /// <returns>A new CadanceEvent with equivalent data</returns>
        private CadanceEvent ConvertKoreographyEventToCadanceEvent(SonicBloom.Koreo.KoreographyEvent koreographyEvent, string eventID)
        {
            try
            {
                int startSample = koreographyEvent.StartSample;
                int endSample = koreographyEvent.EndSample;

                // Convert payload if present
                IPayload payload = null;
                if (koreographyEvent.Payload != null)
                {
                    payload = ConvertKoreographyPayloadToCadancePayload(koreographyEvent);
                }

                return new CadanceEvent(eventID, startSample, endSample, payload);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance] Failed to convert KoreographyEvent: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Converts Koreography payloads to Cadance payloads.
        /// </summary>
        /// <param name="koreographyEvent">The KoreographyEvent with payload</param>
        /// <returns>A compatible IPayload or null</returns>
        private IPayload ConvertKoreographyPayloadToCadancePayload(SonicBloom.Koreo.KoreographyEvent koreographyEvent)
        {
            try
            {
                // Check if the event has a payload
                if (koreographyEvent.Payload == null)
                    return null;

                // Get the payload object and convert based on type
                var payload = koreographyEvent.Payload;
                string payloadTypeName = payload.GetType().Name;

                switch (payloadTypeName)
                {
                    case "IntPayload":
                        var intField = payload.GetType().GetField("IntVal");
                        if (intField != null)
                        {
                            int intValue = (int)intField.GetValue(payload);
                            return new IntPayload(intValue);
                        }
                        break;

                    case "FloatPayload":
                        var floatField = payload.GetType().GetField("FloatVal");
                        if (floatField != null)
                        {
                            float floatValue = (float)floatField.GetValue(payload);
                            return new FloatPayload(floatValue);
                        }
                        break;

                    case "BoolPayload":
                        // Use reflection to get the value since extension methods might not be available
                        var boolField = payload.GetType().GetField("BoolVal");
                        if (boolField != null)
                        {
                            bool boolValue = (bool)boolField.GetValue(payload);
                            return new BoolPayload(boolValue);
                        }
                        break;

                    case "ColorPayload":
                        var colorField = payload.GetType().GetField("ColorVal");
                        if (colorField != null)
                        {
                            Color colorValue = (Color)colorField.GetValue(payload);
                            return new ColorPayload(colorValue);
                        }
                        break;

                    case "GradientPayload":
                        var gradientField = payload.GetType().GetField("GradientVal");
                        if (gradientField != null)
                        {
                            Gradient gradientValue = (Gradient)gradientField.GetValue(payload);
                            return new GradientPayload(gradientValue);
                        }
                        break;

                    case "CurvePayload":
                        var curveField = payload.GetType().GetField("CurveVal");
                        if (curveField != null)
                        {
                            AnimationCurve curveValue = (AnimationCurve)curveField.GetValue(payload);
                            return new CurvePayload(curveValue);
                        }
                        break;

                    case "TextPayload":
                        var textField = payload.GetType().GetField("TextVal");
                        if (textField != null)
                        {
                            string textValue = (string)textField.GetValue(payload);
                            return new TextPayload(textValue);
                        }
                        break;

                    case "SpectrumPayload":
                        var spectrumDataField = payload.GetType().GetField("SpectrumData");
                        var spectrumInfoField = payload.GetType().GetField("SpectrumDataInfo");
                        if (spectrumDataField != null)
                        {
                            float[] spectrumData = (float[])spectrumDataField.GetValue(payload);

                            // Create SpectrumInfo from Koreographer's data
                            SpectrumInfo spectrumInfo = default;
                            if (spectrumInfoField != null)
                            {
                                var koreographySpectrumInfo = spectrumInfoField.GetValue(payload);
                                if (koreographySpectrumInfo != null)
                                {
                                    // Extract sample rate and FFT size from Koreographer's SpectrumInfo
                                    var sampleRateField = koreographySpectrumInfo.GetType().GetField("sampleRate");
                                    var fftSizeField = koreographySpectrumInfo.GetType().GetField("fftSize");

                                    int sampleRate = sampleRateField != null ? (int)sampleRateField.GetValue(koreographySpectrumInfo) : 44100;
                                    int fftSize = fftSizeField != null ? (int)fftSizeField.GetValue(koreographySpectrumInfo) : 1024;

                                    spectrumInfo = new SpectrumInfo(sampleRate, fftSize);
                                }
                            }

                            return new SpectrumPayload(spectrumData, spectrumInfo);
                        }
                        break;

                    case "RMSPayload":
                        // Extract RMS data from Koreographer's RMSPayload
                        var rmsValueField = payload.GetType().GetField("RMSValue") ?? payload.GetType().GetField("rmsValue");
                        if (rmsValueField != null)
                        {
                            float rmsValue = (float)rmsValueField.GetValue(payload);

                            // Try to get additional values if available
                            float peakValue = 0f;
                            float averageValue = 0f;

                            var peakField = payload.GetType().GetField("PeakValue") ?? payload.GetType().GetField("peakValue");
                            if (peakField != null)
                            {
                                peakValue = (float)peakField.GetValue(payload);
                            }

                            var avgField = payload.GetType().GetField("AverageValue") ?? payload.GetType().GetField("averageValue");
                            if (avgField != null)
                            {
                                averageValue = (float)avgField.GetValue(payload);
                            }

                            // Create RMSInfo for the payload
                            var rmsInfo = new RMSInfo(44100, 1024); // Default values
                            return new RMSPayload(rmsValue, peakValue, rmsInfo);
                        }
                        break;

                    case "AssetPayload":
                        var assetField = payload.GetType().GetField("AssetVal") ?? payload.GetType().GetField("assetValue");
                        if (assetField != null)
                        {
                            UnityEngine.Object assetValue = (UnityEngine.Object)assetField.GetValue(payload);
                            return new AssetPayload(assetValue);
                        }
                        break;
                }

                // Fallback: try to get string representation
                return new TextPayload(payload.ToString());
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Cadance] Failed to convert Koreography payload: {ex.Message}");
                return new TextPayload("Conversion Failed");
            }
        }

        #endregion
    }
}
