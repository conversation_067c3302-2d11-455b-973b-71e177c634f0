#if URP_INSTALLED
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using Stylo.Flux.Universal;
using System.Collections.Generic;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Tool to validate that all Flux parameters are properly saving their override states and values
    /// </summary>
    public class FluxParameterValidationTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<Volume> fluxVolumes = new List<Volume>();
        private bool autoRefresh = true;

        [MenuItem("Tools/Flux/Parameter Validation Tool", false, 200)]
        public static void ShowWindow()
        {
            var window = GetWindow<FluxParameterValidationTool>("Flux Parameter Validation");
            window.minSize = new Vector2(500, 400);
            window.Show();
        }

        private void OnEnable()
        {
            RefreshVolumeList();
            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
        }

        private void OnDisable()
        {
            EditorApplication.playModeStateChanged -= OnPlayModeStateChanged;
        }

        private void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            if (autoRefresh)
            {
                RefreshVolumeList();
                Repaint();
            }
        }

        private void RefreshVolumeList()
        {
            fluxVolumes.Clear();
            var allVolumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in allVolumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out _))
                {
                    fluxVolumes.Add(volume);
                }
            }
        }

        private void OnGUI()
        {
            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.LabelField("Flux Parameter Validation Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Controls
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Refresh Volume List"))
            {
                RefreshVolumeList();
            }
            autoRefresh = EditorGUILayout.Toggle("Auto Refresh", autoRefresh);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Status
            EditorGUILayout.LabelField($"Found {fluxVolumes.Count} Volume(s) with Flux Effects");
            EditorGUILayout.LabelField($"Play Mode: {(Application.isPlaying ? "Active" : "Inactive")}");

            EditorGUILayout.Space();

            if (fluxVolumes.Count == 0)
            {
                EditorGUILayout.HelpBox("No Volumes with Flux Effects found in the current scene.", MessageType.Info);
                EditorGUILayout.EndVertical();
                return;
            }

            // Instructions
            if (!Application.isPlaying)
            {
                EditorGUILayout.HelpBox(
                    "To test parameter saving:\n" +
                    "1. Enter Play Mode\n" +
                    "2. Modify Flux parameters (enable overrides, change values)\n" +
                    "3. Exit Play Mode\n" +
                    "4. Check if parameters retained their override states and values",
                    MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox(
                    "Currently in Play Mode. Modify Flux parameters and then exit Play Mode to test saving.",
                    MessageType.Warning);
            }

            EditorGUILayout.Space();

            // Volume list
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var volume in fluxVolumes)
            {
                if (volume == null || volume.profile == null) continue;

                DrawVolumeInfo(volume);
                EditorGUILayout.Space();
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private void DrawVolumeInfo(Volume volume)
        {
            EditorGUILayout.BeginVertical("box");

            // Volume header
            EditorGUILayout.LabelField($"Volume: {volume.name}", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Profile: {volume.profile.name}", EditorStyles.miniLabel);

            if (volume.profile.TryGet<FluxEffect>(out var fluxEffect))
            {
                EditorGUILayout.Space(5);

                // Parameter summary
                var enabledCount = CountEnabledParameters(fluxEffect);
                var totalCount = GetTotalParameterCount();

                EditorGUILayout.LabelField($"Enabled Parameters: {enabledCount}/{totalCount}");

                // Quick parameter overview
                EditorGUILayout.BeginVertical();

                DrawParameterStatus("Effect Intensity", fluxEffect.EffectIntensity);
                DrawParameterStatus("Color Crunch", fluxEffect.ColorCrunch);
                DrawParameterStatus("Reproject Base Noise", fluxEffect.ReprojectBaseNoise);
                DrawParameterStatus("Reproject Length Influence", fluxEffect.ReprojectLengthInfluence);

                // Consolidated Motion Processing
                DrawParameterStatus("Motion Amplification", fluxEffect.MotionAmplification);
                DrawParameterStatus("Motion Threshold", fluxEffect.MotionThreshold);
                DrawParameterStatus("Camera Object Motion Balance", fluxEffect.CameraObjectMotionBalance);
                DrawParameterStatus("Motion Smoothing", fluxEffect.MotionSmoothing);

                // Pixel Flow & Trailing parameters
                DrawParameterStatus("Trail Intensity", fluxEffect.TrailIntensity);
                DrawParameterStatus("Trail Smoothness", fluxEffect.TrailSmoothness);
                DrawParameterStatus("Trail Persistence", fluxEffect.TrailPersistence);
                DrawParameterStatus("Flow Spread", fluxEffect.FlowSpread);

                // Advanced Datamoshing parameters
                DrawParameterStatus("Keyframe Reset Rate", fluxEffect.KeyframeResetRate);
                DrawParameterStatus("Motion Vector Corruption", fluxEffect.MotionVectorCorruption);
                DrawParameterStatus("Error Accumulation", fluxEffect.ErrorAccumulation);
                DrawParameterStatus("DCT Corruption", fluxEffect.DCTCorruption);

                // Enhanced Corruption parameters
                DrawParameterStatus("Chroma Corruption", fluxEffect.ChromaCorruption);
                DrawParameterStatus("Glitch Transition", fluxEffect.GlitchTransition);
                DrawParameterStatus("Feedback Intensity", fluxEffect.FeedbackIntensity);
                DrawParameterStatus("Multi-Scale Corruption", fluxEffect.MultiScaleCorruption);

                EditorGUILayout.EndVertical();

                // Action buttons
                EditorGUILayout.Space(5);
                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button("Select Volume"))
                {
                    Selection.activeObject = volume;
                }

                if (GUILayout.Button("Select Profile"))
                {
                    Selection.activeObject = volume.profile;
                }

                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawParameterStatus(string name, VolumeParameter parameter)
        {
            EditorGUILayout.BeginHorizontal();

            // Parameter name
            EditorGUILayout.LabelField(name, GUILayout.Width(200));

            // Override status
            var overrideStyle = parameter.overrideState ? EditorStyles.boldLabel : EditorStyles.label;
            var overrideColor = parameter.overrideState ? Color.green : Color.gray;

            var originalColor = GUI.color;
            GUI.color = overrideColor;
            EditorGUILayout.LabelField(parameter.overrideState ? "ON" : "OFF", overrideStyle, GUILayout.Width(30));
            GUI.color = originalColor;

            // Value
            string valueText = FormatParameterValue(parameter);
            EditorGUILayout.LabelField(valueText, GUILayout.Width(100));

            EditorGUILayout.EndHorizontal();
        }

        private string FormatParameterValue(VolumeParameter parameter)
        {
            switch (parameter)
            {
                case ClampedFloatParameter floatParam:
                    return floatParam.value.ToString("F3");
                case ClampedIntParameter intParam:
                    return intParam.value.ToString();
                case BoolParameter boolParam:
                    return boolParam.value ? "True" : "False";
                case Texture2DParameter texParam:
                    return texParam.value != null ? texParam.value.name : "null";
                default:
                    return parameter.ToString();
            }
        }

        private int CountEnabledParameters(FluxEffect fluxEffect)
        {
            int count = 0;

            if (fluxEffect.EffectIntensity.overrideState) count++;
            if (fluxEffect.OnlyStenciled.overrideState) count++;
            if (fluxEffect.ColorCrunch.overrideState) count++;
            if (fluxEffect.Downscaling.overrideState) count++;
            if (fluxEffect.BlockSize.overrideState) count++;
            if (fluxEffect.Oversharpening.overrideState) count++;
            if (fluxEffect.DontCrunchSkybox.overrideState) count++;
            if (fluxEffect.ReprojectBaseNoise.overrideState) count++;
            if (fluxEffect.ReprojectBaseRerollSpeed.overrideState) count++;
            if (fluxEffect.ReprojectLengthInfluence.overrideState) count++;

            // Consolidated Motion Processing
            if (fluxEffect.MotionAmplification.overrideState) count++;
            if (fluxEffect.MotionThreshold.overrideState) count++;
            if (fluxEffect.CameraObjectMotionBalance.overrideState) count++;
            if (fluxEffect.MotionSmoothing.overrideState) count++;

            // Pixel Flow & Trailing parameters
            if (fluxEffect.TrailIntensity.overrideState) count++;
            if (fluxEffect.TrailSmoothness.overrideState) count++;
            if (fluxEffect.TrailPersistence.overrideState) count++;
            if (fluxEffect.FlowSpread.overrideState) count++;

            // Advanced Datamoshing
            if (fluxEffect.KeyframeResetRate.overrideState) count++;
            if (fluxEffect.MotionVectorCorruption.overrideState) count++;
            if (fluxEffect.ErrorAccumulation.overrideState) count++;
            if (fluxEffect.DCTCorruption.overrideState) count++;

            // Enhanced Corruption
            if (fluxEffect.ChromaCorruption.overrideState) count++;
            if (fluxEffect.GlitchTransition.overrideState) count++;
            if (fluxEffect.FeedbackIntensity.overrideState) count++;
            if (fluxEffect.MultiScaleCorruption.overrideState) count++;

            // Debug
            if (fluxEffect.VisualizeMotionVectors.overrideState) count++;

            return count;
        }

        private int GetTotalParameterCount()
        {
            return 24; // Total number of Flux parameters after corruption mask removal
        }
    }
}
#endif
