#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Stylo.Flux;
using System.Linq;
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux.Editor
{
    [CustomEditor(typeof(FluxPreset))]
    public class FluxPresetEditor : UnityEditor.Editor
    {
        // Foldout states for parameter groups
        private bool showCoreControls = true;
        private bool showCompressionEngine = true;
        private bool showMotionSystem = true;
        private bool showTrailSystem = true;
        private bool showArtifactGeneration = true;
        private bool showJPEGSimulation = true;
        private bool showArtisticEffects = true;
        private bool showBrightnessControl = true;
        private bool showDebugUtility = false;

        // Parameter state tracking
        private bool showParameterStates = false;
        private bool showDependencyWarnings = true;

        public override void OnInspectorGUI()
        {
            FluxPreset preset = (FluxPreset)target;

            EditorGUILayout.Space(5);

            // Header with preset info
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Flux Preset", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Category: {preset.category}");
            if (!string.IsNullOrEmpty(preset.description))
            {
                EditorGUILayout.LabelField("Description:", EditorStyles.miniLabel);
                EditorGUILayout.LabelField(preset.description, EditorStyles.wordWrappedMiniLabel);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Apply to Scene button
            if (GUILayout.Button("Apply to Active Flux Effect in Scene", GUILayout.Height(30)))
            {
                ApplyPresetToScene(preset);
            }

            // Validation and Auto-fix buttons
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Validate Preset", GUILayout.Height(25)))
            {
                ValidatePresetWithDependencies(preset);
            }
            if (GUILayout.Button("Auto-Fix Issues", GUILayout.Height(25)))
            {
                AutoFixPresetWithDependencies(preset);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            // Parameter State Control
            showParameterStates = EditorGUILayout.BeginFoldoutHeaderGroup(showParameterStates, "🔧 Parameter State Control");
            if (showParameterStates)
            {
                EditorGUILayout.BeginVertical("box");
                EditorGUI.BeginChangeCheck();
                bool newUseParameterStates = EditorGUILayout.Toggle("Use Parameter States", preset.useParameterStates);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(preset, "Toggle Parameter States");
                    preset.useParameterStates = newUseParameterStates;
                    EditorUtility.SetDirty(preset);
                }

                if (preset.useParameterStates)
                {
                    EditorGUILayout.HelpBox("Parameter states control which parameters are applied when using this preset. Disabled parameters will not modify their target values.", MessageType.Info);

                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("Enable All", GUILayout.Height(20)))
                    {
                        Undo.RecordObject(preset, "Enable All Parameters");
                        EnableAllParameters(preset);
                        EditorUtility.SetDirty(preset);
                    }

                    if (GUILayout.Button("Disable All", GUILayout.Height(20)))
                    {
                        Undo.RecordObject(preset, "Disable All Parameters");
                        DisableAllParameters(preset);
                        EditorUtility.SetDirty(preset);
                    }

                    if (GUILayout.Button("Reset to Defaults", GUILayout.Height(20)))
                    {
                        Undo.RecordObject(preset, "Reset Parameter States");
                        preset.InitializeParameterStates();
                        EditorUtility.SetDirty(preset);
                    }
                    EditorGUILayout.EndHorizontal();
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            EditorGUILayout.Space(5);

            // Dependency Warnings
            showDependencyWarnings = EditorGUILayout.BeginFoldoutHeaderGroup(showDependencyWarnings, "⚠️ Dependency Warnings");
            if (showDependencyWarnings)
            {
                EditorGUILayout.BeginVertical("box");
                var validationResults = ValidateParameterDependencies(preset);

                if (validationResults.errors.Count > 0)
                {
                    EditorGUILayout.HelpBox("Critical Issues Found:\n" + string.Join("\n", validationResults.errors.ToArray()), MessageType.Error);
                }

                if (validationResults.warnings.Count > 0)
                {
                    EditorGUILayout.HelpBox("Warnings:\n" + string.Join("\n", validationResults.warnings.ToArray()), MessageType.Warning);
                }

                if (validationResults.suggestions.Count > 0)
                {
                    EditorGUILayout.LabelField("Suggestions:", EditorStyles.boldLabel);
                    foreach (var suggestion in validationResults.suggestions)
                    {
                        EditorGUILayout.LabelField("• " + suggestion, EditorStyles.wordWrappedLabel);
                    }
                }

                if (!validationResults.hasIssues)
                {
                    EditorGUILayout.HelpBox("No dependency issues found.", MessageType.Info);
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            EditorGUILayout.Space(10);

            // Preset Information
            EditorGUILayout.LabelField("Preset Information", EditorStyles.boldLabel);
            preset.presetName = EditorGUILayout.TextField("Preset Name", preset.presetName);
            preset.category = EditorGUILayout.TextField("Category", preset.category);
            preset.description = EditorGUILayout.TextArea(preset.description, GUILayout.Height(60));

            EditorGUILayout.Space(10);

            // Core Controls
            showCoreControls = EditorGUILayout.BeginFoldoutHeaderGroup(showCoreControls, "🔧 Core Controls");
            if (showCoreControls)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "EffectIntensity", () =>
                {
                    preset.effectIntensity = EditorGUILayout.Slider("Effect Intensity", preset.effectIntensity, 0f, 1f);
                });
                DrawParameterWithState(preset, "OnlyStenciled", () =>
                {
                    preset.onlyStenciled = EditorGUILayout.Toggle("Only Stenciled", preset.onlyStenciled);
                });
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Compression Engine
            showCompressionEngine = EditorGUILayout.BeginFoldoutHeaderGroup(showCompressionEngine, "🎯 Compression Engine");
            if (showCompressionEngine)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "ColorCrunch", () =>
                {
                    preset.colorCrunch = EditorGUILayout.Slider("Color Crunch", preset.colorCrunch, 0f, 1f);
                });
                DrawParameterWithState(preset, "Downscaling", () =>
                {
                    preset.downscaling = EditorGUILayout.IntSlider("Downscaling", preset.downscaling, 1, 10);
                });
                DrawParameterWithState(preset, "BlockSize", () =>
                {
                    preset.blockSize = (FluxPreset.FluxBlockSize)EditorGUILayout.EnumPopup("Block Size", preset.blockSize);
                });
                DrawParameterWithState(preset, "DontCrunchSkybox", () =>
                {
                    preset.dontCrunchSkybox = EditorGUILayout.Toggle("Don't Crunch Skybox", preset.dontCrunchSkybox);
                });

                // Show performance warning for dangerous combinations
                if ((preset.blockSize == FluxPreset.FluxBlockSize._32x32 && preset.downscaling < 5) ||
                    (preset.blockSize == FluxPreset.FluxBlockSize._16x16 && preset.downscaling < 3))
                {
                    EditorGUILayout.HelpBox("⚠️ Performance Warning: Large block sizes with low downscaling may cause performance issues!", MessageType.Warning);
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Motion System
            showMotionSystem = EditorGUILayout.BeginFoldoutHeaderGroup(showMotionSystem, "🌊 Motion System");
            if (showMotionSystem)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "MotionAmplification", () =>
                {
                    preset.motionAmplification = EditorGUILayout.Slider("Motion Amplification", preset.motionAmplification, 0f, 10f);
                });
                DrawParameterWithState(preset, "MotionThreshold", () =>
                {
                    preset.motionThreshold = EditorGUILayout.Slider("Motion Threshold", preset.motionThreshold, 0f, 0.1f);
                });
                DrawParameterWithState(preset, "CameraObjectMotionBalance", () =>
                {
                    preset.cameraObjectMotionBalance = EditorGUILayout.Slider("Camera vs Object Balance", preset.cameraObjectMotionBalance, 0f, 1f);
                });
                DrawParameterWithState(preset, "MotionSmoothing", () =>
                {
                    preset.motionSmoothing = EditorGUILayout.Slider("Motion Smoothing", preset.motionSmoothing, 0f, 1f);
                });
                DrawParameterWithState(preset, "ReprojectBaseNoise", () =>
                {
                    preset.reprojectBaseNoise = EditorGUILayout.Slider("Base Noise", preset.reprojectBaseNoise, 0f, 1f);
                });
                DrawParameterWithState(preset, "ReprojectBaseRerollSpeed", () =>
                {
                    preset.reprojectBaseRerollSpeed = EditorGUILayout.Slider("Base Reroll Speed", preset.reprojectBaseRerollSpeed, 0f, 20f);
                });
                DrawParameterWithState(preset, "ReprojectLengthInfluence", () =>
                {
                    preset.reprojectLengthInfluence = EditorGUILayout.Slider("Length Influence", preset.reprojectLengthInfluence, 0f, 5f);
                });

                // Show dependency warning
                if (preset.motionAmplification > 0f && preset.reprojectBaseNoise == 0f && preset.reprojectLengthInfluence == 0f)
                {
                    EditorGUILayout.HelpBox("⚠️ Motion effects require reprojection! Enable Base Noise or Length Influence.", MessageType.Warning);
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Trail System
            showTrailSystem = EditorGUILayout.BeginFoldoutHeaderGroup(showTrailSystem, "✨ Trail System");
            if (showTrailSystem)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "TrailIntensity", () =>
                {
                    preset.trailIntensity = EditorGUILayout.Slider("Trail Intensity", preset.trailIntensity, 0f, 5f);
                });
                DrawParameterWithState(preset, "TrailSmoothness", () =>
                {
                    preset.trailSmoothness = EditorGUILayout.Slider("Trail Smoothness", preset.trailSmoothness, 0f, 1f);
                });
                DrawParameterWithState(preset, "TrailPersistence", () =>
                {
                    preset.trailPersistence = EditorGUILayout.Slider("Trail Persistence", preset.trailPersistence, 0f, 1f);
                });
                DrawParameterWithState(preset, "FlowSpread", () =>
                {
                    preset.flowSpread = EditorGUILayout.Slider("Flow Spread", preset.flowSpread, 0f, 5f);
                });

                // Show dependency warning
                if (preset.trailIntensity > 0f && preset.motionAmplification == 0f)
                {
                    EditorGUILayout.HelpBox("⚠️ Trail effects require Motion Amplification to be enabled!", MessageType.Warning);
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Artifact Generation
            showArtifactGeneration = EditorGUILayout.BeginFoldoutHeaderGroup(showArtifactGeneration, "🎨 Artifact Generation");
            if (showArtifactGeneration)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "KeyframeResetRate", () =>
                {
                    preset.keyframeResetRate = EditorGUILayout.Slider("Keyframe Reset Rate", preset.keyframeResetRate, 0f, 1f);
                });
                DrawParameterWithState(preset, "MotionVectorCorruption", () =>
                {
                    preset.motionVectorCorruption = EditorGUILayout.Slider("Motion Vector Corruption", preset.motionVectorCorruption, 0f, 2f);
                });
                DrawParameterWithState(preset, "ErrorAccumulation", () =>
                {
                    preset.errorAccumulation = EditorGUILayout.Slider("Error Accumulation", preset.errorAccumulation, 0f, 1f);
                });
                DrawParameterWithState(preset, "DCTCorruption", () =>
                {
                    preset.dctCorruption = EditorGUILayout.Slider("DCT Corruption", preset.dctCorruption, 0f, 1f);
                });
                DrawParameterWithState(preset, "ChromaCorruption", () =>
                {
                    preset.chromaCorruption = EditorGUILayout.Slider("Chroma Corruption", preset.chromaCorruption, 0f, 1f);
                });
                DrawParameterWithState(preset, "MultiScaleCorruption", () =>
                {
                    preset.multiScaleCorruption = EditorGUILayout.Slider("Multi-Scale Corruption", preset.multiScaleCorruption, 0f, 1f);
                });
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // JPEG Simulation
            showJPEGSimulation = EditorGUILayout.BeginFoldoutHeaderGroup(showJPEGSimulation, "📷 JPEG Simulation");
            if (showJPEGSimulation)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "JPEGQuality", () =>
                {
                    preset.jpegQuality = EditorGUILayout.Slider("JPEG Quality", preset.jpegQuality, 1f, 100f);
                });
                DrawParameterWithState(preset, "LuminanceQuantization", () =>
                {
                    preset.luminanceQuantization = EditorGUILayout.Slider("Luminance Quantization", preset.luminanceQuantization, 0f, 2f);
                });
                DrawParameterWithState(preset, "ChrominanceQuantization", () =>
                {
                    preset.chrominanceQuantization = EditorGUILayout.Slider("Chrominance Quantization", preset.chrominanceQuantization, 0f, 2f);
                });
                DrawParameterWithState(preset, "ChromaSubsampling", () =>
                {
                    preset.chromaSubsampling = EditorGUILayout.Toggle("Chroma Subsampling", preset.chromaSubsampling);
                });
                DrawParameterWithState(preset, "RingingArtifacts", () =>
                {
                    preset.ringingArtifacts = EditorGUILayout.Slider("Ringing Artifacts", preset.ringingArtifacts, 0f, 1f);
                });
                DrawParameterWithState(preset, "MosquitoNoise", () =>
                {
                    preset.mosquitoNoise = EditorGUILayout.Slider("Mosquito Noise", preset.mosquitoNoise, 0f, 1f);
                });
                DrawParameterWithState(preset, "EdgeSensitivity", () =>
                {
                    preset.edgeSensitivity = EditorGUILayout.Slider("Edge Sensitivity", preset.edgeSensitivity, 0.1f, 2f);
                });

                // Show quality warning
                if (preset.jpegQuality < 10f && preset.effectIntensity > 0.8f)
                {
                    EditorGUILayout.HelpBox("⚠️ Quality Warning: Very low JPEG quality with high intensity may cause visual artifacts!", MessageType.Warning);
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Artistic Effects
            showArtisticEffects = EditorGUILayout.BeginFoldoutHeaderGroup(showArtisticEffects, "🎭 Artistic Effects");
            if (showArtisticEffects)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "GlitchTransition", () =>
                {
                    preset.glitchTransition = EditorGUILayout.Slider("Glitch Transition", preset.glitchTransition, 0f, 1f);
                });
                DrawParameterWithState(preset, "FeedbackIntensity", () =>
                {
                    preset.feedbackIntensity = EditorGUILayout.Slider("Feedback Intensity", preset.feedbackIntensity, 0f, 0.8f);
                });
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Brightness Control
            showBrightnessControl = EditorGUILayout.BeginFoldoutHeaderGroup(showBrightnessControl, "💡 Brightness Control");
            if (showBrightnessControl)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "NoiseTransparency", () =>
                {
                    preset.noiseTransparency = EditorGUILayout.Slider("Noise Transparency", preset.noiseTransparency, 0f, 0.5f);
                });
                DrawParameterWithState(preset, "MaxNoiseBrightness", () =>
                {
                    preset.maxNoiseBrightness = EditorGUILayout.Slider("Max Noise Brightness", preset.maxNoiseBrightness, 0f, 1f);
                });
                DrawParameterWithState(preset, "BrightnessThreshold", () =>
                {
                    preset.brightnessThreshold = EditorGUILayout.Slider("Brightness Threshold", preset.brightnessThreshold, 0f, 1f);
                });
                DrawParameterWithState(preset, "BrightAreaMasking", () =>
                {
                    preset.brightAreaMasking = EditorGUILayout.Slider("Bright Area Masking", preset.brightAreaMasking, 0f, 1f);
                });
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Debug & Utility
            showDebugUtility = EditorGUILayout.BeginFoldoutHeaderGroup(showDebugUtility, "🔍 Debug & Utility");
            if (showDebugUtility)
            {
                EditorGUILayout.BeginVertical("box");
                DrawParameterWithState(preset, "Oversharpening", () =>
                {
                    preset.oversharpening = EditorGUILayout.Slider("Oversharpening", preset.oversharpening, 0f, 10f);
                });
                DrawParameterWithState(preset, "VisualizeMotionVectors", () =>
                {
                    preset.visualizeMotionVectors = EditorGUILayout.Toggle("Visualize Motion Vectors", preset.visualizeMotionVectors);
                });
                DrawParameterWithState(preset, "DebugCompressionArtifacts", () =>
                {
                    preset.debugCompressionArtifacts = EditorGUILayout.Toggle("Debug Compression Artifacts", preset.debugCompressionArtifacts);
                });
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Save changes
            if (GUI.changed)
            {
                EditorUtility.SetDirty(preset);
            }
        }

        /// <summary>
        /// Draws a parameter with optional enable/disable checkbox
        /// </summary>
        private void DrawParameterWithState(FluxPreset preset, string parameterName, System.Action drawParameter)
        {
            if (preset.useParameterStates)
            {
                EditorGUILayout.BeginHorizontal();

                // Enable/disable checkbox
                EditorGUI.BeginChangeCheck();
                bool isEnabled = EditorGUILayout.Toggle(preset.IsParameterEnabled(parameterName), GUILayout.Width(20));
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(preset, $"Toggle {parameterName} State");
                    preset.SetParameterEnabled(parameterName, isEnabled);
                    EditorUtility.SetDirty(preset);
                }

                // Parameter control (grayed out if disabled)
                EditorGUI.BeginDisabledGroup(!isEnabled);
                drawParameter();
                EditorGUI.EndDisabledGroup();

                EditorGUILayout.EndHorizontal();
            }
            else
            {
                drawParameter();
            }
        }

        private void EnableAllParameters(FluxPreset preset)
        {
            var parameterNames = preset.GetType().GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(f => !f.Name.Contains("presetName") && !f.Name.Contains("description") && !f.Name.Contains("category") && !f.Name.Contains("useParameterStates"))
                .Select(f => f.Name)
                .ToArray();

            foreach (var paramName in parameterNames)
            {
                preset.SetParameterEnabled(paramName, true);
            }
        }

        private void DisableAllParameters(FluxPreset preset)
        {
            var parameterNames = preset.GetType().GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(f => !f.Name.Contains("presetName") && !f.Name.Contains("description") && !f.Name.Contains("category") && !f.Name.Contains("useParameterStates"))
                .Select(f => f.Name)
                .ToArray();

            foreach (var paramName in parameterNames)
            {
                preset.SetParameterEnabled(paramName, false);
            }
        }

        private void ApplyPresetToScene(FluxPreset preset)
        {
#if URP_INSTALLED
            // Try to find URP Volume with Flux Effect
            Volume[] volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var urpFlux))
                {
                    Undo.RecordObject(volume.profile, "Apply Flux Preset");
                    preset.ApplyToURP(urpFlux);
                    EditorUtility.SetDirty(volume.profile);
                    EditorUtility.DisplayDialog("Preset Applied",
                        $"Preset '{preset.GetDisplayName()}' applied to URP Volume Flux Effect.", "OK");
                    return;
                }
            }
#endif

            EditorUtility.DisplayDialog("No Flux Effect Found",
                "No active Flux Effect found in the scene. Please add a Flux Effect component or Volume Profile with URP.", "OK");
        }

        private void ValidatePresetWithDependencies(FluxPreset preset)
        {
            var results = ValidateParameterDependencies(preset);

            if (!results.hasIssues)
            {
                EditorUtility.DisplayDialog("Validation Passed",
                    "No issues found with this preset. It should work correctly.", "OK");
            }
            else
            {
                string message = "";
                if (results.errors.Count > 0)
                {
                    message += "ERRORS:\n" + string.Join("\n", results.errors.ToArray()) + "\n\n";
                }
                if (results.warnings.Count > 0)
                {
                    message += "WARNINGS:\n" + string.Join("\n", results.warnings.ToArray()) + "\n\n";
                }
                if (results.suggestions.Count > 0)
                {
                    message += "SUGGESTIONS:\n" + string.Join("\n", results.suggestions.ToArray());
                }

                EditorUtility.DisplayDialog("Validation Issues Found", message, "OK");
            }
        }

        private void AutoFixPresetWithDependencies(FluxPreset preset)
        {
            string fixes = AutoFixParameterDependencies(preset);

            if (fixes == "No fixes needed")
            {
                EditorUtility.DisplayDialog("No Fixes Needed",
                    "No automatic fixes were applied. The preset appears to be configured correctly.", "OK");
            }
            else
            {
                Undo.RecordObject(preset, "Auto-Fix Flux Preset");
                EditorUtility.SetDirty(preset);

                EditorUtility.DisplayDialog("Auto-Fix Applied",
                    $"The following fixes were applied:\n\n{fixes}\n\nPlease review the changes.", "OK");
            }
        }

        /// <summary>
        /// Validation results container for editor use
        /// </summary>
        private class ValidationResults
        {
            public System.Collections.Generic.List<string> errors = new System.Collections.Generic.List<string>();
            public System.Collections.Generic.List<string> warnings = new System.Collections.Generic.List<string>();
            public System.Collections.Generic.List<string> suggestions = new System.Collections.Generic.List<string>();

            public bool hasIssues => errors.Count > 0 || warnings.Count > 0;
        }

        /// <summary>
        /// Validates parameter dependencies (editor version)
        /// </summary>
        private ValidationResults ValidateParameterDependencies(FluxPreset preset)
        {
            var results = new ValidationResults();

            // Motion system validation
            bool hasMotionFeatures = preset.motionAmplification > 0f || preset.trailIntensity > 0f;
            bool hasReprojection = preset.reprojectBaseNoise > 0f || preset.reprojectLengthInfluence > 0f;

            if (hasMotionFeatures && !hasReprojection)
            {
                results.warnings.Add("Motion features enabled but reprojection disabled - trails may not work");
                results.suggestions.Add("Enable Base Noise (0.1+) or Length Influence (0.1+) for motion effects");
            }

            // Trail system validation
            if (preset.trailIntensity > 0f && preset.motionAmplification == 0f)
            {
                results.warnings.Add("Trail effects require Motion Amplification to be enabled");
                results.suggestions.Add("Enable Motion Amplification (0.1+) for trail effects");
            }

            // Performance validation
            if ((preset.blockSize == FluxPreset.FluxBlockSize._32x32 && preset.downscaling < 5) ||
                (preset.blockSize == FluxPreset.FluxBlockSize._16x16 && preset.downscaling < 3))
            {
                results.errors.Add("Dangerous performance combination: Large blocks with low downscaling");
                results.suggestions.Add("Increase downscaling or use smaller block size");
            }

            // Quality validation
            if (preset.jpegQuality < 10f && preset.effectIntensity > 0.8f)
            {
                results.warnings.Add("Quality warning: Very low JPEG quality with high intensity");
                results.suggestions.Add("Increase JPEG Quality to 15+ or reduce Effect Intensity");
            }

            return results;
        }

        /// <summary>
        /// Auto-fixes common parameter conflicts (editor version)
        /// </summary>
        private string AutoFixParameterDependencies(FluxPreset preset)
        {
            var fixes = new System.Collections.Generic.List<string>();

            // Auto-enable reprojection if motion features are used
            bool hasMotionFeatures = preset.motionAmplification > 0f || preset.trailIntensity > 0f;
            bool hasReprojection = preset.reprojectBaseNoise > 0f || preset.reprojectLengthInfluence > 0f;

            if (hasMotionFeatures && !hasReprojection)
            {
                preset.reprojectBaseNoise = 0.1f;
                fixes.Add("Enabled Base Noise (0.1) to support motion features");
            }

            // Auto-enable motion amplification for trails
            if (preset.trailIntensity > 0f && preset.motionAmplification == 0f)
            {
                preset.motionAmplification = 0.1f;
                fixes.Add("Enabled Motion Amplification (0.1) to support trail effects");
            }

            // Auto-adjust dangerous performance combinations
            if (preset.blockSize == FluxPreset.FluxBlockSize._32x32 && preset.downscaling < 5)
            {
                preset.downscaling = 6;
                fixes.Add("Increased downscaling to 6 for better 32x32 block performance");
            }

            if (preset.blockSize == FluxPreset.FluxBlockSize._16x16 && preset.downscaling < 3)
            {
                preset.downscaling = 4;
                fixes.Add("Increased downscaling to 4 for better 16x16 block performance");
            }

            // Auto-fix quality issues
            if (preset.jpegQuality < 10f && preset.effectIntensity > 0.8f)
            {
                preset.jpegQuality = 15f;
                fixes.Add("Increased JPEG Quality to 15 for better visual quality");
            }

            return fixes.Count > 0 ? string.Join("\n", fixes) : "No fixes needed";
        }

        [MenuItem("Stylo/Flux/Create Default Presets")]
        public static void CreateDefaultPresetsMenuItem()
        {
            FluxPresetManager.CreateDefaultPresets();
            EditorUtility.DisplayDialog("Default Presets Created",
                "Default Flux presets have been created in Assets/Stylo/Flux/Presets/", "OK");
        }

        [MenuItem("Stylo/Flux/Refresh Preset Cache")]
        public static void RefreshPresetCacheMenuItem()
        {
            FluxPresetManager.RefreshPresetCache();
            EditorUtility.DisplayDialog("Cache Refreshed",
                "Flux preset cache has been refreshed.", "OK");
        }

        [MenuItem("Stylo/Flux/Save All Current Settings", true)]
        public static bool ValidateSaveAllCurrentSettings()
        {
#if URP_INSTALLED
            // Only enable if we're in play mode or have active Flux effects
            return Application.isPlaying || FindObjectsByType<Volume>(FindObjectsSortMode.None).Length > 0;
#else
            return false;
#endif
        }

        [MenuItem("Stylo/Flux/Save All Current Settings")]
        public static void SaveAllCurrentSettingsMenuItem()
        {
#if URP_INSTALLED
            try
            {
                Volume[] volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
                int savedCount = 0;

                foreach (var volume in volumes)
                {
                    if (volume.profile != null && volume.profile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var urpFlux))
                    {
                        // Create a preset from current settings
                        var preset = ScriptableObject.CreateInstance<FluxPreset>();
                        preset.CaptureFromURP(urpFlux);
                        preset.presetName = $"Runtime Settings {volume.name} {System.DateTime.Now:HH-mm-ss}";
                        preset.description = $"Captured from {volume.name} during runtime";
                        preset.category = "Runtime";

                        // Save to Assets folder
                        string path = $"Assets/Flux_Runtime_Settings_{volume.name}_{System.DateTime.Now:yyyy-MM-dd_HH-mm-ss}.asset";
                        path = AssetDatabase.GenerateUniqueAssetPath(path);

                        AssetDatabase.CreateAsset(preset, path);
                        savedCount++;
                    }
                }

                if (savedCount > 0)
                {
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                    EditorUtility.DisplayDialog("Settings Saved",
                        $"Saved current settings from {savedCount} Flux effect(s) to preset files.", "OK");
                }
                else
                {
                    EditorUtility.DisplayDialog("No Effects Found",
                        "No active Flux effects found to save settings from.", "OK");
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.DisplayDialog("Save Error",
                    $"Failed to save current settings: {ex.Message}", "OK");
                Debug.LogError($"[Flux] Failed to save current settings: {ex}");
            }
#endif
        }

        [MenuItem("Stylo/Flux/Capture from Active Scene")]
        public static void CaptureFromActiveSceneMenuItem()
        {
#if URP_INSTALLED
            Volume[] volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
            if (volumes.Length == 0)
            {
                EditorUtility.DisplayDialog("No Volumes Found",
                    "No Volume components found in the active scene.", "OK");
                return;
            }

            // Show selection dialog if multiple volumes
            Volume selectedVolume = null;
            if (volumes.Length == 1)
            {
                selectedVolume = volumes[0];
            }
            else
            {
                string[] volumeNames = new string[volumes.Length];
                for (int i = 0; i < volumes.Length; i++)
                {
                    volumeNames[i] = volumes[i].name;
                }

                int selectedIndex = EditorUtility.DisplayDialogComplex(
                    "Multiple Volumes Found",
                    "Multiple Volume components found. Which one would you like to capture from?",
                    volumeNames[0],
                    "Cancel",
                    volumes.Length > 1 ? volumeNames[1] : "");

                if (selectedIndex == 1) return; // Cancel
                selectedVolume = volumes[selectedIndex == 2 ? 1 : 0];
            }

            if (selectedVolume?.profile != null && selectedVolume.profile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var fluxEffect))
            {
                // Create preset
                var preset = ScriptableObject.CreateInstance<FluxPreset>();
                preset.CaptureFromURP(fluxEffect);
                preset.presetName = $"Scene Capture {selectedVolume.name}";
                preset.description = $"Captured from {selectedVolume.name} in scene {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}";
                preset.category = "Scene";

                // Save with dialog
                string path = EditorUtility.SaveFilePanelInProject(
                    "Save Captured Preset",
                    preset.presetName + ".asset",
                    "asset",
                    "Choose where to save the captured preset");

                if (!string.IsNullOrEmpty(path))
                {
                    AssetDatabase.CreateAsset(preset, path);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();

                    Selection.activeObject = preset;
                    EditorGUIUtility.PingObject(preset);

                    EditorUtility.DisplayDialog("Preset Captured",
                        $"Successfully captured preset from '{selectedVolume.name}' and saved to '{path}'", "OK");
                }
                else
                {
                    DestroyImmediate(preset);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("No Flux Effect",
                    $"Volume '{selectedVolume?.name}' does not contain a Flux Effect.", "OK");
            }
#endif
        }

        public static void CreateFluxPresetAsset()
        {
            FluxPresetManager.CreatePresetAsset("New Flux Preset");
        }
    }
}
#endif
