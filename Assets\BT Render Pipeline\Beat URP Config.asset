%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf2edee5c58d82540a51f03df9d42094, type: 3}
  m_Name: Beat URP Config
  m_EditorClassIdentifier: 
  k_AssetVersion: 12
  k_AssetPreviousVersion: 12
  m_RendererType: 1
  m_RendererData: {fileID: 0}
  m_RendererDataList:
  - {fileID: 11400000, guid: 60578be7f2e0f5943ab89d9409c3361c, type: 2}
  m_DefaultRendererIndex: 0
  m_RequireDepthTexture: 1
  m_RequireOpaqueTexture: 1
  m_OpaqueDownsampling: 0
  m_SupportsTerrainHoles: 1
  m_SupportsHDR: 1
  m_HDRColorBufferPrecision: 0
  m_MSAA: 2
  m_RenderScale: 1
  m_UpscalingFilter: 0
  m_FsrOverrideSharpness: 0
  m_FsrSharpness: 0.92
  m_EnableLODCrossFade: 1
  m_LODCrossFadeDitheringType: 1
  m_ShEvalMode: 0
  m_LightProbeSystem: 1
  m_ProbeVolumeMemoryBudget: 1024
  m_ProbeVolumeBlendingMemoryBudget: 256
  m_SupportProbeVolumeGPUStreaming: 1
  m_SupportProbeVolumeDiskStreaming: 1
  m_SupportProbeVolumeScenarios: 1
  m_SupportProbeVolumeScenarioBlending: 1
  m_ProbeVolumeSHBands: 1
  m_MainLightRenderingMode: 1
  m_MainLightShadowsSupported: 1
  m_MainLightShadowmapResolution: 1024
  m_AdditionalLightsRenderingMode: 1
  m_AdditionalLightsPerObjectLimit: 4
  m_AdditionalLightShadowsSupported: 1
  m_AdditionalLightsShadowmapResolution: 1024
  m_AdditionalLightsShadowResolutionTierLow: 256
  m_AdditionalLightsShadowResolutionTierMedium: 512
  m_AdditionalLightsShadowResolutionTierHigh: 1024
  m_ReflectionProbeBlending: 1
  m_ReflectionProbeBoxProjection: 1
  m_ReflectionProbeAtlas: 1
  m_ShadowDistance: 100
  m_ShadowCascadeCount: 2
  m_Cascade2Split: 0.25
  m_Cascade3Split: {x: 0.1, y: 0.3}
  m_Cascade4Split: {x: 0.067, y: 0.19174919, z: 0.46700004}
  m_CascadeBorder: 0.2
  m_ShadowDepthBias: 1
  m_ShadowNormalBias: 1
  m_AnyShadowsSupported: 1
  m_SoftShadowsSupported: 0
  m_ConservativeEnclosingSphere: 1
  m_NumIterationsEnclosingSphere: 64
  m_SoftShadowQuality: 2
  m_AdditionalLightsCookieResolution: 1024
  m_AdditionalLightsCookieFormat: 3
  m_UseSRPBatcher: 1
  m_SupportsDynamicBatching: 1
  m_MixedLightingSupported: 1
  m_SupportsLightCookies: 1
  m_SupportsLightLayers: 1
  m_DebugLevel: 1
  m_StoreActionsOptimization: 0
  m_UseAdaptivePerformance: 1
  m_ColorGradingMode: 1
  m_ColorGradingLutSize: 32
  m_AllowPostProcessAlphaOutput: 0
  m_UseFastSRGBLinearConversion: 0
  m_SupportDataDrivenLensFlare: 1
  m_SupportScreenSpaceLensFlare: 1
  m_GPUResidentDrawerMode: 1
  m_SmallMeshScreenPercentage: 0
  m_GPUResidentDrawerEnableOcclusionCullingInCameras: 1
  m_ShadowType: 1
  m_LocalShadowsSupported: 0
  m_LocalShadowsAtlasResolution: 256
  m_MaxPixelLights: 0
  m_ShadowAtlasResolution: 256
  m_VolumeFrameworkUpdateMode: 0
  m_VolumeProfile: {fileID: 0}
  apvScenesData:
    obsoleteSceneBounds:
      m_Keys: []
      m_Values: []
    obsoleteHasProbeVolumes:
      m_Keys: []
      m_Values: 
  m_PrefilteringModeMainLightShadows: 3
  m_PrefilteringModeAdditionalLight: 0
  m_PrefilteringModeAdditionalLightShadows: 2
  m_PrefilterXRKeywords: 1
  m_PrefilteringModeForwardPlus: 2
  m_PrefilteringModeDeferredRendering: 0
  m_PrefilteringModeScreenSpaceOcclusion: 0
  m_PrefilterDebugKeywords: 1
  m_PrefilterWriteRenderingLayers: 1
  m_PrefilterHDROutput: 0
  m_PrefilterAlphaOutput: 1
  m_PrefilterSSAODepthNormals: 1
  m_PrefilterSSAOSourceDepthLow: 1
  m_PrefilterSSAOSourceDepthMedium: 1
  m_PrefilterSSAOSourceDepthHigh: 1
  m_PrefilterSSAOInterleaved: 1
  m_PrefilterSSAOBlueNoise: 1
  m_PrefilterSSAOSampleCountLow: 1
  m_PrefilterSSAOSampleCountMedium: 1
  m_PrefilterSSAOSampleCountHigh: 1
  m_PrefilterDBufferMRT1: 1
  m_PrefilterDBufferMRT2: 1
  m_PrefilterDBufferMRT3: 1
  m_PrefilterSoftShadowsQualityLow: 1
  m_PrefilterSoftShadowsQualityMedium: 1
  m_PrefilterSoftShadowsQualityHigh: 1
  m_PrefilterSoftShadows: 0
  m_PrefilterScreenCoord: 1
  m_PrefilterNativeRenderPass: 1
  m_PrefilterUseLegacyLightmaps: 1
  m_PrefilterBicubicLightmapSampling: 1
  m_ShaderVariantLogLevel: 0
  m_ShadowCascades: 2
  m_Textures:
    blueNoise64LTex: {fileID: 2800000, guid: e3d24661c1e055f45a7560c033dbb837, type: 3}
    bayerMatrixTex: {fileID: 2800000, guid: f9ee4ed84c1d10c49aabb9b210b0fc44, type: 3}
