using UnityEngine;
using System.Linq;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux
{
    [CreateAssetMenu(fileName = "New Flux Preset", menuName = "Stylo/Flux Preset", order = 1)]
    public class FluxPreset : ScriptableObject
    {
        [Header("Preset Information")]
        public string presetName = "New Flux Preset";
        [TextArea(2, 4)]
        public string description = "Custom datamoshing preset";
        public string category = "Custom";

        [Header("🔧 Parameter State Control")]
        [Tooltip("Controls which parameters are enabled/disabled when applying this preset")]
        public bool useParameterStates = true;

        /// <summary>
        /// Tracks which parameters are enabled/disabled in this preset
        /// Key: parameter name, Value: enabled state
        /// </summary>
        [SerializeField]
        private Dictionary<string, bool> parameterStates = new Dictionary<string, bool>();

        /// <summary>
        /// Serialized version of parameter states for Unity serialization
        /// </summary>
        [SerializeField]
        private List<ParameterState> serializedParameterStates = new List<ParameterState>();

        [System.Serializable]
        public class ParameterState
        {
            public string parameterName;
            public bool enabled;

            public ParameterState(string name, bool state)
            {
                parameterName = name;
                enabled = state;
            }
        }

        [Header("🔧 Master Controls")]
        [Range(0f, 1f)] public float effectIntensity = 0.35f;
        public bool onlyStenciled = false;

        [Header("🎯 TRUE DATAMOSHING")]
        [Header("Compression Encoding")]
        [Range(0f, 1f)] public float colorCrunch = 0.5f;
        [Range(1, 10)] public int downscaling = 10;
        public FluxBlockSize blockSize = FluxBlockSize._16x16;
        public bool dontCrunchSkybox = true;

        [Header("Motion Vector Reprojection")]
        [Range(0f, 1f)] public float reprojectBaseNoise = 0f;
        [Range(0f, 20f)] public float reprojectBaseRerollSpeed = 3f;
        [Range(0f, 5f)] public float reprojectLengthInfluence = 0f;

        [Header("Authentic Compression Artifacts")]
        [Range(0f, 1f)] public float keyframeResetRate = 0f;
        [Range(0f, 2f)] public float motionVectorCorruption = 0f;
        [Range(0f, 1f)] public float errorAccumulation = 0f;
        [Range(0f, 1f)] public float dctCorruption = 0f;
        [Range(0f, 1f)] public float chromaCorruption = 0f;

        [Header("🌟 ENHANCED VISUAL EFFECTS")]
        [Header("Consolidated Motion Processing")]
        [Range(0f, 10f)] public float motionAmplification = 3f;
        [Range(0f, 0.1f)] public float motionThreshold = 0.001f;
        [Range(0f, 1f)] public float cameraObjectMotionBalance = 0.3f;
        [Range(0f, 1f)] public float motionSmoothing = 0.1f;

        [Header("Pixel Flow & Trailing")]
        [Range(0f, 5f)] public float trailIntensity = 2f;
        [Range(0f, 1f)] public float trailSmoothness = 0.5f;
        [Range(0f, 1f)] public float trailPersistence = 0.8f;
        [Range(0f, 5f)] public float flowSpread = 2f;

        [Header("Artistic Effects")]
        [Range(0f, 1f)] public float glitchTransition = 0f;
        [Range(0f, 0.8f)] public float feedbackIntensity = 0f;
        [Range(0f, 1f)] public float multiScaleCorruption = 0f;

        [Header("JPEG Quality Control")]
        [Range(1f, 100f)] public float jpegQuality = 50f;
        [Range(0f, 2f)] public float luminanceQuantization = 0.5f;
        [Range(0f, 2f)] public float chrominanceQuantization = 0.7f;
        public bool chromaSubsampling = false;

        [Header("Compression Artifacts")]
        [Range(0f, 1f)] public float ringingArtifacts = 0f;
        [Range(0f, 1f)] public float mosquitoNoise = 0f;
        [Range(0.1f, 2f)] public float edgeSensitivity = 0.5f;



        [Header("💡 Brightness Control")]
        [Range(0f, 0.5f)] public float noiseTransparency = 0.05f;
        [Range(0f, 1f)] public float maxNoiseBrightness = 0.9f;
        [Range(0f, 1f)] public float brightnessThreshold = 0.7f;
        [Range(0f, 1f)] public float brightAreaMasking = 0.8f;

        [Header("🔧 Utility Controls")]
        [Range(0f, 10f)] public float oversharpening = 0f;
        public bool visualizeMotionVectors = false;
        public bool debugCompressionArtifacts = false;

        // Enum to match both Runtime and SRP versions
        public enum FluxBlockSize
        {
            _2x2 = 0,
            _4x4 = 1,
            _8x8 = 2,
            _16x16 = 3,
            _32x32 = 4
        }

        /// <summary>
        /// Returns a formatted display name for UI
        /// </summary>
        public string GetDisplayName()
        {
            return string.IsNullOrEmpty(presetName) ? name : presetName;
        }

        /// <summary>
        /// Returns category and name for organized display
        /// </summary>
        public string GetCategorizedName()
        {
            return string.IsNullOrEmpty(category) ? GetDisplayName() : $"{category}/{GetDisplayName()}";
        }

        /// <summary>
        /// Sets the enabled state for a parameter
        /// </summary>
        public void SetParameterEnabled(string parameterName, bool enabled)
        {
            if (parameterStates == null)
                parameterStates = new Dictionary<string, bool>();

            parameterStates[parameterName] = enabled;
            SyncSerializedStates();
        }

        /// <summary>
        /// Gets the enabled state for a parameter (defaults to true if not found)
        /// </summary>
        public bool IsParameterEnabled(string parameterName)
        {
            if (!useParameterStates) return true;

            if (parameterStates == null)
                LoadSerializedStates();

            return parameterStates.ContainsKey(parameterName) ? parameterStates[parameterName] : true;
        }

        /// <summary>
        /// Initializes parameter states with default enabled values for all parameters
        /// </summary>
        public void InitializeParameterStates()
        {
            if (parameterStates == null)
                parameterStates = new Dictionary<string, bool>();

            // Initialize all parameters as enabled by default
            var parameterNames = GetAllParameterNames();
            foreach (var paramName in parameterNames)
            {
                if (!parameterStates.ContainsKey(paramName))
                    parameterStates[paramName] = true;
            }

            SyncSerializedStates();
        }

        /// <summary>
        /// Gets all parameter names in this preset
        /// </summary>
        private string[] GetAllParameterNames()
        {
            return new string[]
            {
                // Master Controls
                "EffectIntensity", "OnlyStenciled",
                
                // TRUE DATAMOSHING - Compression Encoding
                "ColorCrunch", "Downscaling", "BlockSize", "DontCrunchSkybox",
                
                // TRUE DATAMOSHING - Motion Vector Reprojection
                "ReprojectBaseNoise", "ReprojectBaseRerollSpeed", "ReprojectLengthInfluence",
                
                // TRUE DATAMOSHING - Authentic Compression Artifacts
                "KeyframeResetRate", "MotionVectorCorruption", "ErrorAccumulation",
                "DCTCorruption", "ChromaCorruption",
                
                // ENHANCED VISUAL EFFECTS - Consolidated Motion Processing
                "MotionAmplification", "MotionThreshold", "CameraObjectMotionBalance", "MotionSmoothing",
                
                // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
                "TrailIntensity", "TrailSmoothness", "TrailPersistence", "FlowSpread",
                
                // ENHANCED VISUAL EFFECTS - Artistic effects
                "GlitchTransition", "FeedbackIntensity", "MultiScaleCorruption",
                
                // JPEG Quality Control
                "JPEGQuality", "LuminanceQuantization", "ChrominanceQuantization", "ChromaSubsampling",
                
                // Compression Artifacts
                "RingingArtifacts", "MosquitoNoise", "EdgeSensitivity",
                
                // Brightness Control
                "NoiseTransparency", "MaxNoiseBrightness", "BrightnessThreshold", "BrightAreaMasking",
                
                // Utility Controls
                "Oversharpening", "VisualizeMotionVectors", "DebugCompressionArtifacts"
            };
        }

        /// <summary>
        /// Syncs the dictionary to the serialized list for Unity serialization
        /// </summary>
        private void SyncSerializedStates()
        {
            if (parameterStates == null) return;

            serializedParameterStates.Clear();
            foreach (var kvp in parameterStates)
            {
                serializedParameterStates.Add(new ParameterState(kvp.Key, kvp.Value));
            }
        }

        /// <summary>
        /// Loads the dictionary from the serialized list
        /// </summary>
        private void LoadSerializedStates()
        {
            if (parameterStates == null)
                parameterStates = new Dictionary<string, bool>();

            parameterStates.Clear();
            foreach (var state in serializedParameterStates)
            {
                parameterStates[state.parameterName] = state.enabled;
            }

            // Initialize any missing parameters as enabled
            InitializeParameterStates();
        }

        /// <summary>
        /// Called when the ScriptableObject is loaded
        /// </summary>
        private void OnEnable()
        {
            LoadSerializedStates();
        }

        /// <summary>
        /// Called before serialization
        /// </summary>
        private void OnBeforeSerialize()
        {
            SyncSerializedStates();
        }

        /// <summary>
        /// Called after deserialization
        /// </summary>
        private void OnAfterDeserialize()
        {
            LoadSerializedStates();
        }

        /// <summary>
        /// Creates a preset from current Runtime Flux Effect parameters
        /// </summary>
        public void CaptureFromRuntime(object fluxEffect)
        {
            // Use reflection to avoid assembly reference issues
            if (fluxEffect == null) return;

            var type = fluxEffect.GetType();

            // Master controls
            effectIntensity = GetFloatProperty(fluxEffect, type, "EffectIntensity");
            onlyStenciled = GetBoolProperty(fluxEffect, type, "OnlyStenciled");

            // TRUE DATAMOSHING - Compression encoding
            colorCrunch = GetFloatProperty(fluxEffect, type, "ColorCrunch");
            downscaling = GetIntProperty(fluxEffect, type, "Downscaling");
            blockSize = (FluxBlockSize)GetIntProperty(fluxEffect, type, "BlockSize");
            dontCrunchSkybox = GetBoolProperty(fluxEffect, type, "DontCrunchSkybox");

            // TRUE DATAMOSHING - Motion vector reprojection
            reprojectBaseNoise = GetFloatProperty(fluxEffect, type, "ReprojectBaseNoise");
            reprojectBaseRerollSpeed = GetFloatProperty(fluxEffect, type, "ReprojectBaseRerollSpeed");
            reprojectLengthInfluence = GetFloatProperty(fluxEffect, type, "ReprojectLengthInfluence");

            // TRUE DATAMOSHING - Authentic compression artifacts
            keyframeResetRate = GetFloatProperty(fluxEffect, type, "KeyframeResetRate");
            motionVectorCorruption = GetFloatProperty(fluxEffect, type, "MotionVectorCorruption");
            errorAccumulation = GetFloatProperty(fluxEffect, type, "ErrorAccumulation");
            dctCorruption = GetFloatProperty(fluxEffect, type, "DCTCorruption");
            chromaCorruption = GetFloatProperty(fluxEffect, type, "ChromaCorruption");

            // CONSOLIDATED MOTION PROCESSING - New unified system
            motionAmplification = GetFloatProperty(fluxEffect, type, "MotionAmplification");
            motionThreshold = GetFloatProperty(fluxEffect, type, "MotionThreshold");
            cameraObjectMotionBalance = GetFloatProperty(fluxEffect, type, "CameraObjectMotionBalance");
            motionSmoothing = GetFloatProperty(fluxEffect, type, "MotionSmoothing");

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            trailIntensity = GetFloatProperty(fluxEffect, type, "TrailIntensity");
            trailSmoothness = GetFloatProperty(fluxEffect, type, "TrailSmoothness");
            trailPersistence = GetFloatProperty(fluxEffect, type, "TrailPersistence");
            flowSpread = GetFloatProperty(fluxEffect, type, "FlowSpread");

            // ENHANCED VISUAL EFFECTS - Artistic effects
            glitchTransition = GetFloatProperty(fluxEffect, type, "GlitchTransition");
            feedbackIntensity = GetFloatProperty(fluxEffect, type, "FeedbackIntensity");
            multiScaleCorruption = GetFloatProperty(fluxEffect, type, "MultiScaleCorruption");

            // JPEG Quality Control
            jpegQuality = GetFloatProperty(fluxEffect, type, "JPEGQuality");
            luminanceQuantization = GetFloatProperty(fluxEffect, type, "LuminanceQuantization");
            chrominanceQuantization = GetFloatProperty(fluxEffect, type, "ChrominanceQuantization");
            chromaSubsampling = GetBoolProperty(fluxEffect, type, "ChromaSubsampling");

            // Compression Artifacts
            ringingArtifacts = GetFloatProperty(fluxEffect, type, "RingingArtifacts");
            mosquitoNoise = GetFloatProperty(fluxEffect, type, "MosquitoNoise");
            edgeSensitivity = GetFloatProperty(fluxEffect, type, "EdgeSensitivity");



            // Brightness Control
            noiseTransparency = GetFloatProperty(fluxEffect, type, "NoiseTransparency");
            maxNoiseBrightness = GetFloatProperty(fluxEffect, type, "MaxNoiseBrightness");
            brightnessThreshold = GetFloatProperty(fluxEffect, type, "BrightnessThreshold");
            brightAreaMasking = GetFloatProperty(fluxEffect, type, "BrightAreaMasking");

            // Utility controls
            oversharpening = GetFloatProperty(fluxEffect, type, "Oversharpening");
            visualizeMotionVectors = GetBoolProperty(fluxEffect, type, "PreviewMotionVectors");
            debugCompressionArtifacts = GetBoolProperty(fluxEffect, type, "DebugCompressionArtifacts");

            // Capture parameter enabled states if supported
            CaptureParameterStates(fluxEffect, type);
        }

        /// <summary>
        /// Captures parameter enabled states from the effect (if supported)
        /// </summary>
        private void CaptureParameterStates(object fluxEffect, System.Type type)
        {
            // Initialize parameter states
            InitializeParameterStates();

            // Try to capture enabled states for URP Volume Parameters
            var parameterNames = GetAllParameterNames();
            foreach (var paramName in parameterNames)
            {
                bool isEnabled = GetParameterEnabledState(fluxEffect, type, paramName);
                SetParameterEnabled(paramName, isEnabled);
            }
        }

        /// <summary>
        /// Gets the enabled state of a parameter from the effect
        /// </summary>
        private bool GetParameterEnabledState(object fluxEffect, System.Type type, string parameterName)
        {
            try
            {
                var prop = type.GetProperty(parameterName);
                if (prop != null)
                {
                    var value = prop.GetValue(fluxEffect);

                    // Check if it's a VolumeParameter with overrideState
                    var overrideStateProp = value?.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null)
                    {
                        return (bool)overrideStateProp.GetValue(value);
                    }
                }
            }
            catch (System.Exception)
            {
                // If we can't determine the state, default to enabled
            }

            return true; // Default to enabled if we can't determine the state
        }

        /// <summary>
        /// Applies this preset to a Runtime Flux Effect
        /// </summary>
        public void ApplyToRuntime(object fluxEffect)
        {
            // Use reflection to avoid assembly reference issues
            if (fluxEffect == null) return;

            var type = fluxEffect.GetType();

            // Master controls
            SetFloatPropertyWithState(fluxEffect, type, "EffectIntensity", effectIntensity);
            SetBoolPropertyWithState(fluxEffect, type, "OnlyStenciled", onlyStenciled);

            // TRUE DATAMOSHING - Compression encoding
            SetFloatPropertyWithState(fluxEffect, type, "ColorCrunch", colorCrunch);
            SetIntPropertyWithState(fluxEffect, type, "Downscaling", downscaling);
            SetEnumPropertyWithState(fluxEffect, type, "BlockSize", (int)blockSize);
            SetBoolPropertyWithState(fluxEffect, type, "DontCrunchSkybox", dontCrunchSkybox);

            // TRUE DATAMOSHING - Motion vector reprojection
            SetFloatPropertyWithState(fluxEffect, type, "ReprojectBaseNoise", reprojectBaseNoise);
            SetFloatPropertyWithState(fluxEffect, type, "ReprojectBaseRerollSpeed", reprojectBaseRerollSpeed);
            SetFloatPropertyWithState(fluxEffect, type, "ReprojectLengthInfluence", reprojectLengthInfluence);

            // TRUE DATAMOSHING - Authentic compression artifacts
            SetFloatPropertyWithState(fluxEffect, type, "KeyframeResetRate", keyframeResetRate);
            SetFloatPropertyWithState(fluxEffect, type, "MotionVectorCorruption", motionVectorCorruption);
            SetFloatPropertyWithState(fluxEffect, type, "ErrorAccumulation", errorAccumulation);
            SetFloatPropertyWithState(fluxEffect, type, "DCTCorruption", dctCorruption);
            SetFloatPropertyWithState(fluxEffect, type, "ChromaCorruption", chromaCorruption);

            // CONSOLIDATED MOTION PROCESSING - New unified system
            SetFloatPropertyWithState(fluxEffect, type, "MotionAmplification", motionAmplification);
            SetFloatPropertyWithState(fluxEffect, type, "MotionThreshold", motionThreshold);
            SetFloatPropertyWithState(fluxEffect, type, "CameraObjectMotionBalance", cameraObjectMotionBalance);
            SetFloatPropertyWithState(fluxEffect, type, "MotionSmoothing", motionSmoothing);

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            SetFloatPropertyWithState(fluxEffect, type, "TrailIntensity", trailIntensity);
            SetFloatPropertyWithState(fluxEffect, type, "TrailSmoothness", trailSmoothness);
            SetFloatPropertyWithState(fluxEffect, type, "TrailPersistence", trailPersistence);
            SetFloatPropertyWithState(fluxEffect, type, "FlowSpread", flowSpread);

            // ENHANCED VISUAL EFFECTS - Artistic effects
            SetFloatPropertyWithState(fluxEffect, type, "GlitchTransition", glitchTransition);
            SetFloatPropertyWithState(fluxEffect, type, "FeedbackIntensity", feedbackIntensity);
            SetFloatPropertyWithState(fluxEffect, type, "MultiScaleCorruption", multiScaleCorruption);

            // JPEG Quality Control
            SetFloatPropertyWithState(fluxEffect, type, "JPEGQuality", jpegQuality);
            SetFloatPropertyWithState(fluxEffect, type, "LuminanceQuantization", luminanceQuantization);
            SetFloatPropertyWithState(fluxEffect, type, "ChrominanceQuantization", chrominanceQuantization);
            SetBoolPropertyWithState(fluxEffect, type, "ChromaSubsampling", chromaSubsampling);

            // Compression Artifacts
            SetFloatPropertyWithState(fluxEffect, type, "RingingArtifacts", ringingArtifacts);
            SetFloatPropertyWithState(fluxEffect, type, "MosquitoNoise", mosquitoNoise);
            SetFloatPropertyWithState(fluxEffect, type, "EdgeSensitivity", edgeSensitivity);

            // Brightness Control
            SetFloatPropertyWithState(fluxEffect, type, "NoiseTransparency", noiseTransparency);
            SetFloatPropertyWithState(fluxEffect, type, "MaxNoiseBrightness", maxNoiseBrightness);
            SetFloatPropertyWithState(fluxEffect, type, "BrightnessThreshold", brightnessThreshold);
            SetFloatPropertyWithState(fluxEffect, type, "BrightAreaMasking", brightAreaMasking);

            // Utility controls
            SetFloatPropertyWithState(fluxEffect, type, "Oversharpening", oversharpening);
            SetBoolPropertyWithState(fluxEffect, type, "PreviewMotionVectors", visualizeMotionVectors);
            SetBoolPropertyWithState(fluxEffect, type, "DebugCompressionArtifacts", debugCompressionArtifacts);
        }

#if URP_INSTALLED
        /// <summary>
        /// Creates a preset from current URP Flux Effect parameters
        /// </summary>
        public void CaptureFromURP(Stylo.Flux.Universal.FluxEffect fluxEffect)
        {
            if (fluxEffect == null)
            {
                throw new System.ArgumentNullException(nameof(fluxEffect), "FluxEffect cannot be null");
            }

            try
            {
                // Master controls
                effectIntensity = fluxEffect.EffectIntensity.value;
                onlyStenciled = fluxEffect.OnlyStenciled.value;

                // TRUE DATAMOSHING - Compression encoding
                colorCrunch = fluxEffect.ColorCrunch.value;
                downscaling = fluxEffect.Downscaling.value;
                blockSize = (FluxBlockSize)(int)fluxEffect.BlockSize.value;
                dontCrunchSkybox = fluxEffect.DontCrunchSkybox.value;

                // TRUE DATAMOSHING - Motion vector reprojection
                reprojectBaseNoise = fluxEffect.ReprojectBaseNoise.value;
                reprojectBaseRerollSpeed = fluxEffect.ReprojectBaseRerollSpeed.value;
                reprojectLengthInfluence = fluxEffect.ReprojectLengthInfluence.value;

                // TRUE DATAMOSHING - Authentic compression artifacts
                keyframeResetRate = fluxEffect.KeyframeResetRate.value;
                motionVectorCorruption = fluxEffect.MotionVectorCorruption.value;
                errorAccumulation = fluxEffect.ErrorAccumulation.value;
                dctCorruption = fluxEffect.DCTCorruption.value;
                chromaCorruption = fluxEffect.ChromaCorruption.value;

                // CONSOLIDATED MOTION PROCESSING - New unified system
                motionAmplification = fluxEffect.MotionAmplification.value;
                motionThreshold = fluxEffect.MotionThreshold.value;
                cameraObjectMotionBalance = fluxEffect.CameraObjectMotionBalance.value;
                motionSmoothing = fluxEffect.MotionSmoothing.value;

                // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
                trailIntensity = fluxEffect.TrailIntensity.value;
                trailSmoothness = fluxEffect.TrailSmoothness.value;
                trailPersistence = fluxEffect.TrailPersistence.value;
                flowSpread = fluxEffect.FlowSpread.value;

                // ENHANCED VISUAL EFFECTS - Artistic effects
                glitchTransition = fluxEffect.GlitchTransition.value;
                feedbackIntensity = fluxEffect.FeedbackIntensity.value;
                multiScaleCorruption = fluxEffect.MultiScaleCorruption.value;

                // JPEG Quality Control
                jpegQuality = fluxEffect.JPEGQuality.value;
                luminanceQuantization = fluxEffect.LuminanceQuantization.value;
                chrominanceQuantization = fluxEffect.ChrominanceQuantization.value;
                chromaSubsampling = fluxEffect.ChromaSubsampling.value;

                // Compression Artifacts
                ringingArtifacts = fluxEffect.RingingArtifacts.value;
                mosquitoNoise = fluxEffect.MosquitoNoise.value;
                edgeSensitivity = fluxEffect.EdgeSensitivity.value;

                // Brightness Control
                noiseTransparency = fluxEffect.NoiseTransparency.value;
                maxNoiseBrightness = fluxEffect.MaxNoiseBrightness.value;
                brightnessThreshold = fluxEffect.BrightnessThreshold.value;
                brightAreaMasking = fluxEffect.BrightAreaMasking.value;

                // Utility controls
                oversharpening = fluxEffect.Oversharpening.value;
                visualizeMotionVectors = fluxEffect.VisualizeMotionVectors.value;
                debugCompressionArtifacts = fluxEffect.DebugCompressionArtifacts.value;

                // Capture parameter enabled states from URP Volume Parameters
                CaptureParameterStatesFromURP(fluxEffect);

#if UNITY_EDITOR
                // Log successful capture for debugging
                UnityEngine.Debug.Log($"[Flux] Successfully captured {GetParameterCount()} parameters from FluxEffect");
#endif
            }
            catch (System.Exception ex)
            {
                throw new System.InvalidOperationException($"Failed to capture parameters from FluxEffect: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Captures parameter enabled states from URP FluxEffect
        /// </summary>
        private void CaptureParameterStatesFromURP(Stylo.Flux.Universal.FluxEffect fluxEffect)
        {
            // Initialize parameter states
            InitializeParameterStates();

            // Capture enabled states for each parameter
            SetParameterEnabled("EffectIntensity", fluxEffect.EffectIntensity.overrideState);
            SetParameterEnabled("OnlyStenciled", fluxEffect.OnlyStenciled.overrideState);

            SetParameterEnabled("ColorCrunch", fluxEffect.ColorCrunch.overrideState);
            SetParameterEnabled("Downscaling", fluxEffect.Downscaling.overrideState);
            SetParameterEnabled("BlockSize", fluxEffect.BlockSize.overrideState);
            SetParameterEnabled("DontCrunchSkybox", fluxEffect.DontCrunchSkybox.overrideState);

            SetParameterEnabled("ReprojectBaseNoise", fluxEffect.ReprojectBaseNoise.overrideState);
            SetParameterEnabled("ReprojectBaseRerollSpeed", fluxEffect.ReprojectBaseRerollSpeed.overrideState);
            SetParameterEnabled("ReprojectLengthInfluence", fluxEffect.ReprojectLengthInfluence.overrideState);

            SetParameterEnabled("KeyframeResetRate", fluxEffect.KeyframeResetRate.overrideState);
            SetParameterEnabled("MotionVectorCorruption", fluxEffect.MotionVectorCorruption.overrideState);
            SetParameterEnabled("ErrorAccumulation", fluxEffect.ErrorAccumulation.overrideState);
            SetParameterEnabled("DCTCorruption", fluxEffect.DCTCorruption.overrideState);
            SetParameterEnabled("ChromaCorruption", fluxEffect.ChromaCorruption.overrideState);

            SetParameterEnabled("MotionAmplification", fluxEffect.MotionAmplification.overrideState);
            SetParameterEnabled("MotionThreshold", fluxEffect.MotionThreshold.overrideState);
            SetParameterEnabled("CameraObjectMotionBalance", fluxEffect.CameraObjectMotionBalance.overrideState);
            SetParameterEnabled("MotionSmoothing", fluxEffect.MotionSmoothing.overrideState);

            SetParameterEnabled("TrailIntensity", fluxEffect.TrailIntensity.overrideState);
            SetParameterEnabled("TrailSmoothness", fluxEffect.TrailSmoothness.overrideState);
            SetParameterEnabled("TrailPersistence", fluxEffect.TrailPersistence.overrideState);
            SetParameterEnabled("FlowSpread", fluxEffect.FlowSpread.overrideState);

            SetParameterEnabled("GlitchTransition", fluxEffect.GlitchTransition.overrideState);
            SetParameterEnabled("FeedbackIntensity", fluxEffect.FeedbackIntensity.overrideState);
            SetParameterEnabled("MultiScaleCorruption", fluxEffect.MultiScaleCorruption.overrideState);

            SetParameterEnabled("JPEGQuality", fluxEffect.JPEGQuality.overrideState);
            SetParameterEnabled("LuminanceQuantization", fluxEffect.LuminanceQuantization.overrideState);
            SetParameterEnabled("ChrominanceQuantization", fluxEffect.ChrominanceQuantization.overrideState);
            SetParameterEnabled("ChromaSubsampling", fluxEffect.ChromaSubsampling.overrideState);

            SetParameterEnabled("RingingArtifacts", fluxEffect.RingingArtifacts.overrideState);
            SetParameterEnabled("MosquitoNoise", fluxEffect.MosquitoNoise.overrideState);
            SetParameterEnabled("EdgeSensitivity", fluxEffect.EdgeSensitivity.overrideState);

            SetParameterEnabled("NoiseTransparency", fluxEffect.NoiseTransparency.overrideState);
            SetParameterEnabled("MaxNoiseBrightness", fluxEffect.MaxNoiseBrightness.overrideState);
            SetParameterEnabled("BrightnessThreshold", fluxEffect.BrightnessThreshold.overrideState);
            SetParameterEnabled("BrightAreaMasking", fluxEffect.BrightAreaMasking.overrideState);

            SetParameterEnabled("Oversharpening", fluxEffect.Oversharpening.overrideState);
            SetParameterEnabled("VisualizeMotionVectors", fluxEffect.VisualizeMotionVectors.overrideState);
            SetParameterEnabled("DebugCompressionArtifacts", fluxEffect.DebugCompressionArtifacts.overrideState);
        }

        /// <summary>
        /// Gets the number of parameters captured (for debugging)
        /// </summary>
        private int GetParameterCount()
        {
            var fields = typeof(FluxPreset).GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            int count = 0;
            foreach (var field in fields)
            {
                if (!field.Name.Contains("presetName") && !field.Name.Contains("description") && !field.Name.Contains("category"))
                {
                    count++;
                }
            }
            return count;
        }

        /// <summary>
        /// Applies this preset to a URP Flux Effect
        /// </summary>
        public void ApplyToURP(Stylo.Flux.Universal.FluxEffect fluxEffect)
        {
            if (fluxEffect == null) return;

            // Master controls
            ApplyURPParameter(fluxEffect.EffectIntensity, "EffectIntensity", effectIntensity);
            ApplyURPParameter(fluxEffect.OnlyStenciled, "OnlyStenciled", onlyStenciled);

            // TRUE DATAMOSHING - Compression encoding
            ApplyURPParameter(fluxEffect.ColorCrunch, "ColorCrunch", colorCrunch);
            ApplyURPParameter(fluxEffect.Downscaling, "Downscaling", downscaling);
            ApplyURPParameter(fluxEffect.BlockSize, "BlockSize", (Stylo.Flux.Universal.FluxEffect._BlockSize)(int)blockSize);
            ApplyURPParameter(fluxEffect.DontCrunchSkybox, "DontCrunchSkybox", dontCrunchSkybox);

            // TRUE DATAMOSHING - Motion vector reprojection
            ApplyURPParameter(fluxEffect.ReprojectBaseNoise, "ReprojectBaseNoise", reprojectBaseNoise);
            ApplyURPParameter(fluxEffect.ReprojectBaseRerollSpeed, "ReprojectBaseRerollSpeed", reprojectBaseRerollSpeed);
            ApplyURPParameter(fluxEffect.ReprojectLengthInfluence, "ReprojectLengthInfluence", reprojectLengthInfluence);

            // TRUE DATAMOSHING - Authentic compression artifacts
            ApplyURPParameter(fluxEffect.KeyframeResetRate, "KeyframeResetRate", keyframeResetRate);
            ApplyURPParameter(fluxEffect.MotionVectorCorruption, "MotionVectorCorruption", motionVectorCorruption);
            ApplyURPParameter(fluxEffect.ErrorAccumulation, "ErrorAccumulation", errorAccumulation);
            ApplyURPParameter(fluxEffect.DCTCorruption, "DCTCorruption", dctCorruption);
            ApplyURPParameter(fluxEffect.ChromaCorruption, "ChromaCorruption", chromaCorruption);

            // CONSOLIDATED MOTION PROCESSING - New unified system
            ApplyURPParameter(fluxEffect.MotionAmplification, "MotionAmplification", motionAmplification);
            ApplyURPParameter(fluxEffect.MotionThreshold, "MotionThreshold", motionThreshold);
            ApplyURPParameter(fluxEffect.CameraObjectMotionBalance, "CameraObjectMotionBalance", cameraObjectMotionBalance);
            ApplyURPParameter(fluxEffect.MotionSmoothing, "MotionSmoothing", motionSmoothing);

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            ApplyURPParameter(fluxEffect.TrailIntensity, "TrailIntensity", trailIntensity);
            ApplyURPParameter(fluxEffect.TrailSmoothness, "TrailSmoothness", trailSmoothness);
            ApplyURPParameter(fluxEffect.TrailPersistence, "TrailPersistence", trailPersistence);
            ApplyURPParameter(fluxEffect.FlowSpread, "FlowSpread", flowSpread);

            // ENHANCED VISUAL EFFECTS - Artistic effects
            ApplyURPParameter(fluxEffect.GlitchTransition, "GlitchTransition", glitchTransition);
            ApplyURPParameter(fluxEffect.FeedbackIntensity, "FeedbackIntensity", feedbackIntensity);
            ApplyURPParameter(fluxEffect.MultiScaleCorruption, "MultiScaleCorruption", multiScaleCorruption);

            // JPEG Quality Control
            ApplyURPParameter(fluxEffect.JPEGQuality, "JPEGQuality", jpegQuality);
            ApplyURPParameter(fluxEffect.LuminanceQuantization, "LuminanceQuantization", luminanceQuantization);
            ApplyURPParameter(fluxEffect.ChrominanceQuantization, "ChrominanceQuantization", chrominanceQuantization);
            ApplyURPParameter(fluxEffect.ChromaSubsampling, "ChromaSubsampling", chromaSubsampling);

            // Compression Artifacts
            ApplyURPParameter(fluxEffect.RingingArtifacts, "RingingArtifacts", ringingArtifacts);
            ApplyURPParameter(fluxEffect.MosquitoNoise, "MosquitoNoise", mosquitoNoise);
            ApplyURPParameter(fluxEffect.EdgeSensitivity, "EdgeSensitivity", edgeSensitivity);

            // Brightness Control
            ApplyURPParameter(fluxEffect.NoiseTransparency, "NoiseTransparency", noiseTransparency);
            ApplyURPParameter(fluxEffect.MaxNoiseBrightness, "MaxNoiseBrightness", maxNoiseBrightness);
            ApplyURPParameter(fluxEffect.BrightnessThreshold, "BrightnessThreshold", brightnessThreshold);
            ApplyURPParameter(fluxEffect.BrightAreaMasking, "BrightAreaMasking", brightAreaMasking);

            // Utility controls
            ApplyURPParameter(fluxEffect.Oversharpening, "Oversharpening", oversharpening);
            ApplyURPParameter(fluxEffect.VisualizeMotionVectors, "VisualizeMotionVectors", visualizeMotionVectors);
            ApplyURPParameter(fluxEffect.DebugCompressionArtifacts, "DebugCompressionArtifacts", debugCompressionArtifacts);
        }

        /// <summary>
        /// Applies a parameter value and state to a URP VolumeParameter
        /// </summary>
        private void ApplyURPParameter<T>(UnityEngine.Rendering.VolumeParameter<T> parameter, string parameterName, T value)
        {
            if (!IsParameterEnabled(parameterName)) return;

            parameter.value = value;
            parameter.overrideState = true;
        }
#endif

        // Reflection helper methods to avoid assembly reference issues
        private float GetFloatProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (float)prop.GetValue(obj) : 0f;
        }

        private int GetIntProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (int)prop.GetValue(obj) : 0;
        }

        private bool GetBoolProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (bool)prop.GetValue(obj) : false;
        }

        private Texture2D GetTextureProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? prop.GetValue(obj) as Texture2D : null;
        }

        private void SetFloatProperty(object obj, System.Type type, string propertyName, float value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetIntProperty(object obj, System.Type type, string propertyName, int value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetBoolProperty(object obj, System.Type type, string propertyName, bool value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetTextureProperty(object obj, System.Type type, string propertyName, Texture2D value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetEnumProperty(object obj, System.Type type, string propertyName, int value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var enumValue = System.Enum.ToObject(prop.PropertyType, value);
                prop.SetValue(obj, enumValue);
            }
        }

        // New methods that respect parameter enabled states
        private void SetFloatPropertyWithState(object obj, System.Type type, string propertyName, float value)
        {
            if (!IsParameterEnabled(propertyName)) return;

            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var paramValue = prop.GetValue(obj);

                // Set the value
                var valueProp = paramValue?.GetType().GetProperty("value");
                if (valueProp != null && valueProp.CanWrite)
                {
                    valueProp.SetValue(paramValue, value);

                    // Set override state to true
                    var overrideStateProp = paramValue.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null && overrideStateProp.CanWrite)
                    {
                        overrideStateProp.SetValue(paramValue, true);
                    }
                }
                else
                {
                    // Fallback for non-VolumeParameter properties
                    prop.SetValue(obj, value);
                }
            }
        }

        private void SetIntPropertyWithState(object obj, System.Type type, string propertyName, int value)
        {
            if (!IsParameterEnabled(propertyName)) return;

            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var paramValue = prop.GetValue(obj);

                // Set the value
                var valueProp = paramValue?.GetType().GetProperty("value");
                if (valueProp != null && valueProp.CanWrite)
                {
                    valueProp.SetValue(paramValue, value);

                    // Set override state to true
                    var overrideStateProp = paramValue.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null && overrideStateProp.CanWrite)
                    {
                        overrideStateProp.SetValue(paramValue, true);
                    }
                }
                else
                {
                    // Fallback for non-VolumeParameter properties
                    prop.SetValue(obj, value);
                }
            }
        }

        private void SetBoolPropertyWithState(object obj, System.Type type, string propertyName, bool value)
        {
            if (!IsParameterEnabled(propertyName)) return;

            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var paramValue = prop.GetValue(obj);

                // Set the value
                var valueProp = paramValue?.GetType().GetProperty("value");
                if (valueProp != null && valueProp.CanWrite)
                {
                    valueProp.SetValue(paramValue, value);

                    // Set override state to true
                    var overrideStateProp = paramValue.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null && overrideStateProp.CanWrite)
                    {
                        overrideStateProp.SetValue(paramValue, true);
                    }
                }
                else
                {
                    // Fallback for non-VolumeParameter properties
                    prop.SetValue(obj, value);
                }
            }
        }

        private void SetTexturePropertyWithState(object obj, System.Type type, string propertyName, Texture2D value)
        {
            if (!IsParameterEnabled(propertyName)) return;

            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var paramValue = prop.GetValue(obj);

                // Set the value
                var valueProp = paramValue?.GetType().GetProperty("value");
                if (valueProp != null && valueProp.CanWrite)
                {
                    valueProp.SetValue(paramValue, value);

                    // Set override state to true
                    var overrideStateProp = paramValue.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null && overrideStateProp.CanWrite)
                    {
                        overrideStateProp.SetValue(paramValue, true);
                    }
                }
                else
                {
                    // Fallback for non-VolumeParameter properties
                    prop.SetValue(obj, value);
                }
            }
        }

        private void SetEnumPropertyWithState(object obj, System.Type type, string propertyName, int value)
        {
            if (!IsParameterEnabled(propertyName)) return;

            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var paramValue = prop.GetValue(obj);

                // Set the value
                var valueProp = paramValue?.GetType().GetProperty("value");
                if (valueProp != null && valueProp.CanWrite)
                {
                    var enumValue = System.Enum.ToObject(valueProp.PropertyType, value);
                    valueProp.SetValue(paramValue, enumValue);

                    // Set override state to true
                    var overrideStateProp = paramValue.GetType().GetProperty("overrideState");
                    if (overrideStateProp != null && overrideStateProp.CanWrite)
                    {
                        overrideStateProp.SetValue(paramValue, true);
                    }
                }
                else
                {
                    // Fallback for non-VolumeParameter properties
                    var enumValue = System.Enum.ToObject(prop.PropertyType, value);
                    prop.SetValue(obj, enumValue);
                }
            }
        }

#if UNITY_EDITOR
        /// <summary>
        /// Applies this preset directly to a Volume Profile asset
        /// </summary>
#if URP_INSTALLED
        public void ApplyToVolumeProfile(VolumeProfile volumeProfile)
        {
            if (volumeProfile == null) return;

            // Get or add Flux effect to the volume profile
            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var existingFlux))
            {
                // Apply to existing effect
                Undo.RecordObject(volumeProfile, "Apply Flux Preset");
                ApplyToURP(existingFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
            else
            {
                // Add new Flux effect and apply preset
                Undo.RecordObject(volumeProfile, "Add Flux Effect and Apply Preset");
                var newFlux = volumeProfile.Add<Stylo.Flux.Universal.FluxEffect>();
                ApplyToURP(newFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
        }
#endif

        /// <summary>
        /// Applies this preset directly to a Flux Effect component
        /// </summary>
        public void ApplyToFluxComponent(object fluxEffect)
        {
            if (fluxEffect == null) return;

            // Cast to UnityEngine.Object for Unity editor methods
            var unityObject = fluxEffect as UnityEngine.Object;
            if (unityObject == null) return;

            Undo.RecordObject(unityObject, "Apply Flux Preset");
            ApplyToRuntime(fluxEffect);
            EditorUtility.SetDirty(unityObject);
        }

        /// <summary>
        /// Creates a preset from a Volume Profile's Flux Effect
        /// </summary>
#if URP_INSTALLED
        public static FluxPreset CreateFromVolumeProfile(VolumeProfile volumeProfile, string presetName = "New Flux Preset")
        {
            if (volumeProfile == null) return null;

            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var fluxEffect))
            {
                var preset = CreateInstance<FluxPreset>();
                preset.presetName = presetName;
                preset.CaptureFromURP(fluxEffect);
                return preset;
            }

            return null;
        }
#endif

        /// <summary>
        /// Creates a preset from a Flux Effect component
        /// </summary>
        public static FluxPreset CreateFromFluxComponent(object fluxEffect, string presetName = "New Flux Preset")
        {
            if (fluxEffect == null) return null;

            var preset = CreateInstance<FluxPreset>();
            preset.presetName = presetName;
            preset.CaptureFromRuntime(fluxEffect);
            return preset;
        }

        /// <summary>
        /// Validates the preset for common issues and conflicts
        /// </summary>
        public string ValidatePreset()
        {
            var issues = new System.Collections.Generic.List<string>();

            // Check for zero datamosh parameters
            if (reprojectBaseNoise <= 0f && reprojectLengthInfluence <= 0f)
            {
                issues.Add("Datamosh disabled: Both Base Noise and Length Influence are zero");
            }

            // Check for motion features without reprojection
            bool hasMotionFeatures = motionAmplification > 0f || trailIntensity > 0f;
            bool hasReprojection = reprojectBaseNoise > 0f || reprojectLengthInfluence > 0f;

            if (hasMotionFeatures && !hasReprojection)
            {
                issues.Add("Motion features enabled but reprojection disabled - effects may not work");
            }

            // Performance warnings
            if (blockSize == FluxBlockSize._32x32 && downscaling < 5)
            {
                issues.Add("Performance warning: 32x32 blocks with low downscaling");
            }

            if (blockSize == FluxBlockSize._16x16 && downscaling < 3)
            {
                issues.Add("Performance warning: 16x16 blocks with low downscaling");
            }

            // Quality warnings
            if (jpegQuality < 10f && effectIntensity > 0.8f)
            {
                issues.Add("Quality warning: Very low JPEG quality with high intensity");
            }

            return issues.Count > 0 ? string.Join("\n", issues) : string.Empty;
        }

        /// <summary>
        /// Auto-fixes common preset issues
        /// </summary>
        public string AutoFixPreset()
        {
            var fixes = new System.Collections.Generic.List<string>();

            // Auto-enable reprojection if motion features are used
            bool hasMotionFeatures = motionAmplification > 0f || trailIntensity > 0f;
            bool hasReprojection = reprojectBaseNoise > 0f || reprojectLengthInfluence > 0f;

            if (hasMotionFeatures && !hasReprojection)
            {
                reprojectBaseNoise = 0.1f;
                fixes.Add("Enabled Base Noise (0.1) to support motion features");
            }

            // Auto-adjust dangerous performance combinations
            if (blockSize == FluxBlockSize._32x32 && downscaling < 5)
            {
                downscaling = 6;
                fixes.Add("Increased downscaling to 6 for better 32x32 block performance");
            }

            if (blockSize == FluxBlockSize._16x16 && downscaling < 3)
            {
                downscaling = 4;
                fixes.Add("Increased downscaling to 4 for better 16x16 block performance");
            }

            return fixes.Count > 0 ? string.Join("\n", fixes) : string.Empty;
        }
#endif
    }
}
