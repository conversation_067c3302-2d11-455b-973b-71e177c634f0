%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a37d1063797b3aa44b147bd056641d62, type: 3}
  m_Name: data_ShadersLimiter
  m_EditorClassIdentifier: 
  shouldStripShaders: 1
  maxVisibleKeywords: 10
  stripAllShadersForFirstBuild: 0
  deleteComputeShadersCache: 0
  printUsageLogs: 1
  printCompiledShadersLogs: 0
  printStrippedShaderLogs: 0
  displayOrder: 0
  visibility: 9
  strippedShaderColor: {r: 0.6901961, g: 0.3294118, b: 0.3294118, a: 1}
  notStrippedShaderColor: {r: 0.2784314, g: 0.6235294, b: 0.2784314, a: 1}
  someKeywordsStrippedColor: {r: 0.8490566, g: 0.8034721, b: 0.252314, a: 1}
  compileOnlySpecifiedColor: {r: 0.1677643, g: 0.3720466, b: 0.5471698, a: 1}
  allShaders:
  - shaderReference: {fileID: 4800000, guid: 88e9264ba3f8ff64fbbcb07a4957e5d3, type: 3}
    shaderShortName: TY_Fast Ghost
    shaderPath: Custom/TY_Fast Ghost
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _TRANSPARENT_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 03cbdd32a8958fe459d7b5edaae49ff7, type: 3}
    shaderShortName: CS_Fast Ghost  (XRay See Through)
    shaderPath: Ciconia Studio/CS_Ghost/URP/CS_Fast Ghost  (XRay See Through)
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 200, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Additive
    shaderPath: Legacy Shaders/Particles/Additive
    shaderType: 4
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9615b9983e7f9d04f809b7325debe425, type: 3}
    shaderShortName: JoostBase
    shaderPath: JoostBase
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8516d7a69675844a7a0b7095af7c46af, type: 3}
    shaderShortName: Simple Lit
    shaderPath: Universal Render Pipeline/Particles/Simple Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: _DISTORTION_ON
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_OFF
      isStripped: 0
    - keywordName: _SOFTPARTICLES_ON
      isStripped: 0
    - keywordName: _FADING_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _SURFACE_TYPE_TRANSPARENT
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _SPECULAR_COLOR
      isStripped: 0
    - keywordName: _ALPHAPREMULTIPLY_ON
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _FLIPBOOKBLENDING_ON
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0406db5a14f94604a8c57ccfbc9f3b46, type: 3}
    shaderShortName: Unlit
    shaderPath: Universal Render Pipeline/Particles/Unlit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _DISTORTION_ON
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _SOFTPARTICLES_ON
      isStripped: 0
    - keywordName: _FADING_ON
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _SURFACE_TYPE_TRANSPARENT
      isStripped: 0
    - keywordName: _ALPHAPREMULTIPLY_ON
      isStripped: 0
    - keywordName: _FLIPBOOKBLENDING_ON
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2a230514c860643f69b6a4d1871d3825, type: 3}
    shaderShortName: Stylized Lit
    shaderPath: Quibli/Stylized Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: _TEXTUREBLENDINGMODE_MULTIPLY
      isStripped: 0
    - keywordName: DR_RIM_ON
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_OFF
      isStripped: 0
    - keywordName: DR_LIGHT_ATTENUATION
      isStripped: 0
    - keywordName: DR_SPECULAR_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _CLUSTERED_RENDERING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DR_OUTLINE_ON
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _DETAILMAPBLENDINGMODE_MULTIPLY
      isStripped: 0
    - keywordName: _ALPHATEST_ON
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _TEXTUREBLENDINGMODE_ADD
      isStripped: 0
    - keywordName: _UNITYSHADOW_OCCLUSION
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _DETAILMAPBLENDINGMODE_ADD
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e6e9a19c3678ded42a3bc431ebef7dbd, type: 3}
    shaderShortName: FallbackError
    shaderPath: Hidden/Universal Render Pipeline/FallbackError
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 203, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Alpha Blended
    shaderPath: Legacy Shaders/Particles/Alpha Blended
    shaderType: 4
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 933532a4fcc9baf4fa0491de14d08ed7, type: 3}
    shaderShortName: Lit
    shaderPath: Universal Render Pipeline/Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: _NORMALMAP
      isStripped: 0
    - keywordName: _PARALLAXMAP
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_OFF
      isStripped: 0
    - keywordName: _ALPHATEST_ON
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _METALLICSPECGLOSSMAP
      isStripped: 0
    - keywordName: _SPECULAR_SETUP
      isStripped: 0
    - keywordName: _SURFACE_TYPE_TRANSPARENT
      isStripped: 0
    - keywordName: _ALPHAPREMULTIPLY_ON
      isStripped: 0
    - keywordName: _OCCLUSIONMAP
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 7e8126331d81ce94d994a853da660dd3, type: 3}
    shaderShortName: Transparent-ReceiveFullFog-Shader
    shaderPath: Shader Graphs/Transparent-ReceiveFullFog-Shader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: da07703fcc09f8d4799221050659bd55, type: 3}
    shaderShortName: FallbackError
    shaderPath: Hidden/Shader Graph/FallbackError
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6d7dbbdf9194b6745ae79aaf2f3c053c, type: 3}
    shaderShortName: UltimateLitShader
    shaderPath: OccaSoftware/UltimateLitShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _ExcludeFromJPG
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: e77111046b851ae47b57003f367ef054, type: 3}
    shaderShortName: Toon Kit 2 Shader
    shaderPath: OccaSoftware/Toon Kit 2/Toon Kit 2 Shader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 034c31a32e398ea429c30a5c48b261be, type: 3}
    shaderShortName: Joost
    shaderPath: Joost
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _CLUSTERED_RENDERING
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _ADDITIONAL_OFF
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 0318a698b312a8f47b7558532a001050, type: 3}
    shaderShortName: Stealth Vision Wireframe
    shaderPath: Shader Graphs/Stealth Vision Wireframe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: eccc0934a4c2a0b499270ffed865aefc, type: 3}
    shaderShortName: GalaxyMaterial
    shaderPath: SineVFX/GalaxyMaterials/SG/GalaxyMaterial
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _RIMENABLED_ON
      isStripped: 0
    - keywordName: _RIMNOISECAENABLED_ON
      isStripped: 0
    - keywordName: _DARKCLOUDSENABLED_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Standard
    shaderPath: Standard
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_SOFT
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 6, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: VertexLit
    shaderPath: Legacy Shaders/VertexLit
    shaderType: 4
    stripWholeShader: 0
    availableKeywords:
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8d2bb70cbf9db8d4da26e15b26e74248, type: 3}
    shaderShortName: Simple Lit
    shaderPath: Universal Render Pipeline/Simple Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 207, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Alpha Blended Premultiply
    shaderPath: Legacy Shaders/Particles/Alpha Blended Premultiply
    shaderType: 4
    stripWholeShader: 0
    availableKeywords:
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 211, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Standard Unlit
    shaderPath: Particles/Standard Unlit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: _ALPHABLEND_ON
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 39ddf95fbbeab264e9902d597e045856, type: 3}
    shaderShortName: GeometryWireframe
    shaderPath: Selected Effect --- Wireframe/GeometryWireframe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: ENABLE_GLOW
      isStripped: 0
    - keywordName: ENABLE_QUAD
      isStripped: 0
    - keywordName: ENABLE_SCANLINE
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b7839dad95683814aa64166edc107ae2, type: 3}
    shaderShortName: Lit
    shaderPath: Universal Render Pipeline/Particles/Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_OFF
      isStripped: 0
    - keywordName: _SOFTPARTICLES_ON
      isStripped: 0
    - keywordName: _FADING_ON
      isStripped: 0
    - keywordName: _DISTORTION_ON
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _SURFACE_TYPE_TRANSPARENT
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _ALPHAPREMULTIPLY_ON
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _FLIPBOOKBLENDING_ON
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 3add2469248ff6f40906004fb3647029, type: 3}
    shaderShortName: EyeShader
    shaderPath: Shader Graphs/EyeShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 3739e562a03fc0b4982d8aa66cc50865, type: 3}
    shaderShortName: ArnoldStandardSurface
    shaderPath: Shader Graphs/ArnoldStandardSurface
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: dad6b0236f3fc4141b1bc6ae44e0d9a6, type: 3}
    shaderShortName: Anim Additive
    shaderPath: Particles/Anim Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 479a3a5ebe2be224c9db1e0fd92977df, type: 3}
    shaderShortName: Dissolve_Direction_Metallic_DoubleSide
    shaderPath: Shader Graphs/URP Dissolve/Dissolve_Direction_Metallic_DoubleSide
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 10720, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Additive
    shaderPath: Mobile/Particles/Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 209, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Anim Alpha Blended
    shaderPath: Legacy Shaders/Particles/Anim Alpha Blended
    shaderType: 4
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SOFTPARTICLES_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7b4df804bfdc6ec429d0ce9540fdfdf7, type: 3}
    shaderShortName: WireframeShader
    shaderPath: OccaSoftware/WireframeShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHTS_VERTEX
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHTS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_SCREEN
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 26e3fb68c360c334a8fbff798b01ade2, type: 3}
    shaderShortName: GalaxyMaterial
    shaderPath: SineVFX/GalaxyMaterials/GalaxyMaterial
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _RIMENABLED_ON
      isStripped: 0
    - keywordName: _RIMNOISECAENABLED_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 2ba5451f84cb8d142b2fd5c48e91de82, type: 3}
    shaderShortName: EyeAdvanced_LOD0_URP
    shaderPath: EyeAdvanced/EyeAdvanced_LOD0_URP
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 10753, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Default
    shaderPath: Sprites/Default
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELSNAP_ON
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: ETC1_EXTERNAL_ALPHA
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: dcda956a6808f8c4e84b1460ad3ac6eb, type: 3}
    shaderShortName: Lit
    shaderPath: Amazing Assets/Advanced Dissolve/Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_OFF
      isStripped: 0
    - keywordName: _AD_STATE_ENABLED
      isStripped: 0
    - keywordName: _AD_CUTOUT_STANDARD_SOURCE_TWO_CUSTOM_MAPS
      isStripped: 0
    - keywordName: _AD_CUTOUT_STANDARD_SOURCE_MAPS_MAPPING_TYPE_SCREEN_SPACE
      isStripped: 0
    - keywordName: _AD_EDGE_BASE_SOURCE_CUTOUT_STANDARD
      isStripped: 0
    - keywordName: _AD_EDGE_ADDITIONAL_COLOR_CUSTOM_MAP
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _CLUSTERED_RENDERING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _ALPHATEST_ON
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 3b64b5de3bfb8a949963df728aa306c0, type: 3}
    shaderShortName: GalaxyMaterial Flipped
    shaderPath: SineVFX/GalaxyMaterials/SG/GalaxyMaterial Flipped
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _RIMENABLED_ON
      isStripped: 0
    - keywordName: _RIMNOISECAENABLED_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 21e4e8d2f60a42819bfae821d278aabf, type: 3}
    shaderShortName: World Position Slice
    shaderPath: AmplifyShaderPack/World Position Slice
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 106, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Procedural
    shaderPath: Skybox/Procedural
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _SUNDISK_NONE
      isStripped: 0
    - keywordName: _SUNDISK_SIMPLE
      isStripped: 0
    - keywordName: _SUNDISK_HIGH_QUALITY
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9103, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: EditorUIE
    shaderPath: Hidden/UIElements/EditorUIE
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UIE_FORCE_GAMMA
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 00ac8ff41ecbf314fa7c876036980861, type: 3}
    shaderShortName: VolumeNoiseShader
    shaderPath: Hidden/Buto/VolumeNoiseShader
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _TYPE_PERLIN
      isStripped: 0
    - keywordName: _GRAYSCALE_ON
      isStripped: 0
    - keywordName: _INVERT_ON
      isStripped: 0
    - keywordName: _TYPE_WORLEY
      isStripped: 0
    - keywordName: _TYPE_PERLINWORLEY
      isStripped: 0
    - keywordName: _TYPE_BILLOW
      isStripped: 0
    - keywordName: _TYPE_CURL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 04c410c9937594faa893a11dceb85f7e, type: 3}
    shaderShortName: Sampling
    shaderPath: Hidden/Universal Render Pipeline/Sampling
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0849e84e3d62649e8882e9d6f056a017, type: 3}
    shaderShortName: ScreenSpaceAmbientOcclusion
    shaderPath: Hidden/Universal Render Pipeline/ScreenSpaceAmbientOcclusion
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _INTERLEAVED_GRADIENT
      isStripped: 0
    - keywordName: _SOURCE_DEPTH_LOW
      isStripped: 0
    - keywordName: _SAMPLE_COUNT_LOW
      isStripped: 0
    - keywordName: _ORTHOGRAPHIC
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0b3b82b438158984dbccd69f0afe6ae6, type: 3}
    shaderShortName: VoxelizeShader
    shaderPath: Hidden/VoxelizeShader
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0f854b35a0cf61a429bd5dcfea30eddd, type: 3}
    shaderShortName: ScreenSpaceShadows
    shaderPath: Hidden/Universal Render Pipeline/ScreenSpaceShadows
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1121bb4e615ca3c48b214e79e841e823, type: 3}
    shaderShortName: Stop NaN
    shaderPath: Hidden/Universal Render Pipeline/Stop NaN
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: TemporalAA
    shaderPath: Hidden/Universal Render Pipeline/TemporalAA
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _USE_DRAW_PROCEDURAL
      isStripped: 0
    - keywordName: TAA_LOW_PRECISION_SOURCE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1a6ceac7f4c639b4096c21f11e4e7a56, type: 3}
    shaderShortName: TreeProxy
    shaderPath: Hidden/GPUInstancer/Nature/TreeProxy
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1edcd131364091c46a17cbff0b1de97a, type: 3}
    shaderShortName: CameraMotionBlur
    shaderPath: Hidden/Universal Render Pipeline/CameraMotionBlur
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 20cf64477973022498e44b932e804288, type: 3}
    shaderShortName: ftOverlapTest
    shaderPath: Hidden/ftOverlapTest
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2aed67ad60045d54ba3a00c91e2d2631, type: 3}
    shaderShortName: BokehDepthOfField
    shaderPath: Hidden/Universal Render Pipeline/BokehDepthOfField
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 33a2079f6a2db4c4eb2e44b33f4ddf6b, type: 3}
    shaderShortName: Empty
    shaderPath: Hidden/VFX/Empty
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 36e335017ad71d54fbb10842863188ae, type: 3}
    shaderShortName: FallbackError
    shaderPath: Hidden/Core/FallbackError
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 415d35c805efe4546aa9ca50de327d75, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/Player Footstep/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4431b1f1f743fbf4eb310a967890cbea, type: 3}
    shaderShortName: XROcclusionMesh
    shaderPath: Hidden/Universal Render Pipeline/XR/XROcclusionMesh
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 562b7ae4f629f144aa97780546fce7c6, type: 3}
    shaderShortName: Edge Adaptive Spatial Upsampling
    shaderPath: Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
    shaderShortName: HDRDebugView
    shaderPath: Hidden/Universal/HDRDebugView
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HDR_ENCODING
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5e7134d6e63e0bc47a1dd2669cedb379, type: 3}
    shaderShortName: GaussianDepthOfField
    shaderPath: Hidden/Universal Render Pipeline/GaussianDepthOfField
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _HIGH_QUALITY_SAMPLING
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5f1864addb451f54bae8c86d230f736e, type: 3}
    shaderShortName: Bloom
    shaderPath: Hidden/Universal Render Pipeline/Bloom
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _BLOOM_HQ
      isStripped: 0
    - keywordName: _USE_RGBM
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5fd9a8feb75a4b5894c241777f519d4e, type: 3}
    shaderShortName: MaterialError
    shaderPath: Hidden/Universal Render Pipeline/MaterialError
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 62449f654265cf94e81275e55a4b1823, type: 3}
    shaderShortName: JPG
    shaderPath: Hidden/Universal Render Pipeline/JPG
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: BLOCK_SIZE_4
      isStripped: 0
    - keywordName: VIZ_MOTION_VECTORS
      isStripped: 0
    - keywordName: REPROJECTION
      isStripped: 0
    - keywordName: COLOR_CRUNCH_SKYBOX
      isStripped: 0
    - keywordName: BLOCK_SIZE_8
      isStripped: 0
    - keywordName: BLOCK_SIZE_16
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 63eaba0ebfb82cc43bde059b4a8c65f6, type: 3}
    shaderShortName: SubpixelMorphologicalAntialiasing
    shaderPath: Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _SMAA_PRESET_LOW
      isStripped: 0
    - keywordName: _SMAA_PRESET_MEDIUM
      isStripped: 0
    - keywordName: _SMAA_PRESET_HIGH
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 65df88701913c224d95fc554db28381a, type: 3}
    shaderShortName: LutBuilderLdr
    shaderPath: Hidden/Universal Render Pipeline/LutBuilderLdr
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 68e6db2ebdc24f95958faec2be5558d6, type: 3}
    shaderShortName: Distance Field
    shaderPath: TextMeshPro/Distance Field
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: BEVEL_ON
      isStripped: 0
    - keywordName: UNDERLAY_ON
      isStripped: 0
    - keywordName: GLOW_ON
      isStripped: 0
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6c78a6dd468954643a87ebf0014bc305, type: 3}
    shaderShortName: Outline
    shaderPath: Hidden/ALINE/Outline
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6cda457ac28612740adb23da5d39ea92, type: 3}
    shaderShortName: LensFlareDataDriven
    shaderPath: Hidden/Universal Render Pipeline/LensFlareDataDriven
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FLARE_HAS_OCCLUSION
      isStripped: 0
    - keywordName: FLARE_OPENGL3_OR_OPENGLCORE
      isStripped: 0
    - keywordName: FLARE_INVERSE_SDF
      isStripped: 0
    - keywordName: FLARE_CIRCLE
      isStripped: 0
    - keywordName: FLARE_POLYGON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6dad35c0b62e44c26ab244ad80deee1a, type: 3}
    shaderShortName: Surface
    shaderPath: Hidden/ALINE/Surface
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7b3ede40266cd49a395def176e1bc486, type: 3}
    shaderShortName: ObjectMotionVectors
    shaderPath: Hidden/Universal Render Pipeline/ObjectMotionVectors
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7f888aff2ac86494babad1c2c5daeee2, type: 3}
    shaderShortName: FallbackLoading
    shaderPath: Hidden/Universal Render Pipeline/FallbackLoading
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8dd49cd8305cbae4f8c358c16965dbba, type: 3}
    shaderShortName: Skybox
    shaderPath: Bakery/Skybox
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 93446b5c5339d4f00b85c159e1159b7c, type: 3}
    shaderShortName: CoreBlit
    shaderPath: Hidden/Universal/CoreBlit
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DISABLE_TEXTURE2D_X_ARRAY
      isStripped: 0
    - keywordName: BLIT_DECODE_HDR
      isStripped: 0
    - keywordName: _LINEAR_TO_SRGB_CONVERSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9cccc23ecce337e4ba025ea38d50ccd1, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/SDF Test/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a0f00a9427f52384abeeac4dfa055318, type: 3}
    shaderShortName: IsolatedBlit
    shaderPath: OccaSoftware/Buto/IsolatedBlit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a15b78cf8ca26ca4fb2090293153c62c, type: 3}
    shaderShortName: PaniniProjection
    shaderPath: Hidden/Universal Render Pipeline/PaniniProjection
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _GENERIC
      isStripped: 0
    - keywordName: _UNIT_DISTANCE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a89bee29cffa951418fc1e2da94d1959, type: 3}
    shaderShortName: BlitHDROverlay
    shaderPath: Hidden/Universal/BlitHDROverlay
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DISABLE_TEXTURE2D_X_ARRAY
      isStripped: 0
    - keywordName: HDR_COLORSPACE_CONVERSION
      isStripped: 0
    - keywordName: HDR_ENCODING
      isStripped: 0
    - keywordName: HDR_COLORSPACE_CONVERSION_AND_ENCODING
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: aaa23196f850a9840b6a7f1eda6a1137, type: 3}
    shaderShortName: Merge
    shaderPath: OccaSoftware/Buto/Merge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: Grid
    shaderPath: Hidden/Behavior Designer/Grid
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
    shaderShortName: Blit
    shaderPath: Hidden/Universal Render Pipeline/Blit
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _LINEAR_TO_SRGB_CONVERSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c49e63ed1bbcb334780a3bd19dfed403, type: 3}
    shaderShortName: FinalPost
    shaderPath: Hidden/Universal Render Pipeline/FinalPost
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HDR_INPUT
      isStripped: 0
    - keywordName: HDR_COLORSPACE_CONVERSION
      isStripped: 0
    - keywordName: HDR_ENCODING
      isStripped: 0
    - keywordName: HDR_COLORSPACE_CONVERSION_AND_ENCODING
      isStripped: 0
    - keywordName: _LINEAR_TO_SRGB_CONVERSION
      isStripped: 0
    - keywordName: _DITHERING
      isStripped: 0
    - keywordName: _FILM_GRAIN
      isStripped: 0
    - keywordName: _FXAA
      isStripped: 0
    - keywordName: _POINT_SAMPLING
      isStripped: 0
    - keywordName: _RCAS
      isStripped: 0
    - keywordName: _EASU_RCAS_AND_HDR_INPUT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c56b7e0d4c7cb484e959caeeedae9bbf, type: 3}
    shaderShortName: CameraMotionVectors
    shaderPath: Hidden/Universal Render Pipeline/CameraMotionVectors
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    shaderShortName: DebugReplacement
    shaderPath: Hidden/Universal Render Pipeline/Debug/DebugReplacement
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d104b2fc1ca6445babb8e90b0758136b, type: 3}
    shaderShortName: CoreBlitColorAndDepth
    shaderPath: Hidden/Universal/CoreBlitColorAndDepth
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DISABLE_TEXTURE2D_X_ARRAY
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d52a38c4e70daf4449eaab298ba27ce6, type: 3}
    shaderShortName: ShadowsOnly
    shaderPath: Hidden/GPUInstancer/ShadowsOnly
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d5a307c014552314b9f560906d708772, type: 3}
    shaderShortName: XRMirrorView
    shaderPath: Hidden/Universal Render Pipeline/XR/XRMirrorView
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HDR_COLORSPACE_CONVERSION_AND_ENCODING
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
    shaderShortName: CopyDepth
    shaderPath: Hidden/Universal Render Pipeline/CopyDepth
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _OUTPUT_DEPTH
      isStripped: 0
    - keywordName: _DEPTH_MSAA_2
      isStripped: 0
    - keywordName: _DEPTH_MSAA_4
      isStripped: 0
    - keywordName: _DEPTH_MSAA_8
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d9ddd17a21a02ca44923f0fe12072d15, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/Warp Speed/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e7857e9d0c934dc4f83f270f8447b006, type: 3}
    shaderShortName: UberPost
    shaderPath: Hidden/Universal Render Pipeline/UberPost
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HDR_INPUT
      isStripped: 0
    - keywordName: HDR_ENCODING
      isStripped: 0
    - keywordName: _GAMMA_20
      isStripped: 0
    - keywordName: _LINEAR_TO_SRGB_CONVERSION
      isStripped: 0
    - keywordName: _DITHERING
      isStripped: 0
    - keywordName: _FILM_GRAIN
      isStripped: 0
    - keywordName: _HDR_GRADING
      isStripped: 0
    - keywordName: _TONEMAP_ACES
      isStripped: 0
    - keywordName: _TONEMAP_NEUTRAL
      isStripped: 0
    - keywordName: _BLOOM_LQ
      isStripped: 0
    - keywordName: _BLOOM_HQ
      isStripped: 0
    - keywordName: _BLOOM_LQ_DIRT
      isStripped: 0
    - keywordName: _BLOOM_HQ_DIRT
      isStripped: 0
    - keywordName: _CHROMATIC_ABERRATION
      isStripped: 0
    - keywordName: _DISTORTION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e8ee25143a34b8c4388709ea947055d1, type: 3}
    shaderShortName: Scaling Setup
    shaderPath: Hidden/Universal Render Pipeline/Scaling Setup
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _FXAA
      isStripped: 0
    - keywordName: HDR_INPUT
      isStripped: 0
    - keywordName: _GAMMA_20
      isStripped: 0
    - keywordName: _FXAA_AND_GAMMA_20
      isStripped: 0
    - keywordName: _GAMMA_20_AND_HDR_INPUT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e9155b26e1bc55942a41e518703fe304, type: 3}
    shaderShortName: StencilDeferred
    shaderPath: Hidden/Universal Render Pipeline/StencilDeferred
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _SPOT
      isStripped: 0
    - keywordName: _POINT
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ec9fec698a3456d4fb18cf8bacb7a2bc, type: 3}
    shaderShortName: LutBuilderHdr
    shaderPath: Hidden/Universal Render Pipeline/LutBuilderHdr
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _TONEMAP_ACES
      isStripped: 0
    - keywordName: _TONEMAP_NEUTRAL
      isStripped: 0
    - keywordName: HDR_COLORSPACE_CONVERSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fe393ace9b354375a9cb14cdbbc28be4, type: 3}
    shaderShortName: Distance Field
    shaderPath: TextMeshPro/Mobile/Distance Field
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: OUTLINE_ON
      isStripped: 0
    - keywordName: UNDERLAY_ON
      isStripped: 0
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: DiffuseIrradianceViewer
    shaderPath: Shader Graphs/DiffuseIrradianceViewer
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 42bf903a2a5e0d34e8e6afd948026512, type: 3}
    shaderShortName: PrototypeGridCenterMaskGM
    shaderPath: SineVFX/SG/PrototypeGridCenterMaskGM
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 4567b8e4e32cd6c4997a57ac37c86859, type: 3}
    shaderShortName: PrototypeGridQuadV2GM
    shaderPath: SineVFX/SG/PrototypeGridQuadV2GM
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: EVALUATE_SH_MIXED
      isStripped: 0
    - keywordName: EVALUATE_SH_VERTEX
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 103, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Cubemap
    shaderPath: Skybox/Cubemap
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 00487d633a303b14eb97c1d6b2474c15, type: 3}
    shaderShortName: ColorShader
    shaderPath: GuardingPearSoftware/ColorShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 01febb80028733145a4571def3b616a7, type: 3}
    shaderShortName: Line 2D Subtractive
    shaderPath: Shapes/Line 2D Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 028134b0a7a83b34a8b04f27db2a5b47, type: 3}
    shaderShortName: Line 2D ColorBurn
    shaderPath: Shapes/Line 2D ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 036dce6766baea34abf14c88d9deab90, type: 3}
    shaderShortName: Torus LinearBurn
    shaderPath: Shapes/Torus LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 03db4f08615e289409269c42fc517eb8, type: 3}
    shaderShortName: Triangle Multiplicative
    shaderPath: Shapes/Triangle Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 055193b8143fc724cb734a480601a728, type: 3}
    shaderShortName: Cuboid Opaque
    shaderPath: Shapes/Cuboid Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0667d849dbc155e49af6d8509c06bc0d, type: 3}
    shaderShortName: Sphere Screen
    shaderPath: Shapes/Sphere Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0675a12405fa94b4ba0d111f2961fa41, type: 3}
    shaderShortName: Rect LinearBurn
    shaderPath: Shapes/Rect LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0736a017ee88a8741b09e7382339ab99, type: 3}
    shaderShortName: Disc Transparent
    shaderPath: Shapes/Disc Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 08972cbecdebc6944b65c598dec5cb43, type: 3}
    shaderShortName: Line 2D Transparent
    shaderPath: Shapes/Line 2D Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 09f3f4d3814479a4d8b38cc0ae911b87, type: 3}
    shaderShortName: Polygon Opaque
    shaderPath: Shapes/Polygon Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: FragmentDownscale
    shaderPath: OccaSoftware/AutoExposure/FragmentDownscale
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0cb75e9d394f99b4fbefd7161296dd9d, type: 3}
    shaderShortName: Cone LinearBurn
    shaderPath: Shapes/Cone LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0e1f46fe23e65874aa44ae5824d18ae6, type: 3}
    shaderShortName: Torus Opaque
    shaderPath: Shapes/Torus Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0ef1742cddfd5c043b7b269da4cb5f4c, type: 3}
    shaderShortName: Line 3D Additive
    shaderPath: Shapes/Line 3D Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0fd73b16195114741812413811964a97, type: 3}
    shaderShortName: Line 3D LinearBurn
    shaderPath: Shapes/Line 3D LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 100881c0c9b76d44db14f218f468c028, type: 3}
    shaderShortName: Rect Multiplicative
    shaderPath: Shapes/Rect Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 10f1e481ec7570246b4e28c11446cd2a, type: 3}
    shaderShortName: Triangle Darken
    shaderPath: Shapes/Triangle Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 11bdfa48936e79649bff811e3fb37419, type: 3}
    shaderShortName: Rect Lighten
    shaderPath: Shapes/Rect Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 16e748ebeda646c478ae4690ff5b0a38, type: 3}
    shaderShortName: Polygon ColorBurn
    shaderPath: Shapes/Polygon ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 18069e4e96d36fd4197185e6832dfc5d, type: 3}
    shaderShortName: Regular Polygon LinearBurn
    shaderPath: Shapes/Regular Polygon LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 19725c22ed49f8346b4df0fa697018f1, type: 3}
    shaderShortName: Cuboid Multiplicative
    shaderPath: Shapes/Cuboid Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1b194be29f3ca4f4a81365d4049baeff, type: 3}
    shaderShortName: Disc Screen
    shaderPath: Shapes/Disc Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1da1f869516645b4aaae58602395f89c, type: 3}
    shaderShortName: Line 2D Screen
    shaderPath: Shapes/Line 2D Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1e31a46789a6fa34fac0fcfd7b7bb004, type: 3}
    shaderShortName: Torus ColorDodge
    shaderPath: Shapes/Torus ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 1e6c13ead1588a5499a5d4a73d710893, type: 3}
    shaderShortName: Line 3D ColorBurn
    shaderPath: Shapes/Line 3D ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2275d7dcd289b0c4294d94c35697a731, type: 3}
    shaderShortName: Occluders
    shaderPath: OccaSoftware/LSPP/Occluders
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 26b61017d9696684bb4050b288203e28, type: 3}
    shaderShortName: Disc Subtractive
    shaderPath: Shapes/Disc Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2ac8d323a50a5a44e8422f7d238d1b3e, type: 3}
    shaderShortName: Disc Opaque
    shaderPath: Shapes/Disc Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2afb410f67bf5f741b72b0b57285249f, type: 3}
    shaderShortName: Cone Multiplicative
    shaderPath: Shapes/Cone Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2b083480016d549498fe211c8a5bdea8, type: 3}
    shaderShortName: Torus Multiplicative
    shaderPath: Shapes/Torus Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: BlitScreen
    shaderPath: OccaSoftware/AutoExposure/BlitScreen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2cbaaeb3c2a5f4d47bc08f61f50c25f7, type: 3}
    shaderShortName: Polygon Multiplicative
    shaderPath: Shapes/Polygon Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2d85a0b94b73ba74893be2faf60f7c1d, type: 3}
    shaderShortName: Cone Additive
    shaderPath: Shapes/Cone Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2f827367cf5aaaa4e8fdf48f221029aa, type: 3}
    shaderShortName: Texture Darken
    shaderPath: Shapes/Texture Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 314a3863a401d644ea855008bd02029d, type: 3}
    shaderShortName: Polygon LinearBurn
    shaderPath: Shapes/Polygon LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 32c832b7dc2ba1042b86acd52432a97b, type: 3}
    shaderShortName: LightScatter
    shaderPath: OccaSoftware/LSPP/LightScatter
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 32f2a3e9fa32c8848bc7632c97ce2e5d, type: 3}
    shaderShortName: Torus Transparent
    shaderPath: Shapes/Torus Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 340c601e965ddb1479e4373b944dcc0f, type: 3}
    shaderShortName: Polyline 2D ColorBurn
    shaderPath: Shapes/Polyline 2D ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 379ba92c9994242d6844f1c0afa6cee5, type: 3}
    shaderShortName: LUT
    shaderPath: Hidden/ColorStudio/LUT
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3963a85208222924fa1806b51c8aa1f3, type: 3}
    shaderShortName: Cone ColorBurn
    shaderPath: Shapes/Cone ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3b2602ae80735bb4ca881c30fa038c8c, type: 3}
    shaderShortName: Line 2D Multiplicative
    shaderPath: Shapes/Line 2D Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3c44e10b12baae44290fb13fd0489153, type: 3}
    shaderShortName: Texture Subtractive
    shaderPath: Shapes/Texture Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3c6edd7c2118bfc4a9fbc905702dadc1, type: 3}
    shaderShortName: Texture ColorBurn
    shaderPath: Shapes/Texture ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3d2ab8f2286c36a4896c144efb4bc05f, type: 3}
    shaderShortName: Regular Polygon Lighten
    shaderPath: Shapes/Regular Polygon Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3df459dde24f0f14897f28c9c9f61bea, type: 3}
    shaderShortName: Cuboid ColorBurn
    shaderPath: Shapes/Cuboid ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 3f477ba555096a44c9ea1a525a87b910, type: 3}
    shaderShortName: Triangle Lighten
    shaderPath: Shapes/Triangle Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 43b8e30500da7724b98fca590f04ee9c, type: 3}
    shaderShortName: Cuboid LinearBurn
    shaderPath: Shapes/Cuboid LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 43d0aa8c708eb264599ed60a4ca68d6f, type: 3}
    shaderShortName: Polyline 2D Multiplicative
    shaderPath: Shapes/Polyline 2D Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4675a0ce07c107741a156f4da8d02447, type: 3}
    shaderShortName: Polyline 2D ColorDodge
    shaderPath: Shapes/Polyline 2D ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 49f475e719c57e846b41b32d4ddbf29a, type: 3}
    shaderShortName: Sphere Subtractive
    shaderPath: Shapes/Sphere Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4a268ef0dab45d147b3d0d013925c07e, type: 3}
    shaderShortName: Cone Screen
    shaderPath: Shapes/Cone Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4adbb5f3d016f8043be1a11c2e5d3db1, type: 3}
    shaderShortName: Regular Polygon ColorBurn
    shaderPath: Shapes/Regular Polygon ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 52908bebb4ec95240bedf3b094f65889, type: 3}
    shaderShortName: Triangle Transparent
    shaderPath: Shapes/Triangle Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 52da73106d2455d4bb4fcf64927797b0, type: 3}
    shaderShortName: Line 3D Opaque
    shaderPath: Shapes/Line 3D Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 537f9312ab33ad434a5484e558bbbca2, type: 3}
    shaderShortName: Font
    shaderPath: Hidden/ALINE/Font
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 53ff47070cc2f6246abd2e6690124b02, type: 3}
    shaderShortName: Polygon Lighten
    shaderPath: Shapes/Polygon Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 58dc4bea6bf8e604689e5c02a8b1a9f0, type: 3}
    shaderShortName: Polygon Darken
    shaderPath: Shapes/Polygon Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 592c53802805cde42ae787b9de4fa2c3, type: 3}
    shaderShortName: Texture Additive
    shaderPath: Shapes/Texture Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 599672801a21fc8489a5a6cc8cb87c16, type: 3}
    shaderShortName: Polygon Subtractive
    shaderPath: Shapes/Polygon Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5a7988d439dbdcf46b3e681884ccbfbe, type: 3}
    shaderShortName: Line 2D LinearBurn
    shaderPath: Shapes/Line 2D LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5c5ea38f0e52ad1419b46edad4b84fb4, type: 3}
    shaderShortName: ProceduralDesaturatedGM
    shaderPath: Skybox/ProceduralDesaturatedGM
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _SUNDISK_NONE
      isStripped: 0
    - keywordName: _SUNDISK_SIMPLE
      isStripped: 0
    - keywordName: _SUNDISK_HIGH_QUALITY
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5cc1594ffc6579944853d50de731510a, type: 3}
    shaderShortName: Cone Opaque
    shaderPath: Shapes/Cone Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5d8603af209e42247b929c40e791675e, type: 3}
    shaderShortName: SgtPlanetWater
    shaderPath: Hidden/SgtPlanetWater
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5d9217fb1c5b03246860e65ca80600f1, type: 3}
    shaderShortName: Line 3D Lighten
    shaderPath: Shapes/Line 3D Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5ddedc27b6a11064aa4ae6b6be4739e2, type: 3}
    shaderShortName: Disc ColorBurn
    shaderPath: Shapes/Disc ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: CubemapDiffuseConvolve
    shaderPath: Hidden/CubemapDiffuseConvolve
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5eb89eed9f924cc4f8e57ea53f77ecd6, type: 3}
    shaderShortName: Texture LinearBurn
    shaderPath: Shapes/Texture LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5f0032d7d9f3f4546a25cd955154c4a6, type: 3}
    shaderShortName: Sphere ColorDodge
    shaderPath: Shapes/Sphere ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 616a25aec4b286744b9fa8a7c081c531, type: 3}
    shaderShortName: Rect Transparent
    shaderPath: Shapes/Rect Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 64428ed47d3d7254db9cebf7727e64ee, type: 3}
    shaderShortName: Polyline 2D Screen
    shaderPath: Shapes/Polyline 2D Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 664104eb6db622c44ab2d35d6ca104c6, type: 3}
    shaderShortName: Quad Screen
    shaderPath: Shapes/Quad Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 66c7fb0c6ddf17b438fcbefe1bd4d642, type: 3}
    shaderShortName: Cuboid Additive
    shaderPath: Shapes/Cuboid Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6b9714d1b34b8354ca61255f290f1643, type: 3}
    shaderShortName: Cuboid Transparent
    shaderPath: Shapes/Cuboid Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6c85fef4cdd798349acf3c5bbc338c4c, type: 3}
    shaderShortName: Regular Polygon Subtractive
    shaderPath: Shapes/Regular Polygon Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6d6c5ad17528acd4bb510c7f651293b9, type: 3}
    shaderShortName: Regular Polygon Additive
    shaderPath: Shapes/Regular Polygon Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6e4c5ce31a2a1db4ba1f09bff95f9d60, type: 3}
    shaderShortName: Triangle LinearBurn
    shaderPath: Shapes/Triangle LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6ed634ab1ce977640a2de0b4682f75c6, type: 3}
    shaderShortName: Torus Additive
    shaderPath: Shapes/Torus Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 70fd05dfd6dbe624f89617428fa0175d, type: 3}
    shaderShortName: Sphere Multiplicative
    shaderPath: Shapes/Sphere Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7112f241d2b65084f8ab8bf9b77c46c1, type: 3}
    shaderShortName: Triangle ColorBurn
    shaderPath: Shapes/Triangle ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 74a701edbce58664d80fc9c2ad6f7684, type: 3}
    shaderShortName: Rect Darken
    shaderPath: Shapes/Rect Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 755647fa45052d74cbb196f9efa59aad, type: 3}
    shaderShortName: Regular Polygon Opaque
    shaderPath: Shapes/Regular Polygon Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 76417c9fb1feca24fb0f3261189e8e55, type: 3}
    shaderShortName: Polyline 2D Lighten
    shaderPath: Shapes/Polyline 2D Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 787de13cec016294e85ac59dd44d32d4, type: 3}
    shaderShortName: Triangle Screen
    shaderPath: Shapes/Triangle Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 79601a6344eda2d4b91e6b821c7b0b88, type: 3}
    shaderShortName: Line 3D Transparent
    shaderPath: Shapes/Line 3D Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 79a3a9ec2647b644bba3b12455e6da6c, type: 3}
    shaderShortName: Line 2D Lighten
    shaderPath: Shapes/Line 2D Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 79eed1a1a276fea419db9805c4bcb8ac, type: 3}
    shaderShortName: Quad Subtractive
    shaderPath: Shapes/Quad Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7ba1211725fcdd648b3d3d021a1098c9, type: 3}
    shaderShortName: Line 3D ColorDodge
    shaderPath: Shapes/Line 3D ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7cdd28a38308f4244b49164c8cd0e2ab, type: 3}
    shaderShortName: Torus Subtractive
    shaderPath: Shapes/Torus Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7e3fd71e84361484da2236f3edcf8532, type: 3}
    shaderShortName: Polyline 2D Darken
    shaderPath: Shapes/Polyline 2D Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7eb17feea64cd274499e70e2d0c950c7, type: 3}
    shaderShortName: Polygon Screen
    shaderPath: Shapes/Polygon Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 85187c2149c549c5b33f0cdb02836b17, type: 3}
    shaderShortName: Distance Field (Surface)
    shaderPath: TextMeshPro/Mobile/Distance Field (Surface)
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8b123c4a073af3f448fdac41cfcfd962, type: 3}
    shaderShortName: Quad ColorBurn
    shaderPath: Shapes/Quad ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8b20971acd9ecb3459c7615b7334d4d8, type: 3}
    shaderShortName: Texture Transparent
    shaderPath: Shapes/Texture Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8cccfaf002159ee48a501317d38f64d3, type: 3}
    shaderShortName: Triangle Subtractive
    shaderPath: Shapes/Triangle Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8e9000e5b4fab994dafde4d454416f4e, type: 3}
    shaderShortName: Quad Lighten
    shaderPath: Shapes/Quad Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8f06c6ff4ecce6f4cb5ef031a19bdde5, type: 3}
    shaderShortName: Sphere Transparent
    shaderPath: Shapes/Sphere Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 90835a0fd9985a24ca833b85d57999e4, type: 3}
    shaderShortName: Regular Polygon Multiplicative
    shaderPath: Shapes/Regular Polygon Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 987e921ca439a9d469c9d37a8e5fbae3, type: 3}
    shaderShortName: Disc Lighten
    shaderPath: Shapes/Disc Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 99a1df08fb8c6bc4b985a2eb44648277, type: 3}
    shaderShortName: Polyline 2D Transparent
    shaderPath: Shapes/Polyline 2D Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9c7ff712cd28bd446bb36a982ee022e6, type: 3}
    shaderShortName: Triangle Additive
    shaderPath: Shapes/Triangle Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9d41f0689841d3548ab54d203ee4d702, type: 3}
    shaderShortName: Rect Opaque
    shaderPath: Shapes/Rect Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9e9148b5b21f316459787c7e5fb17626, type: 3}
    shaderShortName: Texture Lighten
    shaderPath: Shapes/Texture Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a008a2b36086d4e4393a9b3c8a6d998f, type: 3}
    shaderShortName: Regular Polygon Transparent
    shaderPath: Shapes/Regular Polygon Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a02a7d8c237544f1962732b55a9aebf1, type: 3}
    shaderShortName: Distance Field Overlay
    shaderPath: TextMeshPro/Mobile/Distance Field Overlay
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a116a913e893c494f8c05d05a7c499e6, type: 3}
    shaderShortName: Cone Lighten
    shaderPath: Shapes/Cone Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a233fc2f0e228774ab69e9e6bc7862d3, type: 3}
    shaderShortName: Torus Screen
    shaderPath: Shapes/Torus Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a32ea38cf1a22714ebf13305578fa4f2, type: 3}
    shaderShortName: Texture Multiplicative
    shaderPath: Shapes/Texture Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a330e4574944c634dace66ec7db4f9f0, type: 3}
    shaderShortName: Sphere Darken
    shaderPath: Shapes/Sphere Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a4791f415644acc47816d33a13fbdc46, type: 3}
    shaderShortName: Line 3D Screen
    shaderPath: Shapes/Line 3D Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a78c37579e3a4174c878faa6911fa258, type: 3}
    shaderShortName: Sphere Lighten
    shaderPath: Shapes/Sphere Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a8f3caee3a19ffc4ea0033ea9f0f2914, type: 3}
    shaderShortName: Line 2D ColorDodge
    shaderPath: Shapes/Line 2D ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a91eee80fe5c9dc41884492a65d23d20, type: 3}
    shaderShortName: Regular Polygon Darken
    shaderPath: Shapes/Regular Polygon Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a9596574a28067e41b07f4647c2932f8, type: 3}
    shaderShortName: Quad Transparent
    shaderPath: Shapes/Quad Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ac2387db4d0065147b527c44f31cb7c9, type: 3}
    shaderShortName: Polygon Transparent
    shaderPath: Shapes/Polygon Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: aed3cc34aa69ee640b8ba17a6036ec13, type: 3}
    shaderShortName: Triangle Opaque
    shaderPath: Shapes/Triangle Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: af3432f19a045f847aa1b55d9a44ccd5, type: 3}
    shaderShortName: Sphere Opaque
    shaderPath: Shapes/Sphere Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b06e5c773d9989b4fb177d307bba650f, type: 3}
    shaderShortName: Texture Screen
    shaderPath: Shapes/Texture Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b10907ba309ee914fa5f74ab946210cf, type: 3}
    shaderShortName: Quad Multiplicative
    shaderPath: Shapes/Quad Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b1fc4f934f29fa44a9b1bc3459f9756f, type: 3}
    shaderShortName: Line 3D Darken
    shaderPath: Shapes/Line 3D Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b2f95594ea5c2b449865e021a2a2dda8, type: 3}
    shaderShortName: Quad LinearBurn
    shaderPath: Shapes/Quad LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b3e01035c553fc24c963bacd564b9e22, type: 3}
    shaderShortName: Disc LinearBurn
    shaderPath: Shapes/Disc LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b495be42b3cd1b0469b4d154db0098c5, type: 3}
    shaderShortName: Quad Darken
    shaderPath: Shapes/Quad Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b6c03ddda6c279d4d98f6575ee0c880f, type: 3}
    shaderShortName: Quad Opaque
    shaderPath: Shapes/Quad Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: FragmentCalculateExposure
    shaderPath: OccaSoftware/AutoExposure/FragmentCalculateExposure
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b8739f528b5fa404a8b7262c8ef77b19, type: 3}
    shaderShortName: Merge
    shaderPath: OccaSoftware/LSPP/Merge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: bc1ede39bf3643ee8e493720e4259791, type: 3}
    shaderShortName: Distance Field - Masking
    shaderPath: TextMeshPro/Mobile/Distance Field - Masking
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: bd2222a9abe9bd6448228306ce43119d, type: 3}
    shaderShortName: Rect Additive
    shaderPath: Shapes/Rect Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: be0fae9b6799f3e4a96d162cab737ffa, type: 3}
    shaderShortName: Triangle ColorDodge
    shaderPath: Shapes/Triangle ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c02ba34bf88b09e42a753b5dcfc7d7bb, type: 3}
    shaderShortName: Rect ColorDodge
    shaderPath: Shapes/Rect ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c117fcd0982de2c4b9ea91ea5c0c5b47, type: 3}
    shaderShortName: Sphere LinearBurn
    shaderPath: Shapes/Sphere LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c3573257a4ef475499dcf1bf802a1049, type: 3}
    shaderShortName: Cuboid Lighten
    shaderPath: Shapes/Cuboid Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c84ddfe9ae8c2ae4d97011e52f4c8c9c, type: 3}
    shaderShortName: Quad ColorDodge
    shaderPath: Shapes/Quad ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cc8394c64a9199543ac3a9c092a70906, type: 3}
    shaderShortName: Sharpen
    shaderPath: Hidden/Mirza Beig/Image Effects/Sharpen
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cc92df816a2513049824e647a2de0c43, type: 3}
    shaderShortName: Rect Subtractive
    shaderPath: Shapes/Rect Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ccaaf8572542637488622e137eca8e53, type: 3}
    shaderShortName: Cone Darken
    shaderPath: Shapes/Cone Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cd2760e21ddcfeb4f9bbc487d4e37f58, type: 3}
    shaderShortName: Cuboid Darken
    shaderPath: Shapes/Cuboid Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cd2b88f87378afc4481e0780a1657022, type: 3}
    shaderShortName: Texture ColorDodge
    shaderPath: Shapes/Texture ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cd82b6dbf3af6554e96188a487f5aeac, type: 3}
    shaderShortName: Line 2D Darken
    shaderPath: Shapes/Line 2D Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ce38766e89428954faec7a208e2e8eb3, type: 3}
    shaderShortName: Sphere Additive
    shaderPath: Shapes/Sphere Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ce9d543fec555b240b9ded3a084c95e0, type: 3}
    shaderShortName: Polyline 2D Opaque
    shaderPath: Shapes/Polyline 2D Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: FragmentApply
    shaderPath: OccaSoftware/AutoExposure/FragmentApply
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cf81c85f95fe47e1a27f6ae460cf182c, type: 3}
    shaderShortName: Sprite
    shaderPath: TextMeshPro/Sprite
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d1b4e65346964a94999ab2d02c43bb40, type: 3}
    shaderShortName: Polyline 2D Additive
    shaderPath: Shapes/Polyline 2D Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d3eeb76d6cb69404797794daff4ca2d5, type: 3}
    shaderShortName: Disc Darken
    shaderPath: Shapes/Disc Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d4e872af9af9c6b498e3fceb7ad1d426, type: 3}
    shaderShortName: Line 3D Multiplicative
    shaderPath: Shapes/Line 3D Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d7426a96783a1d043bc6e7e1cb8d09d1, type: 3}
    shaderShortName: Line 2D Additive
    shaderPath: Shapes/Line 2D Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d7d36618a6a0cad4fa7aa1b25b387d81, type: 3}
    shaderShortName: Cuboid Subtractive
    shaderPath: Shapes/Cuboid Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d8f885f2a0f71e5408a7b6ccf1bed94c, type: 3}
    shaderShortName: Blit
    shaderPath: OccaSoftware/LSPP/Blit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: db224947bb884fb47829ae497d47b12a, type: 3}
    shaderShortName: Cone Transparent
    shaderPath: Shapes/Cone Transparent
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: df9acfcfa1edab2489bc36ca98162ef0, type: 3}
    shaderShortName: Quad Additive
    shaderPath: Shapes/Quad Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e160f38daff3f834585c382fb0ee5e77, type: 3}
    shaderShortName: Polygon ColorDodge
    shaderPath: Shapes/Polygon ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e172dc322b7ed144a9506606fb1a275d, type: 3}
    shaderShortName: Polygon Additive
    shaderPath: Shapes/Polygon Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e25ac24f63e2f074788bf45a85bd9e95, type: 3}
    shaderShortName: Polyline 2D Subtractive
    shaderPath: Shapes/Polyline 2D Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e5ffb1f9e8f5ef6429f92831f9faf252, type: 3}
    shaderShortName: Rect Screen
    shaderPath: Shapes/Rect Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e6cfe8b866dec67468274d9a950411dc, type: 3}
    shaderShortName: Polyline 2D LinearBurn
    shaderPath: Shapes/Polyline 2D LinearBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: JOIN_MITER
      isStripped: 0
    - keywordName: JOIN_ROUND
      isStripped: 0
    - keywordName: JOIN_BEVEL
      isStripped: 0
    - keywordName: IS_JOIN_MESH
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 0}
    shaderShortName: FragmentBlitData
    shaderPath: OccaSoftware/AutoExposure/FragmentBlitData
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e927d8a39e9575d43bfe3a7ec6984cf3, type: 3}
    shaderShortName: Disc Multiplicative
    shaderPath: Shapes/Disc Multiplicative
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ea45b3e0ce18dff458f99faef5f3db51, type: 3}
    shaderShortName: BuiltInDownsampleBlit
    shaderPath: Matej Vanco/Raymarcher/BuiltInDownsampleBlit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ea79d94536e806545b3c4e880b4101f6, type: 3}
    shaderShortName: Disc Additive
    shaderPath: Shapes/Disc Additive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ec85c80e56492b74986385399b54da15, type: 3}
    shaderShortName: Cone ColorDodge
    shaderPath: Shapes/Cone ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ee85343ca64de14468d399128956ac17, type: 3}
    shaderShortName: Regular Polygon Screen
    shaderPath: Shapes/Regular Polygon Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f0dbbf70a3e129f4284b02ed59dfd7e0, type: 3}
    shaderShortName: Cuboid Screen
    shaderPath: Shapes/Cuboid Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f1ef4298bb91b5e48983a0a6eb59eca0, type: 3}
    shaderShortName: Cuboid ColorDodge
    shaderPath: Shapes/Cuboid ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f2a1c4a43b7d7d6438b67a915f4f4340, type: 3}
    shaderShortName: Disc ColorDodge
    shaderPath: Shapes/Disc ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: SECTOR
      isStripped: 0
    - keywordName: INNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f3276818b94bf0945896026ae59979e7, type: 3}
    shaderShortName: Texture Opaque
    shaderPath: Shapes/Texture Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f33669e058484df439cbfcf8691b8be9, type: 3}
    shaderShortName: ColorGridShader
    shaderPath: GuardingPearSoftware/ColorGridShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f398f29d5637e40f8a0032bb158b1779, type: 3}
    shaderShortName: StylizedDetail
    shaderPath: Hidden/CompoundRendererFeature/StylizedDetail
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f4a7e97bd3f8b48a0be6eeee50fecec6, type: 3}
    shaderShortName: ColorGrading
    shaderPath: Hidden/CompoundRendererFeature/ColorGrading
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f70b158aaf2f00a47be44d933b54c341, type: 3}
    shaderShortName: Line 2D Opaque
    shaderPath: Shapes/Line 2D Opaque
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f738be81326243c409deb57538f55652, type: 3}
    shaderShortName: Regular Polygon ColorDodge
    shaderPath: Shapes/Regular Polygon ColorDodge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f7ada0af4f174f0694ca6a487b8f543d, type: 3}
    shaderShortName: Distance Field (Surface)
    shaderPath: TextMeshPro/Distance Field (Surface)
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: GLOW_ON
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f9e001de8982489429271d4b376df2cd, type: 3}
    shaderShortName: Cone Subtractive
    shaderPath: Shapes/Cone Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fb1016d478ceb274bb09c46d6e95170f, type: 3}
    shaderShortName: Torus Lighten
    shaderPath: Shapes/Torus Lighten
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fb4a7266a86ba6f49861a71fe4c21805, type: 3}
    shaderShortName: Torus ColorBurn
    shaderPath: Shapes/Torus ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fc9ba1eaafedfbf45b9ed4f2b899d703, type: 3}
    shaderShortName: Rect ColorBurn
    shaderPath: Shapes/Rect ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: BORDERED
      isStripped: 0
    - keywordName: CORNER_RADIUS
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fd0809e0e0eeb75408cfb5ff76b6e085, type: 3}
    shaderShortName: Torus Darken
    shaderPath: Shapes/Torus Darken
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fd772bb2167b3b84aa3b6680ef9bdeef, type: 3}
    shaderShortName: Line 3D Subtractive
    shaderPath: Shapes/Line 3D Subtractive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: CAP_ROUND
      isStripped: 0
    - keywordName: CAP_SQUARE
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 16f283f45978cac41a7c04930ed11eae, type: 3}
    shaderShortName: Sphere ColorBurn
    shaderPath: Shapes/Sphere ColorBurn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4a7c468e9c5c4184cbfabc1384b83ba4, type: 3}
    shaderShortName: TySpriteShader
    shaderPath: Custom/TySpriteShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7d8b51f689e211e49994910d793d3c44, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_PowerUp/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 93854148289543448ac2e60169a6f538, type: 3}
    shaderShortName: UI Masked
    shaderPath: AmplifyShaderPack/UI Masked
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9dd36e8bc31afa940a6c2cc6c6994a4f, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/vfxgraph_DatamoshWithMask/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _GBUFFER_NORMALS_OCT
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e260cfa7296ee7642b167f1eb5be5023, type: 3}
    shaderShortName: Sprite-Lit-Default
    shaderPath: Universal Render Pipeline/2D/Sprite-Lit-Default
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: USE_SHAPE_LIGHT_TYPE_0
      isStripped: 0
    - keywordName: USE_SHAPE_LIGHT_TYPE_1
      isStripped: 0
    - keywordName: USE_SHAPE_LIGHT_TYPE_2
      isStripped: 0
    - keywordName: USE_SHAPE_LIGHT_TYPE_3
      isStripped: 0
    - keywordName: SKINNED_SPRITE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800002, guid: 7d8b51f689e211e49994910d793d3c44, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_PowerUp/System (1)/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7eefe3d8a8ce83843bb441f5c8c156bc, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_InkBlots_Burst/System (1)/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _GBUFFER_NORMALS_OCT
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: add4619f6fe7d25438f59ebaf3755350, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_Gunfire/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d230a125746f41740a3e7baf73591ec0, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_HitSparks/System/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800002, guid: add4619f6fe7d25438f59ebaf3755350, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_Gunfire/System (1)/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800002, guid: d230a125746f41740a3e7baf73591ec0, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_HitSparks/System (1)/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800004, guid: add4619f6fe7d25438f59ebaf3755350, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_Gunfire/Swarm/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800004, guid: d230a125746f41740a3e7baf73591ec0, type: 3}
    shaderShortName: Output Particle Quad
    shaderPath: Hidden/VFX/VFX_HitSparks/System (2)/Output Particle Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: _SCREEN_SPACE_OCCLUSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 19, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-StencilWrite
    shaderPath: Hidden/Internal-StencilWrite
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 65, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-CombineDepthNormals
    shaderPath: Hidden/Internal-CombineDepthNormals
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 66, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopy
    shaderPath: Hidden/BlitCopy
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 67, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyDepth
    shaderPath: Hidden/BlitCopyDepth
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 68, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: ConvertTexture
    shaderPath: Hidden/ConvertTexture
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 107, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyWithDepth
    shaderPath: Hidden/BlitCopyWithDepth
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 109, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitToDepth
    shaderPath: Hidden/BlitToDepth
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 110, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitToDepth_MSAA
    shaderPath: Hidden/BlitToDepth_MSAA
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 111, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemap
    shaderPath: Hidden/BlitCopyHDRTonemap
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 112, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemappedToHDRTonemap
    shaderPath: Hidden/BlitCopyHDRTonemappedToHDRTonemap
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 113, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-DebugPattern
    shaderPath: Hidden/Internal-DebugPattern
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 114, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemappedToSDR
    shaderPath: Hidden/BlitCopyHDRTonemappedToSDR
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9000, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUITextureClip
    shaderPath: Hidden/Internal-GUITextureClip
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9001, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUITextureClipText
    shaderPath: Hidden/Internal-GUITextureClipText
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9002, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUITexture
    shaderPath: Hidden/Internal-GUITexture
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9003, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUITextureBlit
    shaderPath: Hidden/Internal-GUITextureBlit
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9004, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUIRoundedRect
    shaderPath: Hidden/Internal-GUIRoundedRect
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9007, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-GUIRoundedRectWithColorPerBorder
    shaderPath: Hidden/Internal-GUIRoundedRectWithColorPerBorder
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9100, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-UIRDefault
    shaderPath: Hidden/Internal-UIRDefault
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UIE_FORCE_GAMMA
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9101, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-UIRAtlasBlitCopy
    shaderPath: Hidden/Internal-UIRAtlasBlitCopy
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 9102, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-UIRDefaultWorld
    shaderPath: Hidden/Internal-UIRDefaultWorld
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 10757, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Mask
    shaderPath: Sprites/Mask
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELSNAP_ON
      isStripped: 0
    - keywordName: ETC1_EXTERNAL_ALPHA
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 10770, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Default
    shaderPath: UI/Default
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15304, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitTexArraySlice
    shaderPath: Hidden/VR/BlitTexArraySlice
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15308, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-ODSWorldTexture
    shaderPath: Hidden/Internal-ODSWorldTexture
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15309, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Internal-CubemapToEquirect
    shaderPath: Hidden/Internal-CubemapToEquirect
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15312, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitFromTex2DToTexArraySlice
    shaderPath: Hidden/VR/BlitFromTex2DToTexArraySlice
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15313, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemapTexArraySlice
    shaderPath: Hidden/VR/BlitCopyHDRTonemapTexArraySlice
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15314, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemappedToHDRTonemapTexArraySlice
    shaderPath: Hidden/VR/BlitCopyHDRTonemappedToHDRTonemapTexArraySlice
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 15315, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: BlitCopyHDRTonemappedToSDRTexArraySlice
    shaderPath: Hidden/VR/BlitCopyHDRTonemappedToSDRTexArraySlice
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 16000, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: VideoComposite
    shaderPath: Hidden/VideoComposite
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 16001, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: VideoDecode
    shaderPath: Hidden/VideoDecode
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: ADJUST_TO_LINEARSPACE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 17000, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Compositing
    shaderPath: Hidden/Compositing
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 19011, guid: 0000000000000000f000000000000000, type: 0}
    shaderShortName: Distance Field SSD
    shaderPath: Hidden/TextCore/Distance Field SSD
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 20b7fb0966d21554195e0f03a58b1968, type: 3}
    shaderShortName: Distance Field
    shaderPath: TextMeshPro/Mobile/Distance Field
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: UNDERLAY_ON
      isStripped: 0
    - keywordName: OUTLINE_ON
      isStripped: 0
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 01c3dafa029cb284398834f43260e8c5, type: 3}
    shaderShortName: TextureVariationShader_SG
    shaderPath: GPUInstancer/CrowdAnimations/Universal Render Pipeline/TextureVariationShader_SG
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 12dc59547ea167a4ab435097dd0f9add, type: 3}
    shaderShortName: CoreCopy
    shaderPath: Hidden/CoreSRP/CoreCopy
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DISABLE_TEXTURE2D_X_ARRAY
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2e75b7d6f276c244bb513348e5a2bdab, type: 3}
    shaderShortName: ObjectsInfo
    shaderPath: HighlightersURP/ObjectsInfo
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5bf6e315093e6344ba353026e990628b, type: 3}
    shaderShortName: MeshOutlineObjects
    shaderPath: HighlightersURP/MeshOutlineObjects
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6cfdeb74fb2f7f043af3a463772b78b1, type: 3}
    shaderShortName: Blur
    shaderPath: HighlightersURP/Blur
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _GAUSSIANBLUR
      isStripped: 0
    - keywordName: _BOXBLUR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 701880fecb344ea4c9cd0db3407ab287, type: 3}
    shaderShortName: LensFlareScreenSpace
    shaderPath: Hidden/Universal Render Pipeline/LensFlareScreenSpace
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 86397d7b7e34f804a9783c3b05a29911, type: 3}
    shaderShortName: Lit
    shaderPath: GPUInstancer/Universal Render Pipeline/Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _NORMALMAP
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8a4d727fcfc589e4bb076bd5279e5201, type: 3}
    shaderShortName: Foliage
    shaderPath: GPUInstancer/Foliage
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: _BILLBOARDFACECAMPOS_ON
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_SOFT
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8c09ceafd07c22648a98a34fdfcbe766, type: 3}
    shaderShortName: ColorVariationShader
    shaderPath: GPUInstancer/ColorVariationShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_SOFT
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 998acd8de1cbc7540833e565fda1154d, type: 3}
    shaderShortName: 2DRendererSoftOcclusion
    shaderPath: GPUInstancer/Billboard/2DRendererSoftOcclusion
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _BILLBOARDFACECAMPOS_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9c70c1a35ff15f340b38ea84842358bf, type: 3}
    shaderShortName: TemporalAA
    shaderPath: Hidden/Universal Render Pipeline/TemporalAA
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TAA_LOW_PRECISION_SOURCE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a1fe6b00539636f49bae5f911c8ccc55, type: 3}
    shaderShortName: AsteroidHaze
    shaderPath: GPUInstancer/AsteroidHaze
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b018d5a5e2799dd469d0d9f2474d499c, type: 3}
    shaderShortName: Standard
    shaderPath: GPUInstancer/Standard
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: _NORMALMAP
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: _EMISSION
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_SOFT
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b1a05ad601a72334d8529afd35a4e2c8, type: 3}
    shaderShortName: Tree Soft Occlusion Bark
    shaderPath: GPUInstancer/Nature/Tree Soft Occlusion Bark
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cb6adc83a5a9d4c4496d830309162925, type: 3}
    shaderShortName: SceneDepthShader
    shaderPath: HighlightersURP/SceneDepthShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cbb3d364f8b69a141845adbf5bcfa2af, type: 3}
    shaderShortName: Overlay
    shaderPath: HighlightersURP/Overlay
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _OVERLAY
      isStripped: 0
    - keywordName: _INNERGLOW
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d11ba8e9d976597429a63a1deb722c9c, type: 3}
    shaderShortName: VertexLit
    shaderPath: GPUInstancer/VertexLit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: UNITY_HDR_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f056d8bd2a1c7e44e9729144b4c70395, type: 3}
    shaderShortName: DBufferClear
    shaderPath: Hidden/Universal Render Pipeline/DBufferClear
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f51ab73f0741c954784acaedd568fbb6, type: 3}
    shaderShortName: AlphaBlit
    shaderPath: HighlightersURP/AlphaBlit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: f8605069e61e60e4685c8e97ead17a56, type: 3}
    shaderShortName: SamplesLit_Inter
    shaderPath: Samples/SamplesLit_Inter
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _SHADOWS_SOFT
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0178fcb869bafef4690d177d31d17db8, type: 3}
    shaderShortName: Distance Field - 2 Pass
    shaderPath: TextMeshPro/Mobile/Distance Field - 2 Pass
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: OUTLINE_ON
      isStripped: 0
    - keywordName: UNDERLAY_ON
      isStripped: 0
    - keywordName: UNITY_UI_ALPHACLIP
      isStripped: 0
    - keywordName: UNITY_UI_CLIP_RECT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 593989fb7572a3e45a6d452c55685f6f, type: 3}
    shaderShortName: VFXParticle
    shaderPath: Custom/VFXParticle
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 31bcbbd690c6665419b8c17996c0ad34, type: 3}
    shaderShortName: MirageImpostor
    shaderPath: Shader Graphs/MirageImpostor
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 13c02b14c4d048fa9653293d54f6e0e1, type: 3}
    shaderShortName: Sprite-Unlit-Default
    shaderPath: Universal Render Pipeline/2D/Sprite-Unlit-Default
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: SKINNED_SPRITE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 68c389d2ae68ea0418f652fdf33a54d2, type: 3}
    shaderShortName: Ty Stylized Lit
    shaderPath: Custom/Ty Stylized Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _DETAILMAPBLENDINGMODE_MULTIPLY
      isStripped: 0
    - keywordName: DR_LIGHT_ATTENUATION
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DR_OUTLINE_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 4061145e244733a4d9e4bac2abbb417d, type: 3}
    shaderShortName: BillboardURP_GPUIPro
    shaderPath: GPUInstancerPro/Billboard/BillboardURP_GPUIPro
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: BILLBOARD_FACE_CAMERA_POS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: 78f1b5ae9238ef140869bdba4aef19db, type: 3}
    shaderShortName: Foliage_SG
    shaderPath: GPUInstancerPro/Foliage_SG
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: BILLBOARD_FACE_CAMERA_POS
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 298caa447d25205409b65b164ccffea3, type: 3}
    shaderShortName: TY_Fast Ghost
    shaderPath: GPUInstancerPro/Custom/TY_Fast Ghost
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 303b28effad14e94871a6cfad2db6615, type: 3}
    shaderShortName: InternalErrorShader
    shaderPath: Hidden/GPUInstancerPro/InternalErrorShader
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4219e08579fba87478a2e9356cedb29f, type: 3}
    shaderShortName: JoostBase
    shaderPath: GPUInstancerPro/JoostBase
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8478781d80234fa29651593b02782b9d, type: 3}
    shaderShortName: TreeProxy
    shaderPath: Hidden/GPUInstancerPro/Nature/TreeProxy
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d11b74a0422573844aac48a5aa01569b, type: 3}
    shaderShortName: Lit
    shaderPath: GPUInstancerPro/Universal Render Pipeline/Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _FORWARD_PLUS
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: _NORMALMAP
      isStripped: 0
    - keywordName: PROCEDURAL_INSTANCING_ON
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: LOD_FADE_CROSSFADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _ENVIRONMENTREFLECTIONS_OFF
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0cbb0f3b3ee194ed3b299b1e761acd68, type: 3}
    shaderShortName: Pixelate
    shaderPath: TransitionsPlus/Pixelate
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 112393ab9bbda4469ba31016dbd3d1cc, type: 3}
    shaderShortName: Circles
    shaderPath: TransitionsPlus/Circles
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 198f63f8208fa40f1a75c453df4d1617, type: 3}
    shaderShortName: Cube
    shaderPath: TransitionsPlus/Cube
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 32aac49dd16d640e5bd03592c2087499, type: 3}
    shaderShortName: Melt
    shaderPath: TransitionsPlus/Melt
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 32d0b07163a7247399dbbeae437e355e, type: 3}
    shaderShortName: Wipe
    shaderPath: TransitionsPlus/Wipe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 58ec841a88c9742cfb1605feab9e2090, type: 3}
    shaderShortName: Dissolve
    shaderPath: TransitionsPlus/Dissolve
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 591bed30147ab4327891d06acba97187, type: 3}
    shaderShortName: Tiles
    shaderPath: TransitionsPlus/Tiles
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 5aceac71f4f3a4933b7213b4d356506a, type: 3}
    shaderShortName: Mosaic
    shaderPath: TransitionsPlus/Mosaic
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6cd26f23bcfa54fbcbc486628564aa8a, type: 3}
    shaderShortName: SeaWaves
    shaderPath: TransitionsPlus/SeaWaves
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 6e55e7d77c029418a8598770301c0956, type: 3}
    shaderShortName: Ripple
    shaderPath: TransitionsPlus/Ripple
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 7d100994d096148b6b58d3ff66da62d6, type: 3}
    shaderShortName: Burn
    shaderPath: TransitionsPlus/Burn
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 87f64466c34c14d7183e665246b4a294, type: 3}
    shaderShortName: Shape
    shaderPath: TransitionsPlus/Shape
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 9060584d78b2d4f0ebc861a1247d973d, type: 3}
    shaderShortName: Splash
    shaderPath: TransitionsPlus/Splash
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a9347c18e98f9486989f38d6ddb3f426, type: 3}
    shaderShortName: TilesProgressive
    shaderPath: TransitionsPlus/TilesProgressive
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b54d44adb82bc403582ae172c4593599, type: 3}
    shaderShortName: Screen
    shaderPath: TransitionsPlus/Screen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c64a0353b520d4f27a8212b101caaf7a, type: 3}
    shaderShortName: BurnSquare
    shaderPath: TransitionsPlus/BurnSquare
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cbfaf1042f9934310838d64767bc6505, type: 3}
    shaderShortName: Spiral
    shaderPath: TransitionsPlus/Spiral
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cfebae45e5e5148a68eba2279d19c056, type: 3}
    shaderShortName: Smear
    shaderPath: TransitionsPlus/Smear
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d79a6b12bea254d79ac3779fa7d0997e, type: 3}
    shaderShortName: DoubleWipe
    shaderPath: TransitionsPlus/DoubleWipe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: debf0c6f547e54721af7e17d45c52b1c, type: 3}
    shaderShortName: CircularWipe
    shaderPath: TransitionsPlus/CircularWipe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e132b8fe9d1274e2a9871f71588335ea, type: 3}
    shaderShortName: Fade
    shaderPath: TransitionsPlus/Fade
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e597efbb6d7cd475784ebb6ce3533714, type: 3}
    shaderShortName: CrossWipe
    shaderPath: TransitionsPlus/CrossWipe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TOON
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f44522c263f364c398d753cd0cf12a17, type: 3}
    shaderShortName: Warp
    shaderPath: TransitionsPlus/Warp
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f6777a9316d9a4e2a81c6d059ed795f1, type: 3}
    shaderShortName: DoubleSlide
    shaderPath: TransitionsPlus/DoubleSlide
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: faa58f4b072124c2d86532ea3ae97291, type: 3}
    shaderShortName: Slide
    shaderPath: TransitionsPlus/Slide
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: PIXELIZATION
      isStripped: 0
    - keywordName: TEXTURE
      isStripped: 0
    - keywordName: GRADIENT_OPACITY
      isStripped: 0
    - keywordName: GRADIENT_TIME
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_RADIAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_HORIZONTAL
      isStripped: 0
    - keywordName: GRADIENT_SPATIAL_VERTICAL
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d84feccc4395b4a4092ac9dc1d1e6f04, type: 3}
    shaderShortName: Output ParticleStrip Line
    shaderPath: Hidden/VFX/Collision edge line/Trails/Output ParticleStrip Line
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800002, guid: d84feccc4395b4a4092ac9dc1d1e6f04, type: 3}
    shaderShortName: Output Particle|Unlit Quad
    shaderPath: Hidden/VFX/Collision edge line/Particles/Output Particle|Unlit Quad
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 049d9e75e07674a78a703cf1203c07dd, type: 3}
    shaderShortName: Glow
    shaderPath: HighlightPlus/Geometry/Glow
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_DITHER_BLUENOISE
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 058a572e30b2d446bade2dda32bcef0f, type: 3}
    shaderShortName: JustDepth
    shaderPath: HighlightPlus/Geometry/JustDepth
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0a6de74b6cfa9440182f8f56e4a0e4f1, type: 3}
    shaderShortName: ComposeOutline
    shaderPath: HighlightPlus/Geometry/ComposeOutline
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_MASK_CUTOUT
      isStripped: 0
    - keywordName: HP_ALL_EDGES
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 102d0b94573b248649f49d8cf8cc8fa1, type: 3}
    shaderShortName: IconFX
    shaderPath: HighlightPlus/Geometry/IconFX
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 229baf997355a43cda580dd4cf86b71e, type: 3}
    shaderShortName: SeeThroughOccluder
    shaderPath: HighlightPlus/Geometry/SeeThroughOccluder
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: ********************************, type: 3}
    shaderShortName: Mask
    shaderPath: HighlightPlus/UI/Mask
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 457d76fdfc7c4472faeb0297c0edab29, type: 3}
    shaderShortName: SeeThroughMask
    shaderPath: HighlightPlus/Geometry/SeeThroughMask
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 47198bbf0b2a44882aceef6af17a467d, type: 3}
    shaderShortName: SeeThrough
    shaderPath: HighlightPlus/Geometry/SeeThrough
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_TEXTURE_TRIPLANAR
      isStripped: 0
    - keywordName: HP_TEXTURE_SCREENSPACE
      isStripped: 0
    - keywordName: HP_TEXTURE_OBJECTSPACE
      isStripped: 0
    - keywordName: HP_SEETHROUGH_ONLY_BORDER
      isStripped: 0
    - keywordName: HP_DEPTH_OFFSET
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 54328cae8f89d442da972097ce4f23d9, type: 3}
    shaderShortName: Target
    shaderPath: HighlightPlus/Geometry/Target
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 74f3491dcf1224f0c91238381c035439, type: 3}
    shaderShortName: BlurOutline
    shaderPath: HighlightPlus/Geometry/BlurOutline
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 77643996218224478a471439e0ea5fb4, type: 3}
    shaderShortName: SolidColor
    shaderPath: HighlightPlus/Geometry/SolidColor
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_ALL_EDGES
      isStripped: 0
    - keywordName: HP_DEPTHCLIP
      isStripped: 0
    - keywordName: HP_DEPTHCLIP_INV
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 844773224daae4c31a9160897f833c5b, type: 3}
    shaderShortName: ClearStencil
    shaderPath: HighlightPlus/ClearStencil
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 84c84ee93ec484bdda371ffbdebfcc7c, type: 3}
    shaderShortName: BlurGlow
    shaderPath: HighlightPlus/Geometry/BlurGlow
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 975a91ee935da4d9c8a3e807fecd8047, type: 3}
    shaderShortName: ComposeGlow
    shaderPath: HighlightPlus/Geometry/ComposeGlow
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_MASK_CUTOUT
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cbbf740e9c8644e8492d08b1a3fd0203, type: 3}
    shaderShortName: Outline
    shaderPath: HighlightPlus/Geometry/Outline
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_OUTLINE_GRADIENT_WS
      isStripped: 0
    - keywordName: HP_OUTLINE_GRADIENT_LS
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    - keywordName: INSTANCING_ON
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: d17a98d19ada34bb7b4f86130e590159, type: 3}
    shaderShortName: Overlay
    shaderPath: HighlightPlus/Geometry/Overlay
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_TEXTURE_TRIPLANAR
      isStripped: 0
    - keywordName: HP_TEXTURE_SCREENSPACE
      isStripped: 0
    - keywordName: HP_TEXTURE_OBJECTSPACE
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e5a069457bd344391acd5af227c0ce11, type: 3}
    shaderShortName: InnerGlow
    shaderPath: HighlightPlus/Geometry/InnerGlow
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e694fa934b6db4a00b8d4b9887115332, type: 3}
    shaderShortName: Mask
    shaderPath: HighlightPlus/Geometry/Mask
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: fe0a98aa774224cf1bc4a800a586a33a, type: 3}
    shaderShortName: SeeThroughBorder
    shaderPath: HighlightPlus/Geometry/SeeThroughBorder
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: HP_DEPTH_OFFSET
      isStripped: 0
    - keywordName: HP_ALPHACLIP
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 09212a0545f190740ab07de17f02345e, type: 3}
    shaderShortName: CircleOfConfusion
    shaderPath: OccaSoftware/HazeFX/CircleOfConfusion
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2866dfcc1c4853e46ba98de9abba9ed9, type: 3}
    shaderShortName: Blur
    shaderPath: OccaSoftware/HazeFX/Blur
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 390d9813cff404243bc608ad789ef99f, type: 3}
    shaderShortName: Merge
    shaderPath: OccaSoftware/HazeFX/Merge
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 4d4fb260d11d9774abe73f8985ffb321, type: 3}
    shaderShortName: Blur
    shaderPath: OccaSoftware/RadialBlur/Blur
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8281e7a76f4e6ed4090242b7f1ae9fd9, type: 3}
    shaderShortName: HeatHaze
    shaderPath: OccaSoftware/HazeFX/HeatHaze
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: c0463e15dbee59243a4c0a6dcfbabf76, type: 3}
    shaderShortName: Premultiply
    shaderPath: OccaSoftware/HazeFX/Premultiply
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0bad311dc04a599429152609a4581703, type: 3}
    shaderShortName: FragmentDownscale
    shaderPath: OccaSoftware/AutoExposure/FragmentDownscale
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 2b9076bf31847c442a200c13376dc3d0, type: 3}
    shaderShortName: BlitScreen
    shaderPath: OccaSoftware/AutoExposure/BlitScreen
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: b766eab9a7277744ca5e4f796bd2eb8a, type: 3}
    shaderShortName: FragmentCalculateExposure
    shaderPath: OccaSoftware/AutoExposure/FragmentCalculateExposure
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: cf2b88f138ad5544598534f4f679ea25, type: 3}
    shaderShortName: FragmentApply
    shaderPath: OccaSoftware/AutoExposure/FragmentApply
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e90307ee39043c94abf6fb65971a6750, type: 3}
    shaderShortName: FragmentBlitData
    shaderPath: OccaSoftware/AutoExposure/FragmentBlitData
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 29868e73b638e48ca99a19ea58c48d90, type: 3}
    shaderShortName: BillboardWavingDoublePass
    shaderPath: Hidden/TerrainEngine/Details/UniversalPipeline/BillboardWavingDoublePass
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 620b55b8040a88d468e94abe55bed5ba, type: 3}
    shaderShortName: VrsVisualization
    shaderPath: Hidden/Core/VrsVisualization
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 8c3ee818f2efa514c889881ccb2e95a2, type: 3}
    shaderShortName: StencilDitherMaskSeed
    shaderPath: Hidden/Universal Render Pipeline/StencilDitherMaskSeed
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a10ddf3c9f9118443ae3397a080ef480, type: 3}
    shaderShortName: Flux
    shaderPath: Hidden/Universal Render Pipeline/Flux
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: BLOCK_SIZE_2
      isStripped: 0
    - keywordName: COMPRESSION_ARTIFACTS
      isStripped: 0
    - keywordName: REPROJECTION
      isStripped: 0
    - keywordName: BLOCK_SIZE_4
      isStripped: 0
    - keywordName: BLOCK_SIZE_8
      isStripped: 0
    - keywordName: BLOCK_SIZE_16
      isStripped: 0
    - keywordName: BLOCK_SIZE_32
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: e507fdfead5ca47e8b9a768b51c291a1, type: 3}
    shaderShortName: WavingDoublePass
    shaderPath: Hidden/TerrainEngine/Details/UniversalPipeline/WavingDoublePass
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: f6783ab646d374f94b199774402a5144, type: 3}
    shaderShortName: Vertexlit
    shaderPath: Hidden/TerrainEngine/Details/UniversalPipeline/Vertexlit
    shaderType: 2
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _MIXED_LIGHTING_SUBTRACTIVE
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: -6465566751694194690, guid: a3d800b099a06e0478fb790c5e79057a, type: 3}
    shaderShortName: TMP_SDF-URP Lit
    shaderPath: TextMeshPro/SRP/TMP_SDF-URP Lit
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: USE_LEGACY_LIGHTMAPS
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: PROBE_VOLUMES_L1
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: DOTS_INSTANCING_ON
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: _LIGHT_COOKIES
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 0dde7f0097aeb2541aea684ca0ad0e3c, type: 3}
    shaderShortName: AllIn13DShader
    shaderPath: AllIn13DShader/AllIn13DShader
    shaderType: 1
    stripWholeShader: 0
    availableKeywords:
    - keywordName: _MAIN_LIGHT_SHADOWS_CASCADE
      isStripped: 0
    - keywordName: _CLUSTER_LIGHT_LOOP
      isStripped: 0
    - keywordName: _LIGHT_LAYERS
      isStripped: 0
    - keywordName: LIGHTMAP_SHADOW_MIXING
      isStripped: 0
    - keywordName: SHADOWS_SHADOWMASK
      isStripped: 0
    - keywordName: LIGHTMAP_ON
      isStripped: 0
    - keywordName: DIRLIGHTMAP_COMBINED
      isStripped: 0
    - keywordName: _ALBEDOVERTEXCOLORMODE_MULTIPLY
      isStripped: 0
    - keywordName: _TEXTUREBLENDINGSOURCE_VERTEXCOLOR
      isStripped: 0
    - keywordName: _TEXTUREBLENDINGMODE_RGB
      isStripped: 0
    - keywordName: _TRIPLANARNORMALSPACE_LOCAL
      isStripped: 0
    - keywordName: _LIGHTMODEL_NONE
      isStripped: 0
    - keywordName: _SPECULARMODEL_NONE
      isStripped: 0
    - keywordName: _RIMLIGHTINGSTAGE_BEFORELIGHTING
      isStripped: 0
    - keywordName: _GREYSCALESTAGE_BEFORELIGHTING
      isStripped: 0
    - keywordName: _REFLECTIONS_NONE
      isStripped: 0
    - keywordName: _MATCAPBLENDMODE_MULTIPLY
      isStripped: 0
    - keywordName: _HEIGHTGRADIENTPOSITIONSPACE_LOCAL
      isStripped: 0
    - keywordName: _COLORRAMPLIGHTINGSTAGE_BEFORELIGHTING
      isStripped: 0
    - keywordName: _SHADINGMODEL_BASIC
      isStripped: 0
    - keywordName: _OUTLINETYPE_NONE
      isStripped: 0
    - keywordName: FOG_LINEAR
      isStripped: 0
    - keywordName: _ALBEDOVERTEXCOLORMODE_REPLACE
      isStripped: 0
    - keywordName: _TEXTUREBLENDINGSOURCE_TEXTURE
      isStripped: 0
    - keywordName: _SCREEN_SPACE_UV_ON
      isStripped: 0
    - keywordName: _TRIPLANARNORMALSPACE_WORLD
      isStripped: 0
    - keywordName: _LIGHTMODEL_CLASSIC
      isStripped: 0
    - keywordName: _SPECULARMODEL_CLASSIC
      isStripped: 0
    - keywordName: _CAST_SHADOWS_ON
      isStripped: 0
    - keywordName: _RIM_LIGHTING_ON
      isStripped: 0
    - keywordName: _RIMLIGHTINGSTAGE_BEFORELIGHTINGLAST
      isStripped: 0
    - keywordName: _REFLECTIONS_CLASSIC
      isStripped: 0
    - keywordName: _FADE_BURN_ON
      isStripped: 0
    - keywordName: _SHADINGMODEL_PBR
      isStripped: 0
    - keywordName: _CONTRAST_BRIGHTNESS_ON
      isStripped: 0
    - keywordName: _HUE_SHIFT_ON
      isStripped: 0
    - keywordName: _SCROLL_TEXTURE_ON
      isStripped: 0
    - keywordName: _WAVE_UV_ON
      isStripped: 0
    - keywordName: _UV_DISTORTION_ON
      isStripped: 0
    - keywordName: _MAIN_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _ADDITIONAL_LIGHT_SHADOWS
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BLENDING
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_BOX_PROJECTION
      isStripped: 0
    - keywordName: _REFLECTION_PROBE_ATLAS
      isStripped: 0
    - keywordName: POINT
      isStripped: 0
    - keywordName: DIRECTIONAL
      isStripped: 0
    - keywordName: SPOT
      isStripped: 0
    - keywordName: POINT_COOKIE
      isStripped: 0
    - keywordName: DIRECTIONAL_COOKIE
      isStripped: 0
    - keywordName: SHADOWS_DEPTH
      isStripped: 0
    - keywordName: SHADOWS_SOFT
      isStripped: 0
    - keywordName: SHADOWS_SCREEN
      isStripped: 0
    - keywordName: SHADOWS_CUBE
      isStripped: 0
    - keywordName: _CASTING_PUNCTUAL_LIGHT_SHADOW
      isStripped: 0
    - keywordName: LIGHTPROBE_SH
      isStripped: 0
    - keywordName: DYNAMICLIGHTMAP_ON
      isStripped: 0
    - keywordName: VERTEXLIGHT_ON
      isStripped: 0
    - keywordName: _RECEIVE_SHADOWS_ON
      isStripped: 0
    - keywordName: _USE_WIND_VERTICAL_MASK
      isStripped: 0
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: 65848f6b24af64f40a188630f7de1b8e, type: 3}
    shaderShortName: Wireframe
    shaderPath: AutoLOD/Wireframe
    shaderType: 1
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  - shaderReference: {fileID: 4800000, guid: a14176a5b941edd47a10e786ce634d9e, type: 3}
    shaderShortName: OitRender
    shaderPath: Hidden/OitRender
    shaderType: 2
    stripWholeShader: 0
    availableKeywords: []
    compileOnlySpecifiedVariants: 0
    variantsCollections: []
    availableVariants: []
    variantsOrderData: []
    forcePrintLogsOnCompile: 0
    forcePrintLogsOnStrip: 0
  globallyStrippedKeywords: []
  uniqueKeywords:
  - INSTANCING_ON
  - FOG_LINEAR
  - SOFTPARTICLES_ON
  - _MAIN_LIGHT_SHADOWS
  - _MAIN_LIGHT_SHADOWS_CASCADE
  - _ADDITIONAL_LIGHT_SHADOWS
  - _MIXED_LIGHTING_SUBTRACTIVE
  - _SHADOWS_SOFT
  - LOD_FADE_CROSSFADE
  - _FORWARD_PLUS
  - _DISTORTION_ON
  - PROCEDURAL_INSTANCING_ON
  - LIGHTMAP_SHADOW_MIXING
  - EVALUATE_SH_MIXED
  - EVALUATE_SH_VERTEX
  - _TEXTUREBLENDINGMODE_MULTIPLY
  - DR_RIM_ON
  - _RECEIVE_SHADOWS_OFF
  - DR_LIGHT_ATTENUATION
  - DR_SPECULAR_ON
  - DYNAMICLIGHTMAP_ON
  - DIRLIGHTMAP_COMBINED
  - _CLUSTERED_RENDERING
  - SHADOWS_SHADOWMASK
  - DR_OUTLINE_ON
  - _CASTING_PUNCTUAL_LIGHT_SHADOW
  - _LIGHT_LAYERS
  - _NORMALMAP
  - _PARALLAXMAP
  - _REFLECTION_PROBE_BLENDING
  - _REFLECTION_PROBE_BOX_PROJECTION
  - _LIGHT_COOKIES
  - _ADDITIONAL_OFF
  - LIGHTMAP_ON
  - _RIMENABLED_ON
  - _RIMNOISECAENABLED_ON
  - _DARKCLOUDSENABLED_ON
  - DIRECTIONAL
  - LIGHTPROBE_SH
  - SHADOWS_SCREEN
  - VERTEXLIGHT_ON
  - POINT
  - SPOT
  - POINT_COOKIE
  - DIRECTIONAL_COOKIE
  - SHADOWS_DEPTH
  - SHADOWS_SOFT
  - SHADOWS_CUBE
  - UNITY_HDR_ON
  - _ALPHABLEND_ON
  - ENABLE_GLOW
  - ENABLE_QUAD
  - ENABLE_SCANLINE
  - _SOFTPARTICLES_ON
  - _FADING_ON
  - _ALPHATEST_ON
  - _ADDITIONAL_LIGHTS_VERTEX
  - _ADDITIONAL_LIGHTS
  - _MAIN_LIGHT_SHADOWS_SCREEN
  - PIXELSNAP_ON
  - ETC1_EXTERNAL_ALPHA
  - _AD_STATE_ENABLED
  - _AD_CUTOUT_STANDARD_SOURCE_TWO_CUSTOM_MAPS
  - _AD_CUTOUT_STANDARD_SOURCE_MAPS_MAPPING_TYPE_SCREEN_SPACE
  - _AD_EDGE_BASE_SOURCE_CUTOUT_STANDARD
  - _AD_EDGE_ADDITIONAL_COLOR_CUSTOM_MAP
  - _SUNDISK_NONE
  - _SUNDISK_SIMPLE
  - _SUNDISK_HIGH_QUALITY
  - _TYPE_PERLIN
  - _GRAYSCALE_ON
  - _INVERT_ON
  - _TYPE_WORLEY
  - _TYPE_PERLINWORLEY
  - _TYPE_BILLOW
  - _TYPE_CURL
  - _USE_DRAW_PROCEDURAL
  - _HIGH_QUALITY_SAMPLING
  - _BLOOM_HQ
  - _USE_RGBM
  - BLOCK_SIZE_4
  - VIZ_MOTION_VECTORS
  - REPROJECTION
  - COLOR_CRUNCH_SKYBOX
  - BLOCK_SIZE_8
  - BLOCK_SIZE_16
  - _SMAA_PRESET_LOW
  - _SMAA_PRESET_MEDIUM
  - _SMAA_PRESET_HIGH
  - BEVEL_ON
  - UNDERLAY_ON
  - GLOW_ON
  - UNITY_UI_ALPHACLIP
  - UNITY_UI_CLIP_RECT
  - FLARE_HAS_OCCLUSION
  - FLARE_OPENGL3_OR_OPENGLCORE
  - DISABLE_TEXTURE2D_X_ARRAY
  - BLIT_DECODE_HDR
  - _GENERIC
  - _UNIT_DISTANCE
  - _OUTPUT_DEPTH
  - _DEPTH_MSAA_2
  - _DEPTH_MSAA_4
  - _DEPTH_MSAA_8
  - _SPOT
  - _POINT
  - _TONEMAP_ACES
  - _TONEMAP_NEUTRAL
  - OUTLINE_ON
  - CAP_ROUND
  - CAP_SQUARE
  - BORDERED
  - CORNER_RADIUS
  - SECTOR
  - INNER_RADIUS
  - JOIN_MITER
  - JOIN_ROUND
  - JOIN_BEVEL
  - IS_JOIN_MESH
  - USE_SHAPE_LIGHT_TYPE_0
  - USE_SHAPE_LIGHT_TYPE_1
  - USE_SHAPE_LIGHT_TYPE_2
  - USE_SHAPE_LIGHT_TYPE_3
  - ADJUST_TO_LINEARSPACE
  - _EMISSION
  - _SURFACE_TYPE_TRANSPARENT
  - _ALPHAPREMULTIPLY_ON
  - _INTERLEAVED_GRADIENT
  - _SOURCE_DEPTH_LOW
  - _SAMPLE_COUNT_LOW
  - _ORTHOGRAPHIC
  - TAA_LOW_PRECISION_SOURCE
  - _DETAILMAPBLENDINGMODE_MULTIPLY
  - _SCREEN_SPACE_OCCLUSION
  - HDR_ENCODING
  - FLARE_INVERSE_SDF
  - FLARE_CIRCLE
  - FLARE_POLYGON
  - _SPECULAR_COLOR
  - _METALLICSPECGLOSSMAP
  - _SPECULAR_SETUP
  - _OCCLUSIONMAP
  - _LINEAR_TO_SRGB_CONVERSION
  - HDR_COLORSPACE_CONVERSION
  - HDR_COLORSPACE_CONVERSION_AND_ENCODING
  - HDR_INPUT
  - _DITHERING
  - _FILM_GRAIN
  - _FXAA
  - _POINT_SAMPLING
  - _RCAS
  - _EASU_RCAS_AND_HDR_INPUT
  - _GAMMA_20
  - _HDR_GRADING
  - _BLOOM_LQ
  - _BLOOM_LQ_DIRT
  - _BLOOM_HQ_DIRT
  - _CHROMATIC_ABERRATION
  - _DISTORTION
  - _FXAA_AND_GAMMA_20
  - _GBUFFER_NORMALS_OCT
  - _FLIPBOOKBLENDING_ON
  - _TEXTUREBLENDINGMODE_ADD
  - _UNITYSHADOW_OCCLUSION
  - UIE_FORCE_GAMMA
  - _GAUSSIANBLUR
  - _BOXBLUR
  - _BILLBOARDFACECAMPOS_ON
  - _OVERLAY
  - _INNERGLOW
  - _GAMMA_20_AND_HDR_INPUT
  - SKINNED_SPRITE
  - _ExcludeFromJPG
  - PROBE_VOLUMES_L1
  - _TRANSPARENT_ON
  - BILLBOARD_FACE_CAMERA_POS
  - _ENVIRONMENTREFLECTIONS_OFF
  - _DETAILMAPBLENDINGMODE_ADD
  - TEXTURE
  - GRADIENT_OPACITY
  - GRADIENT_TIME
  - GRADIENT_SPATIAL_RADIAL
  - GRADIENT_SPATIAL_HORIZONTAL
  - GRADIENT_SPATIAL_VERTICAL
  - PIXELIZATION
  - TOON
  - HP_DITHER_BLUENOISE
  - HP_ALPHACLIP
  - HP_MASK_CUTOUT
  - HP_ALL_EDGES
  - HP_TEXTURE_TRIPLANAR
  - HP_TEXTURE_SCREENSPACE
  - HP_TEXTURE_OBJECTSPACE
  - HP_SEETHROUGH_ONLY_BORDER
  - HP_DEPTH_OFFSET
  - HP_DEPTHCLIP
  - HP_DEPTHCLIP_INV
  - HP_OUTLINE_GRADIENT_WS
  - HP_OUTLINE_GRADIENT_LS
  - _CLUSTER_LIGHT_LOOP
  - USE_LEGACY_LIGHTMAPS
  - DOTS_INSTANCING_ON
  - _REFLECTION_PROBE_ATLAS
  - BLOCK_SIZE_2
  - COMPRESSION_ARTIFACTS
  - BLOCK_SIZE_32
  - _ALBEDOVERTEXCOLORMODE_MULTIPLY
  - _TEXTUREBLENDINGSOURCE_VERTEXCOLOR
  - _TEXTUREBLENDINGMODE_RGB
  - _TRIPLANARNORMALSPACE_LOCAL
  - _LIGHTMODEL_NONE
  - _SPECULARMODEL_NONE
  - _RIMLIGHTINGSTAGE_BEFORELIGHTING
  - _GREYSCALESTAGE_BEFORELIGHTING
  - _REFLECTIONS_NONE
  - _MATCAPBLENDMODE_MULTIPLY
  - _HEIGHTGRADIENTPOSITIONSPACE_LOCAL
  - _COLORRAMPLIGHTINGSTAGE_BEFORELIGHTING
  - _SHADINGMODEL_BASIC
  - _OUTLINETYPE_NONE
  - _ALBEDOVERTEXCOLORMODE_REPLACE
  - _TEXTUREBLENDINGSOURCE_TEXTURE
  - _SCREEN_SPACE_UV_ON
  - _TRIPLANARNORMALSPACE_WORLD
  - _LIGHTMODEL_CLASSIC
  - _SPECULARMODEL_CLASSIC
  - _CAST_SHADOWS_ON
  - _RIM_LIGHTING_ON
  - _RIMLIGHTINGSTAGE_BEFORELIGHTINGLAST
  - _REFLECTIONS_CLASSIC
  - _FADE_BURN_ON
  - _SHADINGMODEL_PBR
  - _CONTRAST_BRIGHTNESS_ON
  - _HUE_SHIFT_ON
  - _SCROLL_TEXTURE_ON
  - _WAVE_UV_ON
  - _UV_DISTORTION_ON
  - _RECEIVE_SHADOWS_ON
  - _USE_WIND_VERTICAL_MASK
  keywordDescriptions:
  - {fileID: 11400000, guid: b59ac19236bff4a4ba10a2e3791fdaeb, type: 2}
  - {fileID: 11400000, guid: fd5f7604f2a67544caee7044d4fe22e7, type: 2}
  - {fileID: 11400000, guid: 30fafe76ee9a1f24fa29c98b0a2a197f, type: 2}
  - {fileID: 11400000, guid: f0a75636390038d4b889200e8ccd0118, type: 2}
  - {fileID: 11400000, guid: 696eb0265d9c53144b77f5a66cf8fc50, type: 2}
  - {fileID: 11400000, guid: ef19f125bd2f6f540aeeffccc68e2948, type: 2}
  - {fileID: 11400000, guid: 377a6acf5a28b0442987dbfbd40438d2, type: 2}
  - {fileID: 11400000, guid: 3029bb6a42ec0944894224b436934b9c, type: 2}
  - {fileID: 11400000, guid: f6ca4792267c0dd4f9c153838266082a, type: 2}
  - {fileID: 11400000, guid: 33478d1bc71da7b44b8d9629ccb04a58, type: 2}
  - {fileID: 11400000, guid: ae8dc4f51a2f4dc4eaa70e7855cafd10, type: 2}
  - {fileID: 11400000, guid: 19a397a38b608544cadad59cc1afc453, type: 2}
  - {fileID: 11400000, guid: 33b45a0e64fc97a4c8af9563b516f909, type: 2}
  - {fileID: 11400000, guid: d1fdeef4952ec7b448c1675e3823bfad, type: 2}
  - {fileID: 11400000, guid: 77b48c761945325408e96dcf1a29ea87, type: 2}
  - {fileID: 11400000, guid: 7f193631a8e85e749ab45c8194aa5f9b, type: 2}
  - {fileID: 11400000, guid: ce1b5a7c408045d47b91a2193f8db7bd, type: 2}
  - {fileID: 11400000, guid: dff81ed560cfdb44a959dd6fc93cfc39, type: 2}
  - {fileID: 11400000, guid: a71f8a9ad2ae28c4390ad69748c5f056, type: 2}
  - {fileID: 11400000, guid: 842301adc073b3f4b9190af4fcd174cf, type: 2}
  - {fileID: 11400000, guid: b64d8ab86eaa7b4489aa563eee3f9d63, type: 2}
  - {fileID: 11400000, guid: a79f5dfdb2d923e4299bf314bf918ed2, type: 2}
  - {fileID: 11400000, guid: b4df83017bb6e5b4c9d5749a8d09e1b0, type: 2}
  - {fileID: 11400000, guid: 183b51c4242cc5943be9db553037d140, type: 2}
  - {fileID: 11400000, guid: a5e4549828c9a59408a89077d2ec00a6, type: 2}
  - {fileID: 11400000, guid: 79725ed1de7f68849976c596043c9b62, type: 2}
  - {fileID: 11400000, guid: f3e5327e6f74a8b4a9322e250aedf46e, type: 2}
  - {fileID: 11400000, guid: 2af2761fe34fa384caa7734049b4e70b, type: 2}
  - {fileID: 11400000, guid: 3dc58c75f8dcd2640918c10cfb37a45f, type: 2}
  - {fileID: 11400000, guid: 1fa674daf0a9c9e4eaf362cce20d0356, type: 2}
  - {fileID: 11400000, guid: 9c8fd39a2ff1ba64ab750ea5769bf999, type: 2}
  - {fileID: 11400000, guid: 33a8f027eb8bf6f449d2bf557e17ada5, type: 2}
  - {fileID: 11400000, guid: 71c78c00be53cc6429ea8b11a21011d8, type: 2}
  - {fileID: 11400000, guid: 6a64de92e576f174a8d2ec3954873cc5, type: 2}
  - {fileID: 11400000, guid: ba13238079f3055498710f28e04ef1bf, type: 2}
  - {fileID: 11400000, guid: 593cecba2535a8f4db04ee75d40c215b, type: 2}
  - {fileID: 11400000, guid: 4258f8996fef17742bbcc3eb5957ce88, type: 2}
  - {fileID: 11400000, guid: a192eafc45bd4b5438350d3e2a14d7ff, type: 2}
